Hello, everybody. Welcome to the OpenUSD Insiders livestream. I'm very honored to have you join us today. We have a very special episode with some amazing partners.
We're going to dive into that topic in just a second, but we're going to be covering building an authentic AI powered digital twins.
We would love to invite your questions, your comments. Let us know where you're watching from. If you've got any interesting projects related to this, we'd love to hear about it also.
So go ahead and introduce yourself in the chat right now. We're going to hit the questions. There are probably a few different points during this hour.
So if we don't get you right when you post, we're trying to keep track of them. We'll hit them by the end, most likely.
OK, so thanks for joining us. So right now, I'd like to also very attention to this great learning path we've got here. Brand new new digital twins for physical AI.
We just came back from SeaGraph. A lot of excitement on this on the subject, including at labs and sessions. This QR code will bring you to that brand new learning path.
For those of you who've been watching for a while, a part of the community, you all know that these learning paths are a fantastic free resource, self-paced courses, basically, on a various number of topics, including OpenUSD and robotics.
And now we have digital twins for physical AI. So use that QR code to find it. If you need the link, we'll also put it in the chat, but that is a super helpful resource for all the developers out there.
Here's another great resource, developing and deploying your own omniverse kit apps. This QR code will also bring you the latest information there.
Another great resource for leveraging the community on deploying your own kit app is our Discord server, where we have developers talking about this very topic pretty much on a daily basis.
So engage with other community members there. We'll post a link in the chat.
Amazon Devices and Services achieves major step towards zero-touch manufacturing with NVIDIA. This is also an amazing story that just came out. This QR code will bring you right to there.
If you have any questions on this, feel free to pop on over to the live streams channel. We have our Discord server. Feel free to tag me, and I'll get you someone who can help in the chat on this.
Developers build fast and reliable robot simulations with NVIDIA omniverse libraries.
As you can see, we're all about QR codes today. So here's another QR code for you. That'll bring you right to the link.
And obviously, if you're watching this live, you will get these links in the live chat also. No need to bring your phone up to the screen unless you want to.
You're free to, of course. But feel free to check out that resource. We'll be happy to also help you with that.
And how to instantly render real-world scenes in interactive simulation. Another great article.
These articles are actually a great resource for people because they leverage not only our NVIDIA developers, but also members of partner teams, members of the community.
Really great use cases and workflows from start to finish. So highly encourage you to check out each of these, but this is how to render real-world scenes in interactive simulation.
NVIDIA opens up portals to world of robotics with new omniverse libraries, Cosmos physical AI models and AI computing infrastructure.
Cosmos is an amazing world foundation model we've been talking about a lot lately. This will help give you more insight into how to leverage that to the fullest.
Again, at Seagraph, we just wrapped up a couple weeks ago. It was all about physical AI, robotics, Cosmos.
It really showed a nice transformation in what's happening in the graphics industry.
So feel free to leverage that QR code to get right to that article.
And another exciting thing that also was debuted at Seagraph was our open USD certification.
For those who attended Seagraph in person, you were able to actually take the certification exam for free.
Otherwise, there's a small cost attached if you are taking it online and here's the QR code for that.
I highly recommend anyone out there who's been floating around to open USD for a while, go ahead and challenge yourself to take the certification.
I do think it's a great idea to take that learning path open USD before you take the certification exam.
You're going to get all the information you need by following along with those learning path self-paced courses.
And then you'll have a great certificate at the end so you can show the world that you are open USD certified, making yourself even more knowledgeable and more of a thought leader whenever you make your posts.
These are the links that will give you the various resources we have in the community.
We have our calendar, which is the first link there, which will bring you all the upcoming live streams, office hours, study groups, key events that are happening, including GTC DC, which is coming up.
I can't believe there's not a slide on that. I'm actually going to bring it to the web page and I'll show it to you later. But GTC DC is coming up.
I'll be there. We always post those events in our event calendar. Very easy to subscribe and always find out about the latest.
Our forums are a great place to post your questions. If you have any blockers, absolutely leverage the forums for pretty much everything.
Some exceptions include Isaac Lab, where we prefer you to go to the GitHub page for Isaac Lab, but Isaac Sim has its own section on the forums.
Our Discord server, which I mentioned earlier, that's an amazing resource. We have thousands of developers that are on there posting regularly about their projects and assisting each other on a day-to-day basis.
Lots of great channels. It's well organized to find what you need quickly. Just use the search bar on the top right. Type anywhere you want. You'll get there in a flash.
Finally, that last link is our community page, where you can look up things, including our ambassadors and other resources we have in the community.
That is a lot of information for you. I hope everyone got good notes there. Now I would like to bring in my great colleague, Ganesh here.
Ganesh, how are you doing? It is an honor to have you joining us here today. This is a very special episode we've got today.
I'm building Agentec AI-powered digital twins, isn't it?
Exactly. Thank you, Edmar, for having me again. I'm excited to share the next 15 minutes with Sight Machine, Kinetic Vision and Microsoft on how we are actually taking digital twin directly into operational shop floor experiences.
And then how are we actually marrying that with Agentec AI? I'm really excited to be here. Thank you for inviting me.
Of course. Let's bring in these very special guests. This is super exciting. We have Jeremy here. Hey, Jeremy.
Hey, everybody. I'm from Kinetic Vision. It's so great to see you. Tell us a little about your background in Kinetic Vision.
Yeah, I'm hailing here from Cincinnati, Ohio, a great hub of US manufacturing. I'm the CEO of Kinetic Vision.
We develop and integrate digital twins for manufacturing and supply chain. We're an NVIDIA partner. We're a Microsoft partner and we're a Sight Machine partner.
So really excited to be here. Talk with everybody today. And Ganesh and Edmar, thank you for inviting me.
Of course. I mean, I love to see when partners are working so closely together. You see some amazing things happen with these billion of minds that are working together.
Speaking of brilliant minds, let me bring in my partner, Sasha here also for NVIDIAside. Hey, Sasha, how are you doing?
Good morning and good afternoon or good evening, depending on where you are. Hi, everyone. I'm Sasha Bichon.
I work with Ganesh and Jeremy and the rest of the team and trying to bring together all of the good work we've done here in terms of digitization, if you will, and how we apply that into factories of the future.
Amazing. I'm so glad to have you here. Sasha is an amazing person on our team, so he's going to have great context as well as Ganesh throughout this episode.
Here's a good friend also, a veteran of the live streams. Hey, Drew. Hey, Edmar, how are you doing? How you been, man? It's great to see you again.
Doing good, doing good. Yeah, you too, man. So you have a special role. Why don't you tell me what you do over there at Microsoft?
Well, at Microsoft, basically I work with a group called Industrial Solutions Engineering. So basically we go into customers and innovate with them, with partners like NVIDIA, you guys.
And we also have, I also work with a team that's actually in Houston, Texas. They have an actual facility where we build up hardware, like, for reels, and then, like, with PLCs.
And we then test, like, getting data into the cloud and then doing stuff with it. And yeah, I've been working with Sasha, Ganesh, and you all, Edmar, like, for a while.
Especially Ganesh, causing chaos wherever we go. So it's great to be here and good to see you.
That's awesome. Well, thank you. I mentioned in my promo earlier I've had a lot of fun in rehearsals with this whole team of people. Let's bring in the last two people here. We've got Sheru. Hey, Sheru, how are you doing?
Hello, everyone. Nice to meet you.
Why don't you tell everybody about your role and your background at Site Machine?
Absolutely. Hi, I'm Charu Kalluri. I'm a director of product marketing at Site Machine. Site Machine is an industrial AI platform focused on delivering manufacturing outcomes at scale.
Over the years, we've been working very closely with Microsoft and NVIDIA and our kinetic vision. So just really excited to, again, be here with everybody to talk about the compelling value of all of these technologies put together.
Awesome. We're very excited to have you here. People are going to really love it. We're going to show off in a few minutes.
Speaking of showing off, we've got Sidhir here. How are you doing, Sidhir?
Hey, I'm doing great. Nice to see you all. Super excited to be here. I run engineering at Site Machine and we work a lot with Microsoft and NVIDIA and all of the folks on the squad and others to bring together Site Machine and Microsoft and NVIDIA, integrating omniverse and bringing true value to our management.
That's awesome. Well, listen, I can't think of a better crew to tackle this topic today. So I think everyone should buckle up. I already see a ton of comments coming in.
That's fantastic. We're going to get to some of those in a second, but why don't we set the stage here, Ganesh and Sashi, for what we're going to really dive into here.
Sounds good. So what are we going to see in the next 15 minutes or so? We're going to actually talk about how digital twins are actually shaping and driving the next wave of AI, which is deeply rooted into physical AI.
And then we're going to actually talk about how Site Machine as an ISV has taken the technology from NVIDIA as well as from Microsoft and bringing that to shop floor and factory operations with the help of kinetic visions where they are actually providing a specific key ingredient in building the digital twin itself
and how the 3D assets are actually getting converted into USD and how that is actually driving and powering the whole digital twin physical AI transformation.
So that's what we're going to primarily cover in the next 15 minutes.
All right. Very cool. And I think with that, I think Sheru, are you going to kick things off for us here?
Yeah, let me...
Oh, go ahead.
Sorry, are you going to say something?
Oh, no, sorry. Go ahead, Sheru.
Yeah, go for it.
All right.
I wanted to talk a little bit about the specific manufacturing use cases that we're going to be solving with the solutions, because again, technology to solve real problems is what's very exciting to all of us.
So give me a moment so I can share my screen.
Okay. And while she's going to go ahead and sharing her screen, I definitely invite everyone to start posting your questions and comments.
We will be hitting those throughout the hour here.
And I think, Sheru, I see your screen is ready for me to share.
Yes, ready to share.
All right. Here we go.
All right. Like I just mentioned, we are an industrial AI data platform.
What we wanted to focus on today was what are the critical production challenges on the manufacturing shop floor.
We are seeing operations teams constantly under varying conditions trying to achieve a lot of outcomes at once.
These outcomes are typically things like line throughput increase, which is really how do I make, produce as much as I can and run as efficiently as possible.
Schedule adherence, how do I make sure I'm not behind on production?
How do I maximize my machine efficiency and maximize availability so that every machine is running close to its potential capacity?
So all of these problems are things that we've been solving for over 10 years already for manufacturers.
So that's sort of the landscape of challenges that customers are facing.
But with all of the changes in technology, AI and, you know, innovations coming out, what manufacturers are struggling with most are things like user experiences.
How do you make sure that all of these are adopted on the shop floor?
How do you make sure that everybody in the enterprise has the same view of the line?
And these are very complex lines and they're very large production lines.
If you actually go to these plants, you literally can't see a couple of machines away from you.
You can only see your machine in the next one, but you are impacted by what's happening on the rest of the line.
So understanding that system level, you know, process is extremely important and very challenging to do today.
So our vision, which is now being realized as we speak and being deployed with our clients is really about the power of 3D immersive visualization,
agentic AI for insights, as well as a real-time data platform.
So we have found that this combination of technologies really provides the visual elements so you can see what is happening.
Everybody has a unified view of the enterprise and the line.
Agentic AI generates recommendations under varying line conditions.
So it's adapting to changing scenarios and it's providing the recommendations at the point of consumption against the 3D layer.
So what you can see in this visual and what we'll be digging into and doing a deep dive on with the rest of our amazing team here is really how are these recommendations generated?
How are these visualizations available and how does all of this work together seamlessly?
They give a complete unified experience.
So this is just an example of some of the real-world results we're seeing with our clients and I just wanted to paint the picture for what our manufacturing teams are looking to do.
That's all I have slide-wise so I'm going to stop sharing.
Very cool.
And we have a quick video.
Did you want me to play that now or is that for a little later?
Sure.
I think, yes, I can speak for a minute as the video plays.
Edmar, that would be great.
I think it's really important to understand again what the complexities are on the shop floor.
So when you see these 3D views, you'll really understand and as the video plays, you can see that this is a full view of a bottling line.
Now, this is again, based on the scale, you can't really see how large it actually is, but you can see that in this 3D view, you can see the machine status and speed instantly.
You do not have to talk to operators and radio people or walk across the line or make decisions with incomplete information.
So again, the real focus is on providing those immersive 3D experiences with insights all in one space.
So the goal of this is to turn everybody on the shop floor team into a data-driven decision maker.
Amazing.
Very cool.
That's so helpful to have that visualization.
Obviously, there's a lot of opportunity for companies to leverage this kind of stuff.
I guess one thing I always think about when I see something that looks so amazing like this is the work involved.
I think sometimes people, companies might look at this and be a little overwhelmed or, oh my gosh, what's involved in that?
But I think it's fair to say that people could take a very layered or phased approach to these kinds of scenarios.
You don't have to go all in with everything at once.
I'd be curious to see what you guys all think about suggestions for companies maybe watching that.
What would that layered approach look like or fees for getting started?
I think we can answer that real quick, Edmar, and then as you see what kinetic vision and site machine and the Microsoft team have done here,
it'll unpack all of those.
But I think the key ingredient starts with how the customer thinks about a brownfield operation.
So the example that Charlie was talking about is an existing operational bottling line.
They already have systems and processes in place.
Some of them have been actually deployed over several years.
So it's part of that whole digital transformation journey where you need to actually deploy more advanced and more latest technologies to harvest that data from the shop floor,
which is what site machines systems do.
And then you need to bring that into the cloud, which is what we're going to talk about in a second with the Microsoft architecture.
And then you start applying more advanced digital twin technologies with what we were showing earlier with omniverse and kid app streaming, which is what Sashi is going to talk about.
So there are existing systems in place and then there are add-ons that is required to harness that data sets to really drive the digital transformations and building the digital twins.
So we'll get into those in a second.
I think with that, we're probably going to hand over to Drew and Sashi to really unpack that architecture.
Amazing. Thank you, Ganesh.
Okay, so Drew.
Okay, Drew is fixing his microphone right now.
So we'll let Sashi take the start here.
Sure.
Yeah, so as Drew is fixing his microphone, I think, Drew, are you able to share with me?
Oh, you're on man, that's great.
The slide that you had on our architecture.
Yeah, so Sashi, I'll just show the initial thing we did back in the night first and then I'll pass it to you. Is that cool?
That's great.
All right.
So Microsoft and NVIDIA have been working together very closely for a while now to try to come up with a solution that combines the two platforms.
And a solution that's also scalable, right?
So at Microsoft, we do have this facility in Houston where we build hardware, pull out with PLCs.
What you're looking at right now is a fluid process machine, which has pumps, valves and whatnot.
And you can see the omniverse with the 3D context.
You can see on the left the data from fabric.
That's actually an embedded PBI in a single pane of glass.
And so we created this, demoed it at Ignite with kind of an inspirational architecture for the future, which the whole purpose is to inspire companies like Sight Machine to take a hold of and kind of have like a base template where Microsoft and NVIDIA are like, yep, this is a good approach.
And it gives folks confidence that this is the right way to go when doing like this is an operational use case view.
So essentially then, so post Ignite, we posted an open source accelerator repo.
And then Sight Machine got involved and worked with us and they took it and ran with it.
And then went crazy cool in reality with the Coca-Cola consolidated bottling line.
And let me pass it to Sashi right after I go to the architecture.
So this is the architecture and Sashi take it away.
Yeah, so thanks, Drew.
So what we're showing here is a design pattern, if you will, or architectural design pattern for creating an operational digital twin.
Now, this is an exemplar.
It's not meant to be like the end off, but it gives you an idea of what to consider as you're trying to build out a capability.
And I know in chat there's questions about like, how is this effective in terms of auto realistic renderings for users and so forth.
We'll get to that.
Let me walk you through this and then we can start answering some of those concerns too.
Fundamentally, when we start thinking about these kinds of systems, where we start is usually in the data, right?
And so that's on the left most side of this where you have edge computing, pulling in data.
You're going to take that data and you're going to have to do some kind of processing and staging of it into an environment.
In this case, we're using things like Azure IoT operations.
We're using Arc enabled Kubernetes to do that kind of stage.
Additionally, you'll have some level of 3D scene creation, and that has to also get managed.
So in our case here, we started putting that together with Azure Blob Storage.
These two pieces of information need to get correlated.
And that's where we're correlating it with Omniverse Kit and with Microsoft Fabric and Power BI.
So Fabric, Real-Time Intelligence and Azure Functions will take that data that we just received, convert it into more actionable data,
convert it from, you know, bronze data to silver and gold data.
And then the Power BI elements in block two will start overlaying that data into a user interface for a user.
That's getting combined with the Omniverse Kit app streaming, the photo realistic rendering.
Now, the big question that most people will have is, well, okay, you've got these two streams.
You've got this 3D enrichment or 3D data and you've got this IoT data, but how do you connect that?
And that's where it's kind of powerful.
We put in enrichment into the USD.
So the USD files that we create in Azure Blob Storage with the 3D data, we enrich them with ID information and other metadata.
I sometimes refer to that as syntactical sugar.
And that's what's brought in into Omniverse Kit and provides the front end.
And the front end is able to then map between the data sources coming in from Fabric to the data source in Omniverse
and give a user an immersive 3D environment plus the dashboarding effect.
So why, right?
Like, okay, this is great.
And one of the questions is usually, so you can do in 3D, but how does this really help?
Now, if we can imagine in the world, as we're progressing into decision making, we want engineers, users and developers
to be able to quickly contextualize the space that they're in and be able to solve problems in that space.
Fundamentally, if you've got an area in a factory or you have a stoppage or a blockage and you want to understand where that is,
what's the context around it?
Maybe you're looking at understanding, okay, what's the ingress path to get to that location to service something?
Or what do I need to change?
You need an immersive 3D environment.
The photo realism part of it helps you in understanding both what you might do as a human operator,
but also what you might do downstream when you're doing things like computer vision or other AI genetic kind of workflows
for simulation purposes, for training, what I've been so far.
So this is kind of like the starting point in which you can then have downstream use cases.
And the starting point then becomes the same single pane of glass that you would use for both operations and for simulation environments
and what if analysis and so forth.
With that, I'd love to hand it over to Jeremy and he can talk through how we created the digital twin.
It's great. Thank you, Sasha. It's awesome.
Jeremy, looks like you have the floor now. Are you ready?
I'm ready.
No pressure. No pressure.
No, so let's see here.
Edmar, I've got a couple of things. There you go.
All right, you're reading my mind here.
So let's start with this.
This is really just a basic representation of what we do to create the beautiful 3D asset that is inside
of factory operate or inside of site machine.
And all I do is just, you know, we have a lot of people on this call.
They're going to be at varying levels of knowledge about all this.
I saw some great questions about, you know, how do you create these assets?
What are the steps?
And I'm going to take some time to go through that.
But, you know, Edmar, you asked one very interesting thing about, hey, how does a company take like a layered approach to this?
And because we are, you know, integrating these solutions, we meet our customers where they're at.
And that's really important.
And so they may not be ready for a full blown site machine digital twin.
They may need something a little more basic than that.
So we developed this really simple process called the choir activate optimize.
And then, and then at the end, you know, collaboration through an amazing platform like omniverse really brings all of your stakeholders together.
So from the choir standpoint, we're just, we're just talking about scan data, get your data.
Most companies do not have a hold of their data.
Activate it using real simple software and then optimize it using data analysis that site machine provides or simulation.
And then in the end, when you have all of this fantastic compute available, you can immerse yourself and really create a connected experience with all of your users.
And so I also saw a question about, you know, how do you know when to use like a really high fidelity 3D model versus really basic representation?
You know, I always err towards immersiveness.
If you can do immersiveness without friction, then the human, we are humans.
The human experience is going to be better.
You're going to be inside the digital twin.
So the more that you, where you get into issues is where you have, you know, lots of data.
It's complex or you, you know, can't display it very well or there's a lot of friction and understanding it.
But if you can without friction err towards immersiveness, you're always going to be in a better spot.
Sometimes you need a little bit of visionary leadership there to, you know, kind of push an organization that direction.
But that's where we always tend towards.
So, you know, I have a really simple video that makes this stuff look simple and feel simple.
It's, I haven't labeled this video one.
I don't know if you can flip over there real quick.
Let me see. I'm looking at it. I see.
Oh, yeah, I do see it. Let's see.
I got three. I got all kinds of content.
Even small delays can snowball into big disruptions.
That's where digital twins come in with our acquire, activate, optimize process.
It's fast, easy and low risk.
We start by scanning your facility in stunning detail up to 10 times faster than traditional methods with zero disruption to operations.
That scan becomes a powerful 3D digital twin, enabling virtual design, remote tours and supporting system updates, layouts, training and safety planning.
In just 60 days, we uncover eight to 10 times first year ROI with payback in less than three months.
AI delivers smart insights and actions to drive fast solutions and improve overall operation effectiveness.
Dashboards track performance in real time.
AI flags a labeler jam, triggers a fix and recommends a second forklift and dual labeler to boost material movement and throughput.
Have a problem or want to try a change?
Simulate it first.
No risk, no downtime.
Your digital twin drives smarter decisions.
Once it works in one facility, it scales easily, making digital twins the perfect solution to transform your operations.
Okay, Edmar, that was marketing glitz from our marketing department.
Makes it look simple.
Well, I gotta tell you, there was a couple of pieces of information that were super compelling.
The ROI of three months, that's pretty amazing.
And also the granularity of detail, down to five millimeters.
It's pretty wild.
Yeah, so can you queue up, there's something called Video 2 in there?
So this is going to be a little more like, what does it look like when you do this stuff?
Like, who's doing what? When are they doing it? What programs are they using?
So just queue that up, that'll be another minute, and then I'll talk a little more after that.
Okay, here we go.
And there's no audio on this.
So really all we're showing is just a case study here of taking this acquire, activate, optimize process to a real facility.
This is a distribution center.
So Brian's out there scanning it.
He looks a lot like that little iconography we made of him.
We use real, we take that data, grab that 3D data, and then we use some other NVIDIA partners.
This is Preview 3D on this particular project to really quickly get measurements, get panos.
This is a simulation tool that we happen to use called FlexSim.
And just, you know, once again, an alternate process, not related to what we're doing with site machine here,
but another method to optimize the site.
Taking those, we're actually doing virtual camera simulation here just to make sure,
even stuff that's as simple as get your camera set up in the right place before you install them, that can be all done virtually.
And it saves a lot of time and it saves big mess ups.
So just a couple of visuals of a recent case study, that's a real project we did for a real customer.
And we did deliver, that was like a three month payback.
And there's some big decisions being made off of what we found digitally.
The biggest thing is not disrupting the operation.
That's kind of the biggest thing.
And then just queue up my PowerPoint if you wouldn't mind.
Okay, let me move this one first.
And then let me see, your PowerPoint is right here.
Yeah, I win the contest for most media presentations.
I love it.
Okay, we saw that, we saw that.
Okay, so how do we do this?
First of all, do not underestimate, you need some great people to do this work.
Kinetic Vision has a lot of amazing people, but we're talking about between site machine and Kinetic Vision.
We have data scientists, we have mechanical engineers, we've got machine learning engineers, we've got software engineers, technical artists.
It's a diverse team.
So when you're making decisions about building even just the 3D asset, it's really helpful to have subject matter expertise alongside your tech artists when you're building that asset.
So just a little bit about, you know, do not forget about the people.
AI is amazing, but we, at least for probably the next five years, we're still going to need people.
Okay, little nuts and bolts on like, what do you use to go do this?
We're, what we're doing, I'll look at the end here really quickly.
We're, you know, we're publishing a kit app, you know, kit app USD that's streaming within an omniverse, like visual context, and that's part of the site machine application.
And so what we're doing is we're delivering a USD asset that can deliver that high fidelity, fully realistic, interactive view.
What we start with is this 3D scan.
There's a lot of choices here, everybody.
We happen to use Naviz, Faro and Leica.
Naviz scanners are very fast, down to five millimeter accuracy.
You're doing a lidar scan, capturing millions of points as you go.
Faro and Leica are more terrestrial scans.
And so you're setting your, you know, you're setting your tripods and you're capturing data, but they're much more accurate and you get a lot of higher fidelity.
So we typically use Naviz for scanning a full site.
And then if we have particular machines where you really need really accurate user interface details and accurate, like mechanical details, we'll go in and re scan with like a Faro or Leica to get those high details.
From a reality capture perspective, we're taking those scans and we're activating them with the real simple off the shelf software that we integrate.
You have a few choices there too.
There's a big ecosystem out there.
We use preview 3D here at Kinetic Vision.
And let's see, we also use Reality Cloud Studio.
So these are two great programs.
There's a handful of them out there, but what's nice, we work with mostly big companies and a lot of them prefer to procure software instead of using something open source, something that's supported.
But there's open source options also.
Then, you know, once we get that reality capture data, which includes really high resolution, panographic images, sometimes we're creating a mesh that's got materials applied to it.
We then pull that into a 3D digital content creation package.
Pick your package, 3D Studio Max, Blender, Maya.
There's a lot of great choices there.
We happen to use all three of these.
And then you're following traditional artist workflows.
You're either doing direct modeling.
You're using a model library and maybe bringing a model in.
You're referencing those scans for the geometry sizes.
You're perhaps re-topologizing some geometry.
And then you're building your assets in 3D Studio Max.
I'll put a footnote here.
There's a lot of exciting, exciting technology around the generative creation of these assets.
NVIDIA's got some great open source libraries out there that they're publishing with their research teams.
And, you know, they're worth checking out.
We're not fully using them in these workflows yet, but there's going to be a whole slew of software packages and workflows available around using generative AI.
So, you know, we're going to be using them for 3D.
And then, you know, once we have that 3D Studio Max or Blender asset,
in order to access it programmatically within Sight Machine,
we're, you know, grouping portions of that USB file.
We're making them available so they're triggered by a sensor.
We're setting camera views.
And so what we're doing that in is just a little application that we built called Data Vision.
It's built on the Omniverse SDK.
And it's really using those great resources from the Omniverse SDK to build 3D application.
This allows us to layer in some extra data that Sight Machine needs to hook up to their platform.
So, and that's most of it.
I'm sure there'll be some questions, but just to cap it off,
this is just one of an asset from one of our pieces, one of our recent projects with Sight Machine,
just showing these steps, going from 3D Point Cloud to Reality Capture Asset to Assimilation Asset
to a really beautiful photorealistic asset with animation done through the Omniverse SDK.
And that's what I got for you, people.
That was really cool. Show me. I didn't see that before. That was really amazing.
I know. Ganesh, you're always, yeah. Ganesh is like, he's like, you never show me anything.
Like, I just got to see the latest stuff. So, yeah, there you go.
I love it. That is so wild. I think, yeah, it's a lot of impressed people watching in the chat as well.
Very cool. Let me see. And I think I'm going to leave this on for me for one second.
And there we go. Okay, cool. That's wild. So, anyone who's just joining us, thank you.
Welcome to the stream. We're talking with Sight Machine, Microsoft Kinetic Vision,
and of course, folks from the NVIDIA team about how Agentec AI and digital twins are transforming manufacturing operations.
Be sure to stay active in the chat. We see a lot of questions and comments coming through.
We'll try to address those. But that was fantastic. Thank you so much for carrying us through that really nice journey, Jeremy.
You are welcome.
Okay. All right. So, of course, now that we've talked about scanning and creating USD,
I think we have our friend, Sudhir here, who's going to bring us into, to bring us home here, so to speak.
All right. Can you share my screen?
Yes, let me see if I can. Yeah, I think I got it right here.
Okay, we can see it.
Awesome. Awesome.
Thanks, everyone. Super exciting. So you guys saw how Shashi and Blue presented the reference architecture that was done at Ignite.
Super cool, right? Shado presented the use case that we are talking to customers about and how we are showing value with this ecosystem.
And then Jeremy presented how they take all these scans on the factory and convert them into meaningful USDs that we can then use.
I'm going to just put it together to show you how Sight Machine took all these pieces together and built an architecture that shows value to our customers with all of these pieces put together.
So here's the technical architecture diagram. It's a flavor of the reference architecture that you saw earlier.
I'm going to highlight some of the changes we did or how we added on our technologies to make this even more compelling for our job customers.
So first off to recap Sight Machine is a AI manufacturing data platform. We take data from all these data sources that you see on the left hand side.
We standardize them, convert them into standard data models, thereby enabling things like analytics, general AI, digital twins and so on.
So here you can see the first step going through the steps here is our factory connect application that Sight Machines application that runs on IoT operations as Shashi was mentioning in the Unable Kubernetes cluster.
This gets all the data and passes it on to the next step.
We also have this data powering the factory operate and factory build platforms which are Sight Machines proprietary platforms to process and model the data for use in the kit application as well as for further analysis and AI.
All of this is running on the Microsoft Azure ecosystem to deliver a scalable unified solution.
So let's look at each component of it by drilling down into each and see each aspect in more detail.
So first off the data ingestion piece, right? So factory connect ingest data. The first step is to ingest data from the edge.
We are able to ingest data from a variety of manufacturing data sources like PLCs, historians, etc.
Factory connect problems as we mentioned in the Arc-enabled Kubernetes cluster, which offers an extensible, composable view to represent the line.
So that's the first step.
The second step is we use IoT operations here that reduces the complexity to build the end-to-end solution.
IoT operations enables us to connect the cloud and edge using bi-directional messaging.
On the second piece here, we have the 3D scans of the factory. This is what Jeremy is talking about. These are created. You saw all the details. I'm not going to go into it again.
These scans are segmented into Assemblies, Machines, Components. All those are in the USD format, which is then loaded into Azure Blob Storage for consumption by us and by NVIDIA on viewers.
With this data and leveraging Azure AI services, Site Machine is able to provide effective insights.
Next up is the scalable cloud platform. On this cloud, once we have the data transferred from the edge to the cloud, Site Machine is manufacturing data platform.
This powers all the data to factory operate. All this runs seamlessly in Azure cloud and Microsoft Fabric.
IoT operations sends data to the cloud via Fabric Azure Event Hubs, where I even streams in Fabric.
This is where Site Machine is able to process the data and create those standardized data models that I talked about that represent the entire line end-to-end.
With this data and then leveraging AI services from Azure, Site Machine is able to provide effective insights like agentic recommendations, which we will look at shortly.
The third piece here is the omniverse kit extensions. We are leveraging NVIDIA omniverse kit app streaming, obviously.
Now that we have data from Site Machine's data platform, we built a couple of kit extensions to integrate with the kit app streaming application.
The first one as Shashi was alluding to takes real-time data from the edge as well as the model data from our factory build application to annotate the USD
so that we can get contextualized data for these twins that Jeremy is so beautifully generated. Example things like filler speed.
That gives you the context of each machine, each asset on the line layered on with meaningful data from the Site Machine application.
The second piece is the operate extension. That's the one that handles rendering of this contextual data.
Example, creating a billboard on top of the machine to show you the name of the filler, the attributes, how it's running, its status, and so on.
It also handles things like zooming into a particular asset, events on the UI, showing where a fault is.
It responds to data changes, like if the filler speed changes, you'll immediately see the change in omniverse, in our UI, and so on.
We see that in a demo or a short piece. It automatically syncs up. Everything is instantly available on the UI and in omniverse.
Events in the UI are in React and it's communicated to the kit application.
Now let's look at how the UI piece works. The last piece here is the seamless UI integration.
On the front end, Factory Operate is a React application. We embed the omniverse viewport into Operate using NVIDIA provided packages.
Every user then connects to a stream from the kit app to show this viewer in the UI.
In order to improve efficiency, we've implemented things like stream management to pre-created cash streams, creating the stream pools, showing instant availability in the UI.
Events in the UI are passed to Kit App via WebRTC and Y-Saver.
Anything that happens in the Kit application, you can have events reflecting in the UI and things the user interacting or data streaming from the UI can interact real time with the omniverse application.
The whole stream is contextualized to the View and React labels and other annotations in the stream provide a seamless view of the line with recommendations from agent.ai layered on top of it.
Very nice.
Okay, you got it. Okay, cool. That was fantastic. We got a lot of great comments as you were showing your presentation there.
Good questions too. Is there anything you guys want to adjust before we start tackling some of these questions?
I want to show up and then we can jump into questions. Give me one sec.
I see omniverse ambassador John Mitchell from BMW is watching today.
We're showing great interest in connecting with members of our panel here, which is great. BMW of course is doing amazing things in the digital twin world with their factories and robotics.
Okay, so do you guys see it right here, right?
Yes, that is correct.
Okay, awesome. So just show a quick demo, putting it all together.
Charo showed this briefly in the video. This is a live version of our factory operate application running with omniverse get up streaming embedded in our React application.
As you can see, this is an entire view of the line. It's a modeling line.
On the left hand side, you can see a bunch of, you know, metrics for the line. What's the machine efficiency, what's the filler speed, different fillers, production volume, what flavor is going through the line and so on.
And at the bottom of the screen, you can see all the assets and their individual attributes like the filler one, it seems like has a fault, filler two is running well, and so on and so forth.
And you can see the same data being leveraged and shown in the omniverse get up streaming as well as billboards, as I mentioned, using the web RTC cons that we talked about.
Now, if I want to look in detail, I can see, okay, this filler one has a fault. So let me draw down into that.
Now, and you can see we zoom in to the asset. And not only do we zoom in, we are able to highlight specific areas of the machine where the fault actually occurred.
So it looks like a conveyor jam. It reflects the red section is this is the place where the operator needs to focus on to fix the issue, right.
So this is super powerful. This is where we talked about how kinetic vision is helping us build the USD segmented into components so that we can leverage our data and pinpoint exact locations.
So not only do we give calls this we also give exact locations and recommendations for operators to immediately fix this on the shop floor.
Let me zoom back out a bit. The other thing you can see here with this purple screen is it says line is running well but not at peak efficiency. I'm going to take ways to optimize.
And this is where a genetic AI recommendation engine comes into play, right. So not only are you seeing like current statuses and basic ways to fix it.
You're also seeing things like hey, the filler speed should be increased from 630 to 700. That's what our agent to get I recommend just the optimal speed for the filler or something to do with the tackle to update their settings to accommodate the next set of packaging or a wrapper.
What is there to replace and similarly, you can now say hey, go to the rapper. Let me see what's going on. Hey, prepare to wrap replace the rap material.
AI has detected that you might run out of the rap soon enough so that's the time for your operator to go on set up the rap get it put it in place so that you don't have an issue you don't have stoppages.
You don't have down times. So very, very powerful leveraging of our data and leveraging of all the 3D assets that Jeremy showed the army was kept up streaming platform bringing it all together to show immense value for our customers.
I will stop right there.
Wow, amazing. I don't think anybody wants you to stop where everyone was blown away. You're going live and everyone can obviously you notice the time and date up up on the right. It was not faked.
Real time. That's pretty amazing. That's a great question. The comments in the chat. Do you any any more or less thoughts before we start tackling some of these questions. I know we've been discussing something in the background which to tackle.
Okay, alright, so let's go. We have we've got nine minutes, so which means we got we got a hustle. So let's try to let's try to keep our answers as concise as possible and we can refer people to our discord.
Amelia is going to set up a thread on our discord server specifically for this live stream. So whether you're watching this live or the replay, you can go to that thread and you could continue to ask questions, conversations and hopefully with other viewers will also go there too.
So you can chat with them on it. Alright, so let's do the little lightning round of questions here. I think earlier on actually we had a good question. I think for a first site machine so sure if you want to take this one about how do you approach a client engagement and when client stakeholders have different priorities.
Who do you prioritize and why.
Yeah, so the answer to that is we have to prioritize everybody. He actually worked with both it and we provide a complete solution without the agreement and agreement across these functions and now now we're also seeing a separate a function which really collaborates with it and
and we provide daily solutions to one in the enterprise. So we go all the way we address security, plant connectivity, as well as operational data and insights.
So the real answer is you cannot afford to prioritize one over the other because then you're not going to have a successful plant level transformation that scales.
Okay, very, very super helpful and just a quick note we had to wave goodbye to Sasha a minute ago he had none of the meaning you had to go do so thank you Sasha for always helping out great to have you here.
Okay, we also I'm looking so we have a couple of different chat channels where we're keeping track of some of these questions I'm looking at our internal chat here where I see a few things that were discussed.
Sure you just mentioned you saw a good question from Victor. Amazing collab curious with this foundation which type of simulation becomes more feasible high fidelity asset level modeling or broader plant level.
What if situational modeling did you want to tackle this one.
Sure, I'd also like to invite maybe so they're in others if they have any specific ideas just to talk about this but we are thinking a lot about what a situational modeling and scenario modeling.
Which means that if I have a change in raw material or if I add another machine to my line. How does the capacity change what will my line look like and what should I be planning for.
Ideally we help them make real decisions by seeing things like if you add another machine you can reduce all of your weekend shifts or eliminate them all together.
And again the focus is on providing broad scenario level guidance with with our simulations but if anyone has additional thoughts and everything I'd love to invite them as well.
Yeah, yeah.
I have one point to add as well.
Yeah, so it's sort of I would say both right like at the first step.
We're trying to get as accurate a representation of the models as possible right and then try to solve real problems like these agentic recommendations or what's happening on the shop floor and so on so forth and for that perhaps you don't need like the super super high fidelity still high fidelity as you could see.
But as as you were talking about but there are definitely use cases when you're looking at animations and like real time playbacks of you know how things happen and how it caused a fault or what have you where a more realistic representation is absolutely essential.
So both are different use cases and you know both are something we are looking at that to finish.
Yeah, so but what what you guys said is spot on but specifically on the asset level or simulation specific question that Victor is asking that we were talking about the bottling line use case the single most expensive asset that's there in the line is a filler and then of course the next one is a packer right.
So there are filler level simulations that can also be done to understand spillage inefficiencies things that are supposed to be followed by certain string guidelines of course you cannot stop a filler to run those scenarios.
So that's where simulation comes really handy.
So to think about the whole digital twin journey that we were discussing in the last few minutes is to break down into two separate journeys one is the whole operational digital twin and the other one is a simulation digital twin and they both kind of go hand in hand.
So the filler simulation is one of the examples where you could be thinking about fluid level simulations and the CFD style simulations as well where we were actually in track with a third party simulation provider that can run that kind of filler level simulations as well.
It's a it's a team play it's not like in a one size fits all and one partner provides all the different experience in the services you bring the right ingredients for the right kind of job to get the results.
The business results that you expect.
Great.
Okay that's super helpful.
And so this this gentleman or a gentle lady has asked us a couple of times in chat so I know they're eager to for this answer.
This is an interesting question obviously because we're talking about very developer kind of focused pipeline but we have a lot of creators out there in the world who really want to ramp up and and contribute to these challenges.
At C graph recently there was a session if Amelia has has a second maybe she can try to find that session I think the moment live the other day for everyone to watch now we had a session about as it's really as if you're a 3D content creator how do you upscale for the physical AI.
Does anyone have any any suggestions for Yash here.
I think I think this is a great question for kinetic vision and Jeremy do you want to take that.
Yeah so I actually saw this question I thought wow we're actually going to be answering some of the one of these questions so you know I the things that I laid out in my presentation.
It is a lot about tools curiosity and skill sets right so.
Really just you know for this type of simulated world we need these types of tools we're using Navis scanning we're using reality capture and then we're using what you know if you're if you're a creator you're already using this great set of 3D content content creation tools.
You know in Maya 3D Studio Max substance you know those are those are all the set of tools that you're using so.
I think really using the those tools and the guidance on how to get to that level simulated world I think is you know from from the from those software vendors is you know going to be really important but the other thing is the collaboration what I'm.
You know a lot of these questions what I'm hearing is they all relate to collaboration and communication.
These are big problems we're creating an entire simulated world so that means that we need all of the skill sets and expertise from each of the individuals that places something in that in that virtual world.
I'm sorry Ganesh I'm not sure when you learned about fillers but you know to make a good decision about how to make a filler run operate properly and it's optimal speed is probably not your your expertise you know enough to be dangerous.
But working with that person who either sold the filler to this customer or who operates the filler every day is really important.
And so you know you have these hard skills but the soft skills are just as important making sure that you are building that network of experts and you're collaborating them.
Collaborating with them as you build that simulated world I know this is a developer webinar and we want to hear about you know programming languages and programs but I cannot underestimate.
The this is where the I saw some you know I'll pivot here to I see things you know comments in here about well you know once we you know once we get rid of the humans where how do we you know manage the narrative.
Don't get rid of the humans that the humans like we're stuck doing so much BS now elevate the humans and continue to build those relationships use the relationships to build that simulated world OK I'm going crazy here.
I'm going to save that quote when I have an extra view with my boss so thank you.
Cool.
That's great.
You're out of here at March.
All right.
So speaking about programming languages about this question came in from LinkedIn about CUDA programming and the a models.
Don't want to tackle this one.
I don't know if we have a CUDA expert here but no I don't think I don't think it feels like a.
Yeah.
Yeah.
So we'll.
Sorry.
CUDA is foundational like every single thing that that you see that's built on top of machine learning and artificial intelligence that allows the GPU to accelerate it is built on CUDA every single every single operation is built on CUDA.
Nobody talks about CUDA anymore because it's so foundational you have to have it to make these operations move quickly on a video graphics card.
So you know it's really any any anything that you're using to accelerate the training of a neural network the inferencing of a neural network a scene graph.
Every single one of those operations is built on the CUDA libraries.
Great great information and may ask the panel here because I know we're at time.
Did you want to hard stop we take a couple of questions.
Okay great.
Okay here's one coming in from Jesus who is asking if we can describe their app streaming over Azure has experienced latency etc.
I believe the question means the Ocast the on US kid upstreaming that's available on Azure marketplace.
It is available.
It is a co-sale ready fully transactable marketplace listing.
So some of the work that we saw in this in this session is actually using that same Ocast.
So as a developer you can actually go to Azure marketplace today and get started follow the GitHub repo the same things at what site machine did.
So the work that site machine did was following that ignite repo that drew was talking about.
So that's the close partnership that we have between NVIDIA and Microsoft to showcase how to get started.
How you can actually take those containers the kid upstreaming containers deployed on your own.
Kubernetes clusters on Azure leverage the a 10s that are running on Azure commercials and get started.
So it all basically starts from that kid upstreaming and then build the entire solutions like what's it was talking about with that front end web applications and integrating it into that web app.
So it's it's all there.
It's all available on that marketplace and get a repo for that we release at ignite.
I think we should also admire you said that discord will have it so we should also share that GitHub repo as well for any developer to get started today.
Okay, we'll do that.
I think Amelia posted that thread in the chat a little earlier.
We'll also add to the description afterwards.
We got another question here from LinkedIn.
Alan, this is probably great for everybody, but definitely Ganesh.
This is, you know, obviously when when when we have business folks from different companies watching these kinds of topics and they realize, you know what, I should take a look at omniverse or take a look at open USD.
What would you say, Ganesh?
What what's what what is your pitch to have these have these leaders actually take a serious look at adoption.
So I think I think it's all I believe channel kind of laid this out at the beginning of the session.
It all starts with the the KPI, the business KPI and the real like immediate material impact.
So the modeling line use case that Charlie was talking about, there was an operational efficiency gain or throughput improvement or yield improvement.
That's where that's where this entire kind of journey started off.
So try to understand how what that KPI would look like and it can be it can be related to going back to what Jeremy was talking about.
If you take the filler simulation example, there are subject matter experts who have been doing that for decades.
So but if they are looking at a specific business KPI that they want to hit through a simulation scenario, then then start with that.
See where all of these new new technologies and the digital transformation, the digital twin, the simulation workflows are going to actually cause an impact.
In case of psych machine, they zeroed in on an OE improvement, took their platform to Microsoft and then media technology took the help from kinetic vision and showcase that example with a double digit operational efficiency improvement.
If I remember right, Charlie, I think it was like a 10 to 15 percent.
You guys were targeting like one to two percent and that itself was like a big impact, both top line and bottom line, but they were able to showcase a double digit impact of 10 to 15 percent on top line and bottom line.
Or I wouldn't say top line of bottom line, but the operational efficiency gain that has impact both top line and bottom line.
So that's that's where it starts.
It always starts with the business value and the KPI and then start with that one use case.
Look at your existing stack of technologies that you have.
See what the gap exists, adopt the technologies to the right level rather than like throwing everything out of the window and start from scratch.
That never works.
It's way too expensive and then see the incremental gain and then keep expanding from there from one line to multiple lines from one factory to multiple factories and see whether that really sticks.
So that's that's how I think I would think about.
I'm open to other inputs and ideas from folks here.
Oh, I've always got a hot take.
So, you know, for organizations when you're thinking about your omniverse investment, you know, omniverse is an, you know, an entire ecosystem.
So you can engage it in different ways.
This engagement through an ISV like site machine is actually a very low friction engagement because your organization probably already has Azure resources.
And now that Microsoft has made this capability available through Azure.
Now this is just a SaaS purchase.
You're just calling up a company like site machine and you're saying, Hey, I would like to use your software to see my game.
And then you're, and then it's utilizing either the SaaS platform or your cloud Azure private tenant.
And that's a very low, that's a very low friction way to invest in omniverse.
You may be a different type of organization and say, Hey, we have a very unique manufacturing process.
A site machine doesn't meet all of our requirements.
We want to build our own applications to be a technology leader in our space and lead everybody else.
That's where then you would make the type of investment with omniverse via omniverse cloud or OBE to bring your developers and actually have them building their own applications.
If you're an organization who can build your own applications and you want to be a leader in your technology, then that's how you would invest in omniverse.
And that's like a strategic decision.
Are we a developer of applications here within our company or are we a procurer of ISV applications to solve our problems?
And that's a key decision point between leaders. Are you developing or are you procuring software?
That's great, great, great context. Amazing.
Okay, we got another question here.
And actually, Jeremy, enter it quickly if you can, but I think you covered this earlier.
How helpful is photorealistic effects when used by GUI engineers or technicians in practice?
Yeah, I mean, I think about this as communication, right? Who are you communicating with?
The more photorealistic your output, then the broader your collaboration is with all in your organization.
Yes, if you're engineer to engineer, you may not need photorealistic effects to be able to solve a certain problem and change a variable.
But if you are making a big investment and you need to communicate to leaders on opening up the budget to go ahead and make some huge cost savings for the company,
you probably do need some kind of photorealistic output.
And people, NVIDIA is going to give it to us for free, right?
I'm not for free. You got to buy GPUs, okay?
But the highly photorealistic effects should all be free in the end.
Yes, there's some friction now, but that friction is going to decrease over time.
Everything, you know, compute becomes cheaper over time.
And so I do think it's very important, especially in creating these connections through your organization to get real work done.
And the photorealism to Jeremy's point, today it feels like it's a human-to-machine interaction,
but the future is actually towards massive amount of automations through physical AI.
It's a lot of machine-to-machine training.
So for a robot to really train and learn, it needs that physical accuracy.
And that's why photorealism is super important.
And when we're building assets, Jeremy and I have had this conversation many times.
When we're building these 3D-USD assets, today it's about operational twin.
But really, that's just the stepping stone.
The real goal is to drive through that entire journey of operational simulation
and ultimately for physical AI and robotics, right?
And when you're thinking about that journey, you really need that photorealism
and really high-fidelity, accurate USD assets.
Yeah, we should do another hour on synthetic training data for vision systems.
Exactly.
Yeah, that is really important. That's the technology side.
I'm always trying to cover the technology side and the human side.
So sorry, I shouldn't have left that one off. That's like a huge, huge thing.
Well, this is an unrelated note.
Curious how one decides optimal resolutions of point clouds that can be fused with realistic data.
Do you have any thoughts on this one?
Yeah, these are all...
It's like what's available and fast, right, and can solve the problem.
It's a multi-variable solution and it's always changing over time.
So we've chosen a certain type of resolution because we're talking about, I'll say,
human-scale, facility-scale decisions, right?
We also operate three North Star Industrial CT scanners here.
So that's a completely different technology.
It scans down to the micron level.
We can detect the porosity and the femur of a mouse or a void in a solder pad on a BGA.
So you really need to use the right technology for the job,
but it's going to be some mixture of those things, right?
The expense of the technology, the value of the problem you're solving.
But if you have questions, hey, you know who you can call.
Yeah, that's great. Well, thank you.
It's fascinating. I never thought of a mouse femur before.
So Rich is asking on LinkedIn about, is this workflow available with academia?
What do you mean, of course? Why not?
We do have an HDR team here actually at NVIDIA that I can help put you in touch with
if you have specific questions about your school working.
But Ganesh, I don't know if you have any other thoughts on this.
No, this is great.
Thank you for hosting it. Thank you for inviting everybody.
And thank you Psych Machine for an awesome work.
I know we had a really short ramp of getting all of this integrated up and running in six months.
Thank you Kinetic Vision for an incredible partnership and support.
You guys have been awesome with NVIDIA for over the years and of course for Microsoft.
So thank you Drew and the entire team.
What we started off like a year ago, it's really like blossoming and this is just the beginning.
We're going to grow even more.
And Ed Mar, I hope you will invite us again with more exciting things.
Of course. I always like to ask this as a last round.
Ganesh, you just went. So you passed this.
But does anyone have any last kind of words of wisdom that you want people to really remember?
What's your key takeaway you want people to come away with?
We'll start with, we'll go backwards actually.
So Drew, what's your key takeaway here if we want people to remember?
Key takeaway. NVIDIA and Microsoft have a beautiful relationship.
And we're also trying to design industrial standards together as well.
So just stay in contact with us and hopefully it gives you confidence to build whatever you all dream up.
So that's what my takeaway is.
Amazing. Great words Drew. I'm glad you went.
Sheru, you're next.
Oh, okay. Yeah. So I think the key takeaway I'd love for everyone to remember is, you know,
the domain specific intelligence and architecture that Sight Machine provides is critical for manufacturers to actually deliver real value.
And that can be a great framework for other verticals as well, right?
Understanding the entire technology stack that's necessary for AI and data transformation, I think,
is becoming much more fascinating.
But you know, understanding each layer and what we provide in addition to Microsoft and NVIDIA platforms would be a great thing to keep in mind.
Great words, Sheru. Thank you.
Sudhir, what's on your mind?
Yeah, I'll channel Jeremy a little bit and say this, you know, sort of the two modes come to mind, right?
Like this partnership and what we've achieved showcases what can be done with the power of omniverse and Microsoft Azure and Fabric, right?
And if somebody who's like really wants to put on a developer hat and play with it and build these technologies, like this is the, you know, sort of blueprint on how it can be done and how it has actually been done.
That said, as Sheru mentioned, a lot is based on data and the domain expertise and so on, and that's where Sight Machine comes in.
And so if you have looking for a solution that offers end-to-end with the data, with all the integrations in place and has done the heavy lifting for you, please feel free to contact us and we have the solution for you. Thank you.
Amazing. Okay, Jeremy, you have the last word here.
I'm afraid.
Yeah, well, so in manufacturing and supply chain, most people's operations run around 50% of the nameplate of what it was designed for, okay?
At every big company, these are billion-dollar problems and they're only going to be solved digitally.
So if you're looking at like, how much does this cost? Should I do it?
Yes, the answer is yes. It's 10 times faster and cheaper doing it digitally than going in and doing things manually.
So the biggest issue is not the cost. The biggest issue is not the technology. The biggest issue is a people problem, okay?
This is about multidisciplinary experts understanding all aspects of the problem. It's about having a vision.
I love that question. What do I do about IT and manufacturing and them being misaligned? Get them aligned, people. They have to be aligned to solve these problems.
So much of what we're talking about here today, this is amazing. NVIDIA has done an amazing job of making sure that technology is available.
Fantastic. Developers and ISVs like Sight Machine have made sure that they use that technology and put it into products.
And then, you know, Kinetic Vision is helping customers solve problems every day.
But what I'm seeing on the front lines is people issues and companies really coming to grips with how to solve these problems in their organization.
So try to build those relationships. People get them in front of the right stakeholders.
Make sure that you're supporting, you know, your visionary leaders, making sure your visionary leaders are working with their visionary developers.
It's a big people challenge, but I know that we'll be able to get there. So thanks again.
That's awesome. I'm so glad. That was a great closing remarks. I want to thank all of our amazing guests from Sight Machine, Microsoft, Kinetic Vision,
and of course, my colleagues here at NVIDIA for sharing your amazing insight. Each one of you has been extremely helpful.
We've seen how Agentec AI, OpenUSD, and Jill Twins can help factory teams spot issues faster and optimize production lines.
And it seems like this is just the beginning of what's possible. So a big thanks to all of you who joined us live out there.
Great comments and questions. We really love the projects that we shared.
If you missed part of today's session, don't worry. The replay is available using the same link you're at right now.
Or just go to our YouTube channel anytime, NVIDIA Omniverse. Go to live and you'll find it there.
And as I'm showing on the screen now, this kind of experts presentation is valuable to you.
I highly encourage you if you can make it to GTC DC. So our first one, Washington DC, you're going to see Jensen there providing a great keynote.
So I hope everyone there can make it. I will be there. So let me know if you're going and we can grab a coffee or something together.
Until next time, thank you so much for joining us, everybody.
It's been an absolute honor to join the panel with you all today and with you all there in the audience.
Have a great rest of the day.
Thank you so much.
Thanks.
Thank you.
