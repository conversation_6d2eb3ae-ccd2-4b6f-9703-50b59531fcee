#!/usr/bin/env python3
"""
Audio transcription script with speaker diarization using Whisper and pyannote.audio
"""

import os
import sys
import json
from datetime import timedelta
from pathlib import Path
import whisper
import torch
from pyannote.audio import Pipeline
from pyannote.core import Segment
import argparse


def format_timestamp(seconds):
    """Convert seconds to HH:MM:SS.mmm format"""
    td = timedelta(seconds=seconds)
    hours, remainder = divmod(td.total_seconds(), 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours):02d}:{int(minutes):02d}:{seconds:06.3f}"


def transcribe_with_speakers(audio_file, output_format="txt", whisper_model="base"):
    """
    Transcribe audio file with speaker diarization
    
    Args:
        audio_file (str): Path to audio file
        output_format (str): Output format - 'txt', 'json', 'srt'
        whisper_model (str): Whisper model size - 'tiny', 'base', 'small', 'medium', 'large'
    """
    
    print(f"Loading Whisper model: {whisper_model}")
    model = whisper.load_model(whisper_model)
    
    print("Transcribing audio...")
    result = model.transcribe(audio_file, word_timestamps=True)
    
    print("Loading speaker diarization pipeline...")
    # Note: You'll need to accept pyannote terms and get a HuggingFace token
    # Visit: https://huggingface.co/pyannote/speaker-diarization-3.1
    try:
        pipeline = Pipeline.from_pretrained(
            "pyannote/speaker-diarization-3.1",
            use_auth_token=os.getenv("HUGGINGFACE_TOKEN")
        )
    except Exception as e:
        print(f"Warning: Could not load speaker diarization pipeline: {e}")
        print("Falling back to transcription without speaker labels")
        pipeline = None
    
    # Perform speaker diarization if pipeline is available
    speaker_segments = []
    if pipeline:
        print("Performing speaker diarization...")
        diarization = pipeline(audio_file)
        
        # Convert diarization to list of segments
        for turn, _, speaker in diarization.itertracks(yield_label=True):
            speaker_segments.append({
                'start': turn.start,
                'end': turn.end,
                'speaker': speaker
            })
    
    # Combine transcription with speaker information
    annotated_segments = []
    
    for segment in result["segments"]:
        segment_start = segment["start"]
        segment_end = segment["end"]
        text = segment["text"].strip()
        
        # Find the most overlapping speaker for this segment
        best_speaker = "Unknown"
        max_overlap = 0
        
        if speaker_segments:
            for speaker_seg in speaker_segments:
                # Calculate overlap between transcription segment and speaker segment
                overlap_start = max(segment_start, speaker_seg['start'])
                overlap_end = min(segment_end, speaker_seg['end'])
                overlap_duration = max(0, overlap_end - overlap_start)
                
                if overlap_duration > max_overlap:
                    max_overlap = overlap_duration
                    best_speaker = speaker_seg['speaker']
        
        annotated_segments.append({
            'start': segment_start,
            'end': segment_end,
            'speaker': best_speaker,
            'text': text
        })
    
    # Generate output
    base_name = Path(audio_file).stem
    
    if output_format == "json":
        output_file = f"{base_name}_transcribed.json"
        output_data = {
            'audio_file': audio_file,
            'model': whisper_model,
            'segments': annotated_segments
        }
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    elif output_format == "srt":
        output_file = f"{base_name}_transcribed.srt"
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, segment in enumerate(annotated_segments, 1):
                start_time = format_timestamp(segment['start']).replace('.', ',')
                end_time = format_timestamp(segment['end']).replace('.', ',')
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"[{segment['speaker']}] {segment['text']}\n\n")
    
    else:  # txt format
        output_file = f"{base_name}_transcribed.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Transcription of: {audio_file}\n")
            f.write(f"Whisper model: {whisper_model}\n")
            f.write("=" * 50 + "\n\n")
            
            for segment in annotated_segments:
                timestamp = format_timestamp(segment['start'])
                f.write(f"[{timestamp}] {segment['speaker']}: {segment['text']}\n")
    
    print(f"Transcription saved to: {output_file}")
    return output_file


def main():
    parser = argparse.ArgumentParser(description="Transcribe audio with speaker diarization")
    parser.add_argument("audio_file", help="Path to audio file")
    parser.add_argument("--model", default="base", 
                       choices=["tiny", "base", "small", "medium", "large"],
                       help="Whisper model size (default: base)")
    parser.add_argument("--format", default="txt",
                       choices=["txt", "json", "srt"],
                       help="Output format (default: txt)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.audio_file):
        print(f"Error: Audio file '{args.audio_file}' not found")
        sys.exit(1)
    
    # Check for HuggingFace token for speaker diarization
    if not os.getenv("HUGGINGFACE_TOKEN"):
        print("Warning: HUGGINGFACE_TOKEN environment variable not set.")
        print("Speaker diarization may not work. To enable it:")
        print("1. Visit https://huggingface.co/pyannote/speaker-diarization-3.1")
        print("2. Accept the terms and get your token")
        print("3. Set environment variable: export HUGGINGFACE_TOKEN=your_token")
        print()
    
    transcribe_with_speakers(args.audio_file, args.format, args.model)


if __name__ == "__main__":
    main()
