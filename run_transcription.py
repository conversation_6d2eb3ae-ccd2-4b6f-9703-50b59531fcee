#!/usr/bin/env python3
"""
Simple script to run transcription with speaker diarization on the audio file
"""

import os
import sys
from transcribe_with_speakers import transcribe_with_speakers

def main():
    audio_file = "8-31-25-esv2-17p-bg-10p.mp3"
    
    # Check if audio file exists
    if not os.path.exists(audio_file):
        print(f"Error: Audio file '{audio_file}' not found")
        sys.exit(1)
    
    print(f"Starting transcription of: {audio_file}")
    print("This may take several minutes depending on the audio length...")
    print()
    
    # Run transcription with different output formats
    formats = ["txt", "json", "srt"]
    
    for fmt in formats:
        print(f"Generating {fmt.upper()} format...")
        try:
            output_file = transcribe_with_speakers(
                audio_file=audio_file,
                output_format=fmt,
                whisper_model="base"  # You can change to "small", "medium", or "large" for better accuracy
            )
            print(f"✓ {fmt.upper()} transcription saved to: {output_file}")
        except Exception as e:
            print(f"✗ Error generating {fmt.upper()} format: {e}")
        print()
    
    print("Transcription complete!")
    print("\nNote: If speaker diarization didn't work, you may need to:")
    print("1. Visit https://huggingface.co/pyannote/speaker-diarization-3.1")
    print("2. Accept the terms and get your HuggingFace token")
    print("3. Set environment variable: export HUGGINGFACE_TOKEN=your_token")

if __name__ == "__main__":
    main()
