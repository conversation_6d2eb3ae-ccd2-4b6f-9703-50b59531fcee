start	end	text
0	28000	Hello, everybody. Welcome to the OpenUSD Insiders livestream. I'm very honored to have you join us today. We have a very special episode with some amazing partners.
28000	34000	We're going to dive into that topic in just a second, but we're going to be covering building an authentic AI powered digital twins.
34000	42000	We would love to invite your questions, your comments. Let us know where you're watching from. If you've got any interesting projects related to this, we'd love to hear about it also.
42000	48000	So go ahead and introduce yourself in the chat right now. We're going to hit the questions. There are probably a few different points during this hour.
48000	53000	So if we don't get you right when you post, we're trying to keep track of them. We'll hit them by the end, most likely.
53000	62000	OK, so thanks for joining us. So right now, I'd like to also very attention to this great learning path we've got here. Brand new new digital twins for physical AI.
62000	71000	We just came back from SeaGraph. A lot of excitement on this on the subject, including at labs and sessions. This QR code will bring you to that brand new learning path.
71000	85000	For those of you who've been watching for a while, a part of the community, you all know that these learning paths are a fantastic free resource, self-paced courses, basically, on a various number of topics, including OpenUSD and robotics.
85000	96000	And now we have digital twins for physical AI. So use that QR code to find it. If you need the link, we'll also put it in the chat, but that is a super helpful resource for all the developers out there.
96000	105000	Here's another great resource, developing and deploying your own omniverse kit apps. This QR code will also bring you the latest information there.
105000	117000	Another great resource for leveraging the community on deploying your own kit app is our Discord server, where we have developers talking about this very topic pretty much on a daily basis.
117000	121000	So engage with other community members there. We'll post a link in the chat.
122000	131000	Amazon Devices and Services achieves major step towards zero-touch manufacturing with NVIDIA. This is also an amazing story that just came out. This QR code will bring you right to there.
131000	141000	If you have any questions on this, feel free to pop on over to the live streams channel. We have our Discord server. Feel free to tag me, and I'll get you someone who can help in the chat on this.
141000	147000	Developers build fast and reliable robot simulations with NVIDIA omniverse libraries.
147000	155000	As you can see, we're all about QR codes today. So here's another QR code for you. That'll bring you right to the link.
155000	162000	And obviously, if you're watching this live, you will get these links in the live chat also. No need to bring your phone up to the screen unless you want to.
162000	168000	You're free to, of course. But feel free to check out that resource. We'll be happy to also help you with that.
168000	173000	And how to instantly render real-world scenes in interactive simulation. Another great article.
173000	185000	These articles are actually a great resource for people because they leverage not only our NVIDIA developers, but also members of partner teams, members of the community.
185000	196000	Really great use cases and workflows from start to finish. So highly encourage you to check out each of these, but this is how to render real-world scenes in interactive simulation.
197000	204000	NVIDIA opens up portals to world of robotics with new omniverse libraries, Cosmos physical AI models and AI computing infrastructure.
204000	212000	Cosmos is an amazing world foundation model we've been talking about a lot lately. This will help give you more insight into how to leverage that to the fullest.
212000	219000	Again, at Seagraph, we just wrapped up a couple weeks ago. It was all about physical AI, robotics, Cosmos.
219000	223000	It really showed a nice transformation in what's happening in the graphics industry.
223000	228000	So feel free to leverage that QR code to get right to that article.
228000	237000	And another exciting thing that also was debuted at Seagraph was our open USD certification.
237000	243000	For those who attended Seagraph in person, you were able to actually take the certification exam for free.
243000	249000	Otherwise, there's a small cost attached if you are taking it online and here's the QR code for that.
249000	257000	I highly recommend anyone out there who's been floating around to open USD for a while, go ahead and challenge yourself to take the certification.
257000	264000	I do think it's a great idea to take that learning path open USD before you take the certification exam.
264000	270000	You're going to get all the information you need by following along with those learning path self-paced courses.
270000	281000	And then you'll have a great certificate at the end so you can show the world that you are open USD certified, making yourself even more knowledgeable and more of a thought leader whenever you make your posts.
281000	285000	These are the links that will give you the various resources we have in the community.
285000	297000	We have our calendar, which is the first link there, which will bring you all the upcoming live streams, office hours, study groups, key events that are happening, including GTC DC, which is coming up.
297000	302000	I can't believe there's not a slide on that. I'm actually going to bring it to the web page and I'll show it to you later. But GTC DC is coming up.
302000	310000	I'll be there. We always post those events in our event calendar. Very easy to subscribe and always find out about the latest.
310000	318000	Our forums are a great place to post your questions. If you have any blockers, absolutely leverage the forums for pretty much everything.
318000	326000	Some exceptions include Isaac Lab, where we prefer you to go to the GitHub page for Isaac Lab, but Isaac Sim has its own section on the forums.
326000	340000	Our Discord server, which I mentioned earlier, that's an amazing resource. We have thousands of developers that are on there posting regularly about their projects and assisting each other on a day-to-day basis.
340000	348000	Lots of great channels. It's well organized to find what you need quickly. Just use the search bar on the top right. Type anywhere you want. You'll get there in a flash.
348000	358000	Finally, that last link is our community page, where you can look up things, including our ambassadors and other resources we have in the community.
358000	367000	That is a lot of information for you. I hope everyone got good notes there. Now I would like to bring in my great colleague, Ganesh here.
367000	376000	Ganesh, how are you doing? It is an honor to have you joining us here today. This is a very special episode we've got today.
376000	379000	I'm building Agentec AI-powered digital twins, isn't it?
379000	399000	Exactly. Thank you, Edmar, for having me again. I'm excited to share the next 15 minutes with Sight Machine, Kinetic Vision and Microsoft on how we are actually taking digital twin directly into operational shop floor experiences.
399000	404000	And then how are we actually marrying that with Agentec AI? I'm really excited to be here. Thank you for inviting me.
404000	411000	Of course. Let's bring in these very special guests. This is super exciting. We have Jeremy here. Hey, Jeremy.
411000	419000	Hey, everybody. I'm from Kinetic Vision. It's so great to see you. Tell us a little about your background in Kinetic Vision.
419000	426000	Yeah, I'm hailing here from Cincinnati, Ohio, a great hub of US manufacturing. I'm the CEO of Kinetic Vision.
426000	435000	We develop and integrate digital twins for manufacturing and supply chain. We're an NVIDIA partner. We're a Microsoft partner and we're a Sight Machine partner.
435000	441000	So really excited to be here. Talk with everybody today. And Ganesh and Edmar, thank you for inviting me.
441000	450000	Of course. I mean, I love to see when partners are working so closely together. You see some amazing things happen with these billion of minds that are working together.
450000	457000	Speaking of brilliant minds, let me bring in my partner, Sasha here also for NVIDIAside. Hey, Sasha, how are you doing?
457000	464000	Good morning and good afternoon or good evening, depending on where you are. Hi, everyone. I'm Sasha Bichon.
464000	478000	I work with Ganesh and Jeremy and the rest of the team and trying to bring together all of the good work we've done here in terms of digitization, if you will, and how we apply that into factories of the future.
478000	486000	Amazing. I'm so glad to have you here. Sasha is an amazing person on our team, so he's going to have great context as well as Ganesh throughout this episode.
486000	494000	Here's a good friend also, a veteran of the live streams. Hey, Drew. Hey, Edmar, how are you doing? How you been, man? It's great to see you again.
494000	502000	Doing good, doing good. Yeah, you too, man. So you have a special role. Why don't you tell me what you do over there at Microsoft?
502000	516000	Well, at Microsoft, basically I work with a group called Industrial Solutions Engineering. So basically we go into customers and innovate with them, with partners like NVIDIA, you guys.
516000	530000	And we also have, I also work with a team that's actually in Houston, Texas. They have an actual facility where we build up hardware, like, for reels, and then, like, with PLCs.
530000	541000	And we then test, like, getting data into the cloud and then doing stuff with it. And yeah, I've been working with Sasha, Ganesh, and you all, Edmar, like, for a while.
542000	550000	Especially Ganesh, causing chaos wherever we go. So it's great to be here and good to see you.
550000	560000	That's awesome. Well, thank you. I mentioned in my promo earlier I've had a lot of fun in rehearsals with this whole team of people. Let's bring in the last two people here. We've got Sheru. Hey, Sheru, how are you doing?
560000	562000	Hello, everyone. Nice to meet you.
562000	566000	Why don't you tell everybody about your role and your background at Site Machine?
566000	577000	Absolutely. Hi, I'm Charu Kalluri. I'm a director of product marketing at Site Machine. Site Machine is an industrial AI platform focused on delivering manufacturing outcomes at scale.
577000	588000	Over the years, we've been working very closely with Microsoft and NVIDIA and our kinetic vision. So just really excited to, again, be here with everybody to talk about the compelling value of all of these technologies put together.
588000	593000	Awesome. We're very excited to have you here. People are going to really love it. We're going to show off in a few minutes.
593000	598000	Speaking of showing off, we've got Sidhir here. How are you doing, Sidhir?
598000	622000	Hey, I'm doing great. Nice to see you all. Super excited to be here. I run engineering at Site Machine and we work a lot with Microsoft and NVIDIA and all of the folks on the squad and others to bring together Site Machine and Microsoft and NVIDIA, integrating omniverse and bringing true value to our management.
623000	633000	That's awesome. Well, listen, I can't think of a better crew to tackle this topic today. So I think everyone should buckle up. I already see a ton of comments coming in.
633000	643000	That's fantastic. We're going to get to some of those in a second, but why don't we set the stage here, Ganesh and Sashi, for what we're going to really dive into here.
644000	661000	Sounds good. So what are we going to see in the next 15 minutes or so? We're going to actually talk about how digital twins are actually shaping and driving the next wave of AI, which is deeply rooted into physical AI.
661000	683000	And then we're going to actually talk about how Site Machine as an ISV has taken the technology from NVIDIA as well as from Microsoft and bringing that to shop floor and factory operations with the help of kinetic visions where they are actually providing a specific key ingredient in building the digital twin itself
683000	694000	and how the 3D assets are actually getting converted into USD and how that is actually driving and powering the whole digital twin physical AI transformation.
694000	698000	So that's what we're going to primarily cover in the next 15 minutes.
698000	704000	All right. Very cool. And I think with that, I think Sheru, are you going to kick things off for us here?
705000	706000	Yeah, let me...
706000	707000	Oh, go ahead.
708000	710000	Sorry, are you going to say something?
710000	712000	Oh, no, sorry. Go ahead, Sheru.
713000	714000	Yeah, go for it.
714000	715000	All right.
716000	727000	I wanted to talk a little bit about the specific manufacturing use cases that we're going to be solving with the solutions, because again, technology to solve real problems is what's very exciting to all of us.
727000	731000	So give me a moment so I can share my screen.
731000	737000	Okay. And while she's going to go ahead and sharing her screen, I definitely invite everyone to start posting your questions and comments.
738000	741000	We will be hitting those throughout the hour here.
742000	744000	And I think, Sheru, I see your screen is ready for me to share.
745000	746000	Yes, ready to share.
746000	747000	All right. Here we go.
749000	753000	All right. Like I just mentioned, we are an industrial AI data platform.
754000	759000	What we wanted to focus on today was what are the critical production challenges on the manufacturing shop floor.
759000	765000	We are seeing operations teams constantly under varying conditions trying to achieve a lot of outcomes at once.
766000	774000	These outcomes are typically things like line throughput increase, which is really how do I make, produce as much as I can and run as efficiently as possible.
774000	777000	Schedule adherence, how do I make sure I'm not behind on production?
778000	786000	How do I maximize my machine efficiency and maximize availability so that every machine is running close to its potential capacity?
787000	792000	So all of these problems are things that we've been solving for over 10 years already for manufacturers.
793000	796000	So that's sort of the landscape of challenges that customers are facing.
797000	809000	But with all of the changes in technology, AI and, you know, innovations coming out, what manufacturers are struggling with most are things like user experiences.
810000	813000	How do you make sure that all of these are adopted on the shop floor?
813000	817000	How do you make sure that everybody in the enterprise has the same view of the line?
818000	821000	And these are very complex lines and they're very large production lines.
822000	826000	If you actually go to these plants, you literally can't see a couple of machines away from you.
827000	831000	You can only see your machine in the next one, but you are impacted by what's happening on the rest of the line.
832000	838000	So understanding that system level, you know, process is extremely important and very challenging to do today.
838000	848000	So our vision, which is now being realized as we speak and being deployed with our clients is really about the power of 3D immersive visualization,
849000	853000	agentic AI for insights, as well as a real-time data platform.
854000	859000	So we have found that this combination of technologies really provides the visual elements so you can see what is happening.
860000	863000	Everybody has a unified view of the enterprise and the line.
863000	867000	Agentic AI generates recommendations under varying line conditions.
868000	875000	So it's adapting to changing scenarios and it's providing the recommendations at the point of consumption against the 3D layer.
876000	885000	So what you can see in this visual and what we'll be digging into and doing a deep dive on with the rest of our amazing team here is really how are these recommendations generated?
886000	892000	How are these visualizations available and how does all of this work together seamlessly?
893000	895000	They give a complete unified experience.
896000	906000	So this is just an example of some of the real-world results we're seeing with our clients and I just wanted to paint the picture for what our manufacturing teams are looking to do.
906000	916000	That's all I have slide-wise so I'm going to stop sharing.
917000	918000	Very cool.
919000	920000	And we have a quick video.
921000	923000	Did you want me to play that now or is that for a little later?
924000	925000	Sure.
925000	927000	I think, yes, I can speak for a minute as the video plays.
928000	929000	Edmar, that would be great.
929000	935000	I think it's really important to understand again what the complexities are on the shop floor.
936000	944000	So when you see these 3D views, you'll really understand and as the video plays, you can see that this is a full view of a bottling line.
945000	953000	Now, this is again, based on the scale, you can't really see how large it actually is, but you can see that in this 3D view, you can see the machine status and speed instantly.
954000	960000	You do not have to talk to operators and radio people or walk across the line or make decisions with incomplete information.
961000	968000	So again, the real focus is on providing those immersive 3D experiences with insights all in one space.
969000	974000	So the goal of this is to turn everybody on the shop floor team into a data-driven decision maker.
975000	976000	Amazing.
976000	977000	Very cool.
977000	979000	That's so helpful to have that visualization.
980000	984000	Obviously, there's a lot of opportunity for companies to leverage this kind of stuff.
984000	989000	I guess one thing I always think about when I see something that looks so amazing like this is the work involved.
990000	995000	I think sometimes people, companies might look at this and be a little overwhelmed or, oh my gosh, what's involved in that?
996000	1001000	But I think it's fair to say that people could take a very layered or phased approach to these kinds of scenarios.
1002000	1005000	You don't have to go all in with everything at once.
1006000	1010000	I'd be curious to see what you guys all think about suggestions for companies maybe watching that.
1010000	1015000	What would that layered approach look like or fees for getting started?
1017000	1025000	I think we can answer that real quick, Edmar, and then as you see what kinetic vision and site machine and the Microsoft team have done here,
1026000	1027000	it'll unpack all of those.
1028000	1033000	But I think the key ingredient starts with how the customer thinks about a brownfield operation.
1033000	1039000	So the example that Charlie was talking about is an existing operational bottling line.
1040000	1042000	They already have systems and processes in place.
1043000	1046000	Some of them have been actually deployed over several years.
1047000	1060000	So it's part of that whole digital transformation journey where you need to actually deploy more advanced and more latest technologies to harvest that data from the shop floor,
1060000	1062000	which is what site machines systems do.
1063000	1068000	And then you need to bring that into the cloud, which is what we're going to talk about in a second with the Microsoft architecture.
1069000	1077000	And then you start applying more advanced digital twin technologies with what we were showing earlier with omniverse and kid app streaming, which is what Sashi is going to talk about.
1078000	1088000	So there are existing systems in place and then there are add-ons that is required to harness that data sets to really drive the digital transformations and building the digital twins.
1088000	1091000	So we'll get into those in a second.
1092000	1096000	I think with that, we're probably going to hand over to Drew and Sashi to really unpack that architecture.
1097000	1098000	Amazing. Thank you, Ganesh.
1100000	1101000	Okay, so Drew.
1102000	1103000	Okay, Drew is fixing his microphone right now.
1104000	1106000	So we'll let Sashi take the start here.
1107000	1108000	Sure.
1109000	1113000	Yeah, so as Drew is fixing his microphone, I think, Drew, are you able to share with me?
1114000	1115000	Oh, you're on man, that's great.
1116000	1119000	The slide that you had on our architecture.
1120000	1128000	Yeah, so Sashi, I'll just show the initial thing we did back in the night first and then I'll pass it to you. Is that cool?
1129000	1130000	That's great.
1131000	1132000	All right.
1133000	1142000	So Microsoft and NVIDIA have been working together very closely for a while now to try to come up with a solution that combines the two platforms.
1143000	1146000	And a solution that's also scalable, right?
1147000	1155000	So at Microsoft, we do have this facility in Houston where we build hardware, pull out with PLCs.
1156000	1162000	What you're looking at right now is a fluid process machine, which has pumps, valves and whatnot.
1163000	1165000	And you can see the omniverse with the 3D context.
1165000	1168000	You can see on the left the data from fabric.
1169000	1171000	That's actually an embedded PBI in a single pane of glass.
1172000	1193000	And so we created this, demoed it at Ignite with kind of an inspirational architecture for the future, which the whole purpose is to inspire companies like Sight Machine to take a hold of and kind of have like a base template where Microsoft and NVIDIA are like, yep, this is a good approach.
1193000	1202000	And it gives folks confidence that this is the right way to go when doing like this is an operational use case view.
1203000	1210000	So essentially then, so post Ignite, we posted an open source accelerator repo.
1211000	1216000	And then Sight Machine got involved and worked with us and they took it and ran with it.
1217000	1224000	And then went crazy cool in reality with the Coca-Cola consolidated bottling line.
1225000	1231000	And let me pass it to Sashi right after I go to the architecture.
1232000	1233000	So this is the architecture and Sashi take it away.
1234000	1235000	Yeah, so thanks, Drew.
1236000	1244000	So what we're showing here is a design pattern, if you will, or architectural design pattern for creating an operational digital twin.
1245000	1246000	Now, this is an exemplar.
1247000	1253000	It's not meant to be like the end off, but it gives you an idea of what to consider as you're trying to build out a capability.
1254000	1261000	And I know in chat there's questions about like, how is this effective in terms of auto realistic renderings for users and so forth.
1261000	1262000	We'll get to that.
1263000	1267000	Let me walk you through this and then we can start answering some of those concerns too.
1268000	1275000	Fundamentally, when we start thinking about these kinds of systems, where we start is usually in the data, right?
1276000	1281000	And so that's on the left most side of this where you have edge computing, pulling in data.
1282000	1289000	You're going to take that data and you're going to have to do some kind of processing and staging of it into an environment.
1289000	1293000	In this case, we're using things like Azure IoT operations.
1294000	1297000	We're using Arc enabled Kubernetes to do that kind of stage.
1298000	1302000	Additionally, you'll have some level of 3D scene creation, and that has to also get managed.
1303000	1307000	So in our case here, we started putting that together with Azure Blob Storage.
1308000	1312000	These two pieces of information need to get correlated.
1313000	1318000	And that's where we're correlating it with Omniverse Kit and with Microsoft Fabric and Power BI.
1319000	1327000	So Fabric, Real-Time Intelligence and Azure Functions will take that data that we just received, convert it into more actionable data,
1328000	1331000	convert it from, you know, bronze data to silver and gold data.
1331000	1341000	And then the Power BI elements in block two will start overlaying that data into a user interface for a user.
1342000	1346000	That's getting combined with the Omniverse Kit app streaming, the photo realistic rendering.
1347000	1350000	Now, the big question that most people will have is, well, okay, you've got these two streams.
1351000	1356000	You've got this 3D enrichment or 3D data and you've got this IoT data, but how do you connect that?
1357000	1359000	And that's where it's kind of powerful.
1359000	1361000	We put in enrichment into the USD.
1362000	1369000	So the USD files that we create in Azure Blob Storage with the 3D data, we enrich them with ID information and other metadata.
1370000	1372000	I sometimes refer to that as syntactical sugar.
1373000	1377000	And that's what's brought in into Omniverse Kit and provides the front end.
1378000	1384000	And the front end is able to then map between the data sources coming in from Fabric to the data source in Omniverse
1384000	1389000	and give a user an immersive 3D environment plus the dashboarding effect.
1390000	1392000	So why, right?
1393000	1394000	Like, okay, this is great.
1395000	1400000	And one of the questions is usually, so you can do in 3D, but how does this really help?
1401000	1408000	Now, if we can imagine in the world, as we're progressing into decision making, we want engineers, users and developers
1409000	1418000	to be able to quickly contextualize the space that they're in and be able to solve problems in that space.
1419000	1428000	Fundamentally, if you've got an area in a factory or you have a stoppage or a blockage and you want to understand where that is,
1429000	1430000	what's the context around it?
1431000	1436000	Maybe you're looking at understanding, okay, what's the ingress path to get to that location to service something?
1436000	1438000	Or what do I need to change?
1439000	1440000	You need an immersive 3D environment.
1441000	1447000	The photo realism part of it helps you in understanding both what you might do as a human operator,
1448000	1454000	but also what you might do downstream when you're doing things like computer vision or other AI genetic kind of workflows
1455000	1458000	for simulation purposes, for training, what I've been so far.
1459000	1464000	So this is kind of like the starting point in which you can then have downstream use cases.
1464000	1473000	And the starting point then becomes the same single pane of glass that you would use for both operations and for simulation environments
1474000	1475000	and what if analysis and so forth.
1476000	1483000	With that, I'd love to hand it over to Jeremy and he can talk through how we created the digital twin.
1487000	1488000	It's great. Thank you, Sasha. It's awesome.
1488000	1493000	Jeremy, looks like you have the floor now. Are you ready?
1494000	1495000	I'm ready.
1496000	1497000	No pressure. No pressure.
1498000	1499000	No, so let's see here.
1500000	1501000	Edmar, I've got a couple of things. There you go.
1502000	1503000	All right, you're reading my mind here.
1504000	1505000	So let's start with this.
1506000	1516000	This is really just a basic representation of what we do to create the beautiful 3D asset that is inside
1516000	1519000	of factory operate or inside of site machine.
1520000	1523000	And all I do is just, you know, we have a lot of people on this call.
1524000	1525000	They're going to be at varying levels of knowledge about all this.
1526000	1529000	I saw some great questions about, you know, how do you create these assets?
1530000	1531000	What are the steps?
1532000	1533000	And I'm going to take some time to go through that.
1534000	1541000	But, you know, Edmar, you asked one very interesting thing about, hey, how does a company take like a layered approach to this?
1541000	1549000	And because we are, you know, integrating these solutions, we meet our customers where they're at.
1550000	1551000	And that's really important.
1552000	1557000	And so they may not be ready for a full blown site machine digital twin.
1558000	1559000	They may need something a little more basic than that.
1560000	1563000	So we developed this really simple process called the choir activate optimize.
1563000	1572000	And then, and then at the end, you know, collaboration through an amazing platform like omniverse really brings all of your stakeholders together.
1573000	1577000	So from the choir standpoint, we're just, we're just talking about scan data, get your data.
1578000	1579000	Most companies do not have a hold of their data.
1580000	1587000	Activate it using real simple software and then optimize it using data analysis that site machine provides or simulation.
1588000	1598000	And then in the end, when you have all of this fantastic compute available, you can immerse yourself and really create a connected experience with all of your users.
1599000	1606000	And so I also saw a question about, you know, how do you know when to use like a really high fidelity 3D model versus really basic representation?
1609000	1611000	You know, I always err towards immersiveness.
1612000	1617000	If you can do immersiveness without friction, then the human, we are humans.
1618000	1619000	The human experience is going to be better.
1620000	1621000	You're going to be inside the digital twin.
1622000	1626000	So the more that you, where you get into issues is where you have, you know, lots of data.
1627000	1632000	It's complex or you, you know, can't display it very well or there's a lot of friction and understanding it.
1633000	1637000	But if you can without friction err towards immersiveness, you're always going to be in a better spot.
1637000	1642000	Sometimes you need a little bit of visionary leadership there to, you know, kind of push an organization that direction.
1643000	1644000	But that's where we always tend towards.
1645000	1650000	So, you know, I have a really simple video that makes this stuff look simple and feel simple.
1651000	1652000	It's, I haven't labeled this video one.
1653000	1654000	I don't know if you can flip over there real quick.
1656000	1657000	Let me see. I'm looking at it. I see.
1658000	1659000	Oh, yeah, I do see it. Let's see.
1660000	1661000	I got three. I got all kinds of content.
1661000	1664000	Even small delays can snowball into big disruptions.
1664000	1669000	That's where digital twins come in with our acquire, activate, optimize process.
1670000	1672000	It's fast, easy and low risk.
1673000	1681000	We start by scanning your facility in stunning detail up to 10 times faster than traditional methods with zero disruption to operations.
1682000	1695000	That scan becomes a powerful 3D digital twin, enabling virtual design, remote tours and supporting system updates, layouts, training and safety planning.
1696000	1702000	In just 60 days, we uncover eight to 10 times first year ROI with payback in less than three months.
1703000	1711000	AI delivers smart insights and actions to drive fast solutions and improve overall operation effectiveness.
1712000	1714000	Dashboards track performance in real time.
1715000	1724000	AI flags a labeler jam, triggers a fix and recommends a second forklift and dual labeler to boost material movement and throughput.
1725000	1726000	Have a problem or want to try a change?
1727000	1728000	Simulate it first.
1729000	1730000	No risk, no downtime.
1730000	1733000	Your digital twin drives smarter decisions.
1734000	1742000	Once it works in one facility, it scales easily, making digital twins the perfect solution to transform your operations.
1744000	1749000	Okay, Edmar, that was marketing glitz from our marketing department.
1749000	1750000	Makes it look simple.
1751000	1754000	Well, I gotta tell you, there was a couple of pieces of information that were super compelling.
1755000	1759000	The ROI of three months, that's pretty amazing.
1760000	1764000	And also the granularity of detail, down to five millimeters.
1765000	1766000	It's pretty wild.
1767000	1770000	Yeah, so can you queue up, there's something called Video 2 in there?
1771000	1775000	So this is going to be a little more like, what does it look like when you do this stuff?
1776000	1780000	Like, who's doing what? When are they doing it? What programs are they using?
1780000	1785000	So just queue that up, that'll be another minute, and then I'll talk a little more after that.
1786000	1787000	Okay, here we go.
1788000	1791000	And there's no audio on this.
1792000	1799000	So really all we're showing is just a case study here of taking this acquire, activate, optimize process to a real facility.
1800000	1801000	This is a distribution center.
1802000	1803000	So Brian's out there scanning it.
1804000	1806000	He looks a lot like that little iconography we made of him.
1807000	1813000	We use real, we take that data, grab that 3D data, and then we use some other NVIDIA partners.
1814000	1821000	This is Preview 3D on this particular project to really quickly get measurements, get panos.
1822000	1825000	This is a simulation tool that we happen to use called FlexSim.
1827000	1832000	And just, you know, once again, an alternate process, not related to what we're doing with site machine here,
1832000	1835000	but another method to optimize the site.
1836000	1840000	Taking those, we're actually doing virtual camera simulation here just to make sure,
1841000	1846000	even stuff that's as simple as get your camera set up in the right place before you install them, that can be all done virtually.
1847000	1850000	And it saves a lot of time and it saves big mess ups.
1851000	1856000	So just a couple of visuals of a recent case study, that's a real project we did for a real customer.
1856000	1861000	And we did deliver, that was like a three month payback.
1862000	1865000	And there's some big decisions being made off of what we found digitally.
1866000	1868000	The biggest thing is not disrupting the operation.
1869000	1870000	That's kind of the biggest thing.
1871000	1873000	And then just queue up my PowerPoint if you wouldn't mind.
1874000	1875000	Okay, let me move this one first.
1877000	1879000	And then let me see, your PowerPoint is right here.
1880000	1885000	Yeah, I win the contest for most media presentations.
1886000	1887000	I love it.
1890000	1891000	Okay, we saw that, we saw that.
1892000	1893000	Okay, so how do we do this?
1894000	1898000	First of all, do not underestimate, you need some great people to do this work.
1899000	1904000	Kinetic Vision has a lot of amazing people, but we're talking about between site machine and Kinetic Vision.
1905000	1911000	We have data scientists, we have mechanical engineers, we've got machine learning engineers, we've got software engineers, technical artists.
1912000	1913000	It's a diverse team.
1913000	1923000	So when you're making decisions about building even just the 3D asset, it's really helpful to have subject matter expertise alongside your tech artists when you're building that asset.
1924000	1927000	So just a little bit about, you know, do not forget about the people.
1928000	1933000	AI is amazing, but we, at least for probably the next five years, we're still going to need people.
1935000	1940000	Okay, little nuts and bolts on like, what do you use to go do this?
1941000	1944000	We're, what we're doing, I'll look at the end here really quickly.
1945000	1956000	We're, you know, we're publishing a kit app, you know, kit app USD that's streaming within an omniverse, like visual context, and that's part of the site machine application.
1957000	1965000	And so what we're doing is we're delivering a USD asset that can deliver that high fidelity, fully realistic, interactive view.
1966000	1967000	What we start with is this 3D scan.
1967000	1969000	There's a lot of choices here, everybody.
1970000	1972000	We happen to use Naviz, Faro and Leica.
1973000	1976000	Naviz scanners are very fast, down to five millimeter accuracy.
1977000	1979000	You're doing a lidar scan, capturing millions of points as you go.
1980000	1983000	Faro and Leica are more terrestrial scans.
1984000	1990000	And so you're setting your, you know, you're setting your tripods and you're capturing data, but they're much more accurate and you get a lot of higher fidelity.
1990000	1994000	So we typically use Naviz for scanning a full site.
1995000	2007000	And then if we have particular machines where you really need really accurate user interface details and accurate, like mechanical details, we'll go in and re scan with like a Faro or Leica to get those high details.
2008000	2014000	From a reality capture perspective, we're taking those scans and we're activating them with the real simple off the shelf software that we integrate.
2015000	2017000	You have a few choices there too.
2017000	2019000	There's a big ecosystem out there.
2021000	2024000	We use preview 3D here at Kinetic Vision.
2025000	2029000	And let's see, we also use Reality Cloud Studio.
2030000	2032000	So these are two great programs.
2033000	2042000	There's a handful of them out there, but what's nice, we work with mostly big companies and a lot of them prefer to procure software instead of using something open source, something that's supported.
2043000	2044000	But there's open source options also.
2045000	2055000	Then, you know, once we get that reality capture data, which includes really high resolution, panographic images, sometimes we're creating a mesh that's got materials applied to it.
2056000	2060000	We then pull that into a 3D digital content creation package.
2061000	2064000	Pick your package, 3D Studio Max, Blender, Maya.
2065000	2066000	There's a lot of great choices there.
2067000	2069000	We happen to use all three of these.
2069000	2072000	And then you're following traditional artist workflows.
2073000	2074000	You're either doing direct modeling.
2075000	2077000	You're using a model library and maybe bringing a model in.
2078000	2081000	You're referencing those scans for the geometry sizes.
2082000	2084000	You're perhaps re-topologizing some geometry.
2085000	2087000	And then you're building your assets in 3D Studio Max.
2088000	2089000	I'll put a footnote here.
2090000	2098000	There's a lot of exciting, exciting technology around the generative creation of these assets.
2099000	2105000	NVIDIA's got some great open source libraries out there that they're publishing with their research teams.
2106000	2107000	And, you know, they're worth checking out.
2108000	2118000	We're not fully using them in these workflows yet, but there's going to be a whole slew of software packages and workflows available around using generative AI.
2119000	2120000	So, you know, we're going to be using them for 3D.
2121000	2124000	And then, you know, once we have that 3D Studio Max or Blender asset,
2125000	2129000	in order to access it programmatically within Sight Machine,
2130000	2132000	we're, you know, grouping portions of that USB file.
2133000	2135000	We're making them available so they're triggered by a sensor.
2136000	2137000	We're setting camera views.
2138000	2142000	And so what we're doing that in is just a little application that we built called Data Vision.
2143000	2144000	It's built on the Omniverse SDK.
2144000	2150000	And it's really using those great resources from the Omniverse SDK to build 3D application.
2151000	2155000	This allows us to layer in some extra data that Sight Machine needs to hook up to their platform.
2156000	2158000	So, and that's most of it.
2159000	2161000	I'm sure there'll be some questions, but just to cap it off,
2162000	2168000	this is just one of an asset from one of our pieces, one of our recent projects with Sight Machine,
2168000	2175000	just showing these steps, going from 3D Point Cloud to Reality Capture Asset to Assimilation Asset
2176000	2182000	to a really beautiful photorealistic asset with animation done through the Omniverse SDK.
2183000	2185000	And that's what I got for you, people.
2186000	2189000	That was really cool. Show me. I didn't see that before. That was really amazing.
2190000	2193000	I know. Ganesh, you're always, yeah. Ganesh is like, he's like, you never show me anything.
2194000	2196000	Like, I just got to see the latest stuff. So, yeah, there you go.
2197000	2203000	I love it. That is so wild. I think, yeah, it's a lot of impressed people watching in the chat as well.
2204000	2209000	Very cool. Let me see. And I think I'm going to leave this on for me for one second.
2210000	2215000	And there we go. Okay, cool. That's wild. So, anyone who's just joining us, thank you.
2216000	2218000	Welcome to the stream. We're talking with Sight Machine, Microsoft Kinetic Vision,
2219000	2225000	and of course, folks from the NVIDIA team about how Agentec AI and digital twins are transforming manufacturing operations.
2226000	2229000	Be sure to stay active in the chat. We see a lot of questions and comments coming through.
2230000	2236000	We'll try to address those. But that was fantastic. Thank you so much for carrying us through that really nice journey, Jeremy.
2238000	2239000	You are welcome.
2240000	2245000	Okay. All right. So, of course, now that we've talked about scanning and creating USD,
2246000	2251000	I think we have our friend, Sudhir here, who's going to bring us into, to bring us home here, so to speak.
2252000	2256000	All right. Can you share my screen?
2257000	2260000	Yes, let me see if I can. Yeah, I think I got it right here.
2264000	2265000	Okay, we can see it.
2266000	2267000	Awesome. Awesome.
2268000	2278000	Thanks, everyone. Super exciting. So you guys saw how Shashi and Blue presented the reference architecture that was done at Ignite.
2278000	2287000	Super cool, right? Shado presented the use case that we are talking to customers about and how we are showing value with this ecosystem.
2288000	2298000	And then Jeremy presented how they take all these scans on the factory and convert them into meaningful USDs that we can then use.
2298000	2311000	I'm going to just put it together to show you how Sight Machine took all these pieces together and built an architecture that shows value to our customers with all of these pieces put together.
2312000	2320000	So here's the technical architecture diagram. It's a flavor of the reference architecture that you saw earlier.
2320000	2330000	I'm going to highlight some of the changes we did or how we added on our technologies to make this even more compelling for our job customers.
2331000	2341000	So first off to recap Sight Machine is a AI manufacturing data platform. We take data from all these data sources that you see on the left hand side.
2341000	2352000	We standardize them, convert them into standard data models, thereby enabling things like analytics, general AI, digital twins and so on.
2353000	2368000	So here you can see the first step going through the steps here is our factory connect application that Sight Machines application that runs on IoT operations as Shashi was mentioning in the Unable Kubernetes cluster.
2368000	2372000	This gets all the data and passes it on to the next step.
2373000	2389000	We also have this data powering the factory operate and factory build platforms which are Sight Machines proprietary platforms to process and model the data for use in the kit application as well as for further analysis and AI.
2390000	2397000	All of this is running on the Microsoft Azure ecosystem to deliver a scalable unified solution.
2398000	2405000	So let's look at each component of it by drilling down into each and see each aspect in more detail.
2406000	2415000	So first off the data ingestion piece, right? So factory connect ingest data. The first step is to ingest data from the edge.
2416000	2421000	We are able to ingest data from a variety of manufacturing data sources like PLCs, historians, etc.
2421000	2431000	Factory connect problems as we mentioned in the Arc-enabled Kubernetes cluster, which offers an extensible, composable view to represent the line.
2432000	2434000	So that's the first step.
2435000	2443000	The second step is we use IoT operations here that reduces the complexity to build the end-to-end solution.
2443000	2450000	IoT operations enables us to connect the cloud and edge using bi-directional messaging.
2451000	2461000	On the second piece here, we have the 3D scans of the factory. This is what Jeremy is talking about. These are created. You saw all the details. I'm not going to go into it again.
2462000	2476000	These scans are segmented into Assemblies, Machines, Components. All those are in the USD format, which is then loaded into Azure Blob Storage for consumption by us and by NVIDIA on viewers.
2477000	2484000	With this data and leveraging Azure AI services, Site Machine is able to provide effective insights.
2485000	2498000	Next up is the scalable cloud platform. On this cloud, once we have the data transferred from the edge to the cloud, Site Machine is manufacturing data platform.
2499000	2506000	This powers all the data to factory operate. All this runs seamlessly in Azure cloud and Microsoft Fabric.
2507000	2515000	IoT operations sends data to the cloud via Fabric Azure Event Hubs, where I even streams in Fabric.
2516000	2525000	This is where Site Machine is able to process the data and create those standardized data models that I talked about that represent the entire line end-to-end.
2526000	2538000	With this data and then leveraging AI services from Azure, Site Machine is able to provide effective insights like agentic recommendations, which we will look at shortly.
2539000	2550000	The third piece here is the omniverse kit extensions. We are leveraging NVIDIA omniverse kit app streaming, obviously.
2551000	2560000	Now that we have data from Site Machine's data platform, we built a couple of kit extensions to integrate with the kit app streaming application.
2560000	2571000	The first one as Shashi was alluding to takes real-time data from the edge as well as the model data from our factory build application to annotate the USD
2572000	2580000	so that we can get contextualized data for these twins that Jeremy is so beautifully generated. Example things like filler speed.
2581000	2591000	That gives you the context of each machine, each asset on the line layered on with meaningful data from the Site Machine application.
2592000	2599000	The second piece is the operate extension. That's the one that handles rendering of this contextual data.
2600000	2608000	Example, creating a billboard on top of the machine to show you the name of the filler, the attributes, how it's running, its status, and so on.
2609000	2618000	It also handles things like zooming into a particular asset, events on the UI, showing where a fault is.
2619000	2628000	It responds to data changes, like if the filler speed changes, you'll immediately see the change in omniverse, in our UI, and so on.
2628000	2638000	We see that in a demo or a short piece. It automatically syncs up. Everything is instantly available on the UI and in omniverse.
2639000	2646000	Events in the UI are in React and it's communicated to the kit application.
2647000	2654000	Now let's look at how the UI piece works. The last piece here is the seamless UI integration.
2654000	2664000	On the front end, Factory Operate is a React application. We embed the omniverse viewport into Operate using NVIDIA provided packages.
2665000	2671000	Every user then connects to a stream from the kit app to show this viewer in the UI.
2671000	2684000	In order to improve efficiency, we've implemented things like stream management to pre-created cash streams, creating the stream pools, showing instant availability in the UI.
2685000	2690000	Events in the UI are passed to Kit App via WebRTC and Y-Saver.
2690000	2704000	Anything that happens in the Kit application, you can have events reflecting in the UI and things the user interacting or data streaming from the UI can interact real time with the omniverse application.
2705000	2719000	The whole stream is contextualized to the View and React labels and other annotations in the stream provide a seamless view of the line with recommendations from agent.ai layered on top of it.
2722000	2724000	Very nice.
2724000	2734000	Okay, you got it. Okay, cool. That was fantastic. We got a lot of great comments as you were showing your presentation there.
2735000	2741000	Good questions too. Is there anything you guys want to adjust before we start tackling some of these questions?
2742000	2749000	I want to show up and then we can jump into questions. Give me one sec.
2750000	2757000	I see omniverse ambassador John Mitchell from BMW is watching today.
2758000	2768000	We're showing great interest in connecting with members of our panel here, which is great. BMW of course is doing amazing things in the digital twin world with their factories and robotics.
2769000	2772000	Okay, so do you guys see it right here, right?
2774000	2776000	Yes, that is correct.
2777000	2782000	Okay, awesome. So just show a quick demo, putting it all together.
2783000	2794000	Charo showed this briefly in the video. This is a live version of our factory operate application running with omniverse get up streaming embedded in our React application.
2795000	2799000	As you can see, this is an entire view of the line. It's a modeling line.
2799000	2811000	On the left hand side, you can see a bunch of, you know, metrics for the line. What's the machine efficiency, what's the filler speed, different fillers, production volume, what flavor is going through the line and so on.
2812000	2824000	And at the bottom of the screen, you can see all the assets and their individual attributes like the filler one, it seems like has a fault, filler two is running well, and so on and so forth.
2824000	2837000	And you can see the same data being leveraged and shown in the omniverse get up streaming as well as billboards, as I mentioned, using the web RTC cons that we talked about.
2838000	2844000	Now, if I want to look in detail, I can see, okay, this filler one has a fault. So let me draw down into that.
2844000	2856000	Now, and you can see we zoom in to the asset. And not only do we zoom in, we are able to highlight specific areas of the machine where the fault actually occurred.
2857000	2866000	So it looks like a conveyor jam. It reflects the red section is this is the place where the operator needs to focus on to fix the issue, right.
2866000	2880000	So this is super powerful. This is where we talked about how kinetic vision is helping us build the USD segmented into components so that we can leverage our data and pinpoint exact locations.
2881000	2890000	So not only do we give calls this we also give exact locations and recommendations for operators to immediately fix this on the shop floor.
2890000	2901000	Let me zoom back out a bit. The other thing you can see here with this purple screen is it says line is running well but not at peak efficiency. I'm going to take ways to optimize.
2902000	2913000	And this is where a genetic AI recommendation engine comes into play, right. So not only are you seeing like current statuses and basic ways to fix it.
2913000	2932000	You're also seeing things like hey, the filler speed should be increased from 630 to 700. That's what our agent to get I recommend just the optimal speed for the filler or something to do with the tackle to update their settings to accommodate the next set of packaging or a wrapper.
2932000	2942000	What is there to replace and similarly, you can now say hey, go to the rapper. Let me see what's going on. Hey, prepare to wrap replace the rap material.
2943000	2955000	AI has detected that you might run out of the rap soon enough so that's the time for your operator to go on set up the rap get it put it in place so that you don't have an issue you don't have stoppages.
2955000	2972000	You don't have down times. So very, very powerful leveraging of our data and leveraging of all the 3D assets that Jeremy showed the army was kept up streaming platform bringing it all together to show immense value for our customers.
2973000	2975000	I will stop right there.
2975000	2985000	Wow, amazing. I don't think anybody wants you to stop where everyone was blown away. You're going live and everyone can obviously you notice the time and date up up on the right. It was not faked.
2986000	2999000	Real time. That's pretty amazing. That's a great question. The comments in the chat. Do you any any more or less thoughts before we start tackling some of these questions. I know we've been discussing something in the background which to tackle.
3000000	3012000	Okay, alright, so let's go. We have we've got nine minutes, so which means we got we got a hustle. So let's try to let's try to keep our answers as concise as possible and we can refer people to our discord.
3013000	3026000	Amelia is going to set up a thread on our discord server specifically for this live stream. So whether you're watching this live or the replay, you can go to that thread and you could continue to ask questions, conversations and hopefully with other viewers will also go there too.
3026000	3042000	So you can chat with them on it. Alright, so let's do the little lightning round of questions here. I think earlier on actually we had a good question. I think for a first site machine so sure if you want to take this one about how do you approach a client engagement and when client stakeholders have different priorities.
3043000	3045000	Who do you prioritize and why.
3046000	3065000	Yeah, so the answer to that is we have to prioritize everybody. He actually worked with both it and we provide a complete solution without the agreement and agreement across these functions and now now we're also seeing a separate a function which really collaborates with it and
3066000	3076000	and we provide daily solutions to one in the enterprise. So we go all the way we address security, plant connectivity, as well as operational data and insights.
3077000	3087000	So the real answer is you cannot afford to prioritize one over the other because then you're not going to have a successful plant level transformation that scales.
3087000	3096000	Okay, very, very super helpful and just a quick note we had to wave goodbye to Sasha a minute ago he had none of the meaning you had to go do so thank you Sasha for always helping out great to have you here.
3097000	3104000	Okay, we also I'm looking so we have a couple of different chat channels where we're keeping track of some of these questions I'm looking at our internal chat here where I see a few things that were discussed.
3104000	3115000	Sure you just mentioned you saw a good question from Victor. Amazing collab curious with this foundation which type of simulation becomes more feasible high fidelity asset level modeling or broader plant level.
3116000	3119000	What if situational modeling did you want to tackle this one.
3119000	3133000	Sure, I'd also like to invite maybe so they're in others if they have any specific ideas just to talk about this but we are thinking a lot about what a situational modeling and scenario modeling.
3134000	3144000	Which means that if I have a change in raw material or if I add another machine to my line. How does the capacity change what will my line look like and what should I be planning for.
3144000	3152000	Ideally we help them make real decisions by seeing things like if you add another machine you can reduce all of your weekend shifts or eliminate them all together.
3153000	3164000	And again the focus is on providing broad scenario level guidance with with our simulations but if anyone has additional thoughts and everything I'd love to invite them as well.
3165000	3166000	Yeah, yeah.
3167000	3168000	I have one point to add as well.
3168000	3174000	Yeah, so it's sort of I would say both right like at the first step.
3175000	3197000	We're trying to get as accurate a representation of the models as possible right and then try to solve real problems like these agentic recommendations or what's happening on the shop floor and so on so forth and for that perhaps you don't need like the super super high fidelity still high fidelity as you could see.
3198000	3218000	But as as you were talking about but there are definitely use cases when you're looking at animations and like real time playbacks of you know how things happen and how it caused a fault or what have you where a more realistic representation is absolutely essential.
3219000	3224000	So both are different use cases and you know both are something we are looking at that to finish.
3224000	3242000	Yeah, so but what what you guys said is spot on but specifically on the asset level or simulation specific question that Victor is asking that we were talking about the bottling line use case the single most expensive asset that's there in the line is a filler and then of course the next one is a packer right.
3242000	3256000	So there are filler level simulations that can also be done to understand spillage inefficiencies things that are supposed to be followed by certain string guidelines of course you cannot stop a filler to run those scenarios.
3257000	3259000	So that's where simulation comes really handy.
3260000	3272000	So to think about the whole digital twin journey that we were discussing in the last few minutes is to break down into two separate journeys one is the whole operational digital twin and the other one is a simulation digital twin and they both kind of go hand in hand.
3273000	3288000	So the filler simulation is one of the examples where you could be thinking about fluid level simulations and the CFD style simulations as well where we were actually in track with a third party simulation provider that can run that kind of filler level simulations as well.
3289000	3300000	It's a it's a team play it's not like in a one size fits all and one partner provides all the different experience in the services you bring the right ingredients for the right kind of job to get the results.
3301000	3302000	The business results that you expect.
3303000	3304000	Great.
3304000	3305000	Okay that's super helpful.
3305000	3312000	And so this this gentleman or a gentle lady has asked us a couple of times in chat so I know they're eager to for this answer.
3312000	3323000	This is an interesting question obviously because we're talking about very developer kind of focused pipeline but we have a lot of creators out there in the world who really want to ramp up and and contribute to these challenges.
3324000	3337000	At C graph recently there was a session if Amelia has has a second maybe she can try to find that session I think the moment live the other day for everyone to watch now we had a session about as it's really as if you're a 3D content creator how do you upscale for the physical AI.
3338000	3341000	Does anyone have any any suggestions for Yash here.
3342000	3345000	I think I think this is a great question for kinetic vision and Jeremy do you want to take that.
3347000	3358000	Yeah so I actually saw this question I thought wow we're actually going to be answering some of the one of these questions so you know I the things that I laid out in my presentation.
3358000	3363000	It is a lot about tools curiosity and skill sets right so.
3364000	3380000	Really just you know for this type of simulated world we need these types of tools we're using Navis scanning we're using reality capture and then we're using what you know if you're if you're a creator you're already using this great set of 3D content content creation tools.
3381000	3388000	You know in Maya 3D Studio Max substance you know those are those are all the set of tools that you're using so.
3389000	3407000	I think really using the those tools and the guidance on how to get to that level simulated world I think is you know from from the from those software vendors is you know going to be really important but the other thing is the collaboration what I'm.
3407000	3415000	You know a lot of these questions what I'm hearing is they all relate to collaboration and communication.
3416000	3428000	These are big problems we're creating an entire simulated world so that means that we need all of the skill sets and expertise from each of the individuals that places something in that in that virtual world.
3428000	3443000	I'm sorry Ganesh I'm not sure when you learned about fillers but you know to make a good decision about how to make a filler run operate properly and it's optimal speed is probably not your your expertise you know enough to be dangerous.
3444000	3452000	But working with that person who either sold the filler to this customer or who operates the filler every day is really important.
3452000	3460000	And so you know you have these hard skills but the soft skills are just as important making sure that you are building that network of experts and you're collaborating them.
3461000	3471000	Collaborating with them as you build that simulated world I know this is a developer webinar and we want to hear about you know programming languages and programs but I cannot underestimate.
3472000	3484000	The this is where the I saw some you know I'll pivot here to I see things you know comments in here about well you know once we you know once we get rid of the humans where how do we you know manage the narrative.
3485000	3498000	Don't get rid of the humans that the humans like we're stuck doing so much BS now elevate the humans and continue to build those relationships use the relationships to build that simulated world OK I'm going crazy here.
3499000	3503000	I'm going to save that quote when I have an extra view with my boss so thank you.
3503000	3504000	Cool.
3504000	3505000	That's great.
3505000	3506000	You're out of here at March.
3506000	3507000	All right.
3507000	3513000	So speaking about programming languages about this question came in from LinkedIn about CUDA programming and the a models.
3513000	3514000	Don't want to tackle this one.
3516000	3521000	I don't know if we have a CUDA expert here but no I don't think I don't think it feels like a.
3521000	3522000	Yeah.
3523000	3524000	Yeah.
3524000	3525000	So we'll.
3526000	3527000	Sorry.
3527000	3542000	CUDA is foundational like every single thing that that you see that's built on top of machine learning and artificial intelligence that allows the GPU to accelerate it is built on CUDA every single every single operation is built on CUDA.
3543000	3550000	Nobody talks about CUDA anymore because it's so foundational you have to have it to make these operations move quickly on a video graphics card.
3550000	3559000	So you know it's really any any anything that you're using to accelerate the training of a neural network the inferencing of a neural network a scene graph.
3559000	3563000	Every single one of those operations is built on the CUDA libraries.
3564000	3568000	Great great information and may ask the panel here because I know we're at time.
3568000	3571000	Did you want to hard stop we take a couple of questions.
3572000	3573000	Okay great.
3573000	3581000	Okay here's one coming in from Jesus who is asking if we can describe their app streaming over Azure has experienced latency etc.
3582000	3589000	I believe the question means the Ocast the on US kid upstreaming that's available on Azure marketplace.
3590000	3591000	It is available.
3591000	3596000	It is a co-sale ready fully transactable marketplace listing.
3596000	3603000	So some of the work that we saw in this in this session is actually using that same Ocast.
3603000	3612000	So as a developer you can actually go to Azure marketplace today and get started follow the GitHub repo the same things at what site machine did.
3612000	3617000	So the work that site machine did was following that ignite repo that drew was talking about.
3617000	3624000	So that's the close partnership that we have between NVIDIA and Microsoft to showcase how to get started.
3624000	3630000	How you can actually take those containers the kid upstreaming containers deployed on your own.
3630000	3637000	Kubernetes clusters on Azure leverage the a 10s that are running on Azure commercials and get started.
3637000	3648000	So it all basically starts from that kid upstreaming and then build the entire solutions like what's it was talking about with that front end web applications and integrating it into that web app.
3648000	3650000	So it's it's all there.
3650000	3654000	It's all available on that marketplace and get a repo for that we release at ignite.
3654000	3661000	I think we should also admire you said that discord will have it so we should also share that GitHub repo as well for any developer to get started today.
3662000	3663000	Okay, we'll do that.
3663000	3665000	I think Amelia posted that thread in the chat a little earlier.
3665000	3667000	We'll also add to the description afterwards.
3667000	3669000	We got another question here from LinkedIn.
3669000	3673000	Alan, this is probably great for everybody, but definitely Ganesh.
3673000	3684000	This is, you know, obviously when when when we have business folks from different companies watching these kinds of topics and they realize, you know what, I should take a look at omniverse or take a look at open USD.
3685000	3686000	What would you say, Ganesh?
3686000	3693000	What what's what what is your pitch to have these have these leaders actually take a serious look at adoption.
3693000	3699000	So I think I think it's all I believe channel kind of laid this out at the beginning of the session.
3699000	3707000	It all starts with the the KPI, the business KPI and the real like immediate material impact.
3707000	3717000	So the modeling line use case that Charlie was talking about, there was an operational efficiency gain or throughput improvement or yield improvement.
3717000	3720000	That's where that's where this entire kind of journey started off.
3720000	3729000	So try to understand how what that KPI would look like and it can be it can be related to going back to what Jeremy was talking about.
3729000	3734000	If you take the filler simulation example, there are subject matter experts who have been doing that for decades.
3734000	3742000	So but if they are looking at a specific business KPI that they want to hit through a simulation scenario, then then start with that.
3742000	3751000	See where all of these new new technologies and the digital transformation, the digital twin, the simulation workflows are going to actually cause an impact.
3751000	3766000	In case of psych machine, they zeroed in on an OE improvement, took their platform to Microsoft and then media technology took the help from kinetic vision and showcase that example with a double digit operational efficiency improvement.
3766000	3770000	If I remember right, Charlie, I think it was like a 10 to 15 percent.
3770000	3781000	You guys were targeting like one to two percent and that itself was like a big impact, both top line and bottom line, but they were able to showcase a double digit impact of 10 to 15 percent on top line and bottom line.
3781000	3786000	Or I wouldn't say top line of bottom line, but the operational efficiency gain that has impact both top line and bottom line.
3786000	3788000	So that's that's where it starts.
3788000	3795000	It always starts with the business value and the KPI and then start with that one use case.
3795000	3798000	Look at your existing stack of technologies that you have.
3798000	3807000	See what the gap exists, adopt the technologies to the right level rather than like throwing everything out of the window and start from scratch.
3807000	3808000	That never works.
3808000	3817000	It's way too expensive and then see the incremental gain and then keep expanding from there from one line to multiple lines from one factory to multiple factories and see whether that really sticks.
3817000	3820000	So that's that's how I think I would think about.
3820000	3825000	I'm open to other inputs and ideas from folks here.
3825000	3829000	Oh, I've always got a hot take.
3829000	3839000	So, you know, for organizations when you're thinking about your omniverse investment, you know, omniverse is an, you know, an entire ecosystem.
3839000	3842000	So you can engage it in different ways.
3842000	3853000	This engagement through an ISV like site machine is actually a very low friction engagement because your organization probably already has Azure resources.
3853000	3857000	And now that Microsoft has made this capability available through Azure.
3857000	3860000	Now this is just a SaaS purchase.
3860000	3866000	You're just calling up a company like site machine and you're saying, Hey, I would like to use your software to see my game.
3867000	3873000	And then you're, and then it's utilizing either the SaaS platform or your cloud Azure private tenant.
3873000	3878000	And that's a very low, that's a very low friction way to invest in omniverse.
3878000	3884000	You may be a different type of organization and say, Hey, we have a very unique manufacturing process.
3884000	3888000	A site machine doesn't meet all of our requirements.
3888000	3894000	We want to build our own applications to be a technology leader in our space and lead everybody else.
3894000	3904000	That's where then you would make the type of investment with omniverse via omniverse cloud or OBE to bring your developers and actually have them building their own applications.
3904000	3913000	If you're an organization who can build your own applications and you want to be a leader in your technology, then that's how you would invest in omniverse.
3913000	3915000	And that's like a strategic decision.
3915000	3923000	Are we a developer of applications here within our company or are we a procurer of ISV applications to solve our problems?
3923000	3928000	And that's a key decision point between leaders. Are you developing or are you procuring software?
3928000	3931000	That's great, great, great context. Amazing.
3931000	3933000	Okay, we got another question here.
3933000	3939000	And actually, Jeremy, enter it quickly if you can, but I think you covered this earlier.
3939000	3946000	How helpful is photorealistic effects when used by GUI engineers or technicians in practice?
3946000	3951000	Yeah, I mean, I think about this as communication, right? Who are you communicating with?
3951000	3961000	The more photorealistic your output, then the broader your collaboration is with all in your organization.
3961000	3969000	Yes, if you're engineer to engineer, you may not need photorealistic effects to be able to solve a certain problem and change a variable.
3969000	3983000	But if you are making a big investment and you need to communicate to leaders on opening up the budget to go ahead and make some huge cost savings for the company,
3983000	3987000	you probably do need some kind of photorealistic output.
3987000	3991000	And people, NVIDIA is going to give it to us for free, right?
3991000	3994000	I'm not for free. You got to buy GPUs, okay?
3994000	3998000	But the highly photorealistic effects should all be free in the end.
3998000	4002000	Yes, there's some friction now, but that friction is going to decrease over time.
4002000	4006000	Everything, you know, compute becomes cheaper over time.
4006000	4014000	And so I do think it's very important, especially in creating these connections through your organization to get real work done.
4014000	4023000	And the photorealism to Jeremy's point, today it feels like it's a human-to-machine interaction,
4023000	4028000	but the future is actually towards massive amount of automations through physical AI.
4028000	4031000	It's a lot of machine-to-machine training.
4031000	4037000	So for a robot to really train and learn, it needs that physical accuracy.
4037000	4040000	And that's why photorealism is super important.
4040000	4045000	And when we're building assets, Jeremy and I have had this conversation many times.
4045000	4049000	When we're building these 3D-USD assets, today it's about operational twin.
4049000	4053000	But really, that's just the stepping stone.
4053000	4057000	The real goal is to drive through that entire journey of operational simulation
4057000	4061000	and ultimately for physical AI and robotics, right?
4061000	4064000	And when you're thinking about that journey, you really need that photorealism
4064000	4067000	and really high-fidelity, accurate USD assets.
4067000	4071000	Yeah, we should do another hour on synthetic training data for vision systems.
4071000	4072000	Exactly.
4072000	4075000	Yeah, that is really important. That's the technology side.
4075000	4078000	I'm always trying to cover the technology side and the human side.
4078000	4083000	So sorry, I shouldn't have left that one off. That's like a huge, huge thing.
4083000	4085000	Well, this is an unrelated note.
4085000	4091000	Curious how one decides optimal resolutions of point clouds that can be fused with realistic data.
4091000	4095000	Do you have any thoughts on this one?
4095000	4098000	Yeah, these are all...
4098000	4103000	It's like what's available and fast, right, and can solve the problem.
4103000	4108000	It's a multi-variable solution and it's always changing over time.
4108000	4115000	So we've chosen a certain type of resolution because we're talking about, I'll say,
4115000	4120000	human-scale, facility-scale decisions, right?
4120000	4125000	We also operate three North Star Industrial CT scanners here.
4125000	4127000	So that's a completely different technology.
4127000	4129000	It scans down to the micron level.
4129000	4139000	We can detect the porosity and the femur of a mouse or a void in a solder pad on a BGA.
4139000	4144000	So you really need to use the right technology for the job,
4144000	4146000	but it's going to be some mixture of those things, right?
4146000	4150000	The expense of the technology, the value of the problem you're solving.
4150000	4153000	But if you have questions, hey, you know who you can call.
4153000	4155000	Yeah, that's great. Well, thank you.
4155000	4159000	It's fascinating. I never thought of a mouse femur before.
4159000	4165000	So Rich is asking on LinkedIn about, is this workflow available with academia?
4165000	4167000	What do you mean, of course? Why not?
4167000	4172000	We do have an HDR team here actually at NVIDIA that I can help put you in touch with
4172000	4175000	if you have specific questions about your school working.
4175000	4177000	But Ganesh, I don't know if you have any other thoughts on this.
4177000	4179000	No, this is great.
4179000	4182000	Thank you for hosting it. Thank you for inviting everybody.
4182000	4185000	And thank you Psych Machine for an awesome work.
4185000	4191000	I know we had a really short ramp of getting all of this integrated up and running in six months.
4191000	4194000	Thank you Kinetic Vision for an incredible partnership and support.
4194000	4198000	You guys have been awesome with NVIDIA for over the years and of course for Microsoft.
4198000	4201000	So thank you Drew and the entire team.
4201000	4206000	What we started off like a year ago, it's really like blossoming and this is just the beginning.
4206000	4208000	We're going to grow even more.
4208000	4212000	And Ed Mar, I hope you will invite us again with more exciting things.
4212000	4216000	Of course. I always like to ask this as a last round.
4216000	4218000	Ganesh, you just went. So you passed this.
4218000	4222000	But does anyone have any last kind of words of wisdom that you want people to really remember?
4222000	4225000	What's your key takeaway you want people to come away with?
4225000	4227000	We'll start with, we'll go backwards actually.
4227000	4232000	So Drew, what's your key takeaway here if we want people to remember?
4232000	4237000	Key takeaway. NVIDIA and Microsoft have a beautiful relationship.
4237000	4244000	And we're also trying to design industrial standards together as well.
4244000	4252000	So just stay in contact with us and hopefully it gives you confidence to build whatever you all dream up.
4252000	4254000	So that's what my takeaway is.
4254000	4257000	Amazing. Great words Drew. I'm glad you went.
4257000	4261000	Sheru, you're next.
4262000	4267000	Oh, okay. Yeah. So I think the key takeaway I'd love for everyone to remember is, you know,
4267000	4276000	the domain specific intelligence and architecture that Sight Machine provides is critical for manufacturers to actually deliver real value.
4276000	4279000	And that can be a great framework for other verticals as well, right?
4279000	4285000	Understanding the entire technology stack that's necessary for AI and data transformation, I think,
4285000	4287000	is becoming much more fascinating.
4287000	4296000	But you know, understanding each layer and what we provide in addition to Microsoft and NVIDIA platforms would be a great thing to keep in mind.
4296000	4299000	Great words, Sheru. Thank you.
4299000	4301000	Sudhir, what's on your mind?
4301000	4308000	Yeah, I'll channel Jeremy a little bit and say this, you know, sort of the two modes come to mind, right?
4308000	4319000	Like this partnership and what we've achieved showcases what can be done with the power of omniverse and Microsoft Azure and Fabric, right?
4319000	4332000	And if somebody who's like really wants to put on a developer hat and play with it and build these technologies, like this is the, you know, sort of blueprint on how it can be done and how it has actually been done.
4332000	4344000	That said, as Sheru mentioned, a lot is based on data and the domain expertise and so on, and that's where Sight Machine comes in.
4344000	4360000	And so if you have looking for a solution that offers end-to-end with the data, with all the integrations in place and has done the heavy lifting for you, please feel free to contact us and we have the solution for you. Thank you.
4360000	4365000	Amazing. Okay, Jeremy, you have the last word here.
4365000	4367000	I'm afraid.
4367000	4382000	Yeah, well, so in manufacturing and supply chain, most people's operations run around 50% of the nameplate of what it was designed for, okay?
4382000	4389000	At every big company, these are billion-dollar problems and they're only going to be solved digitally.
4389000	4396000	So if you're looking at like, how much does this cost? Should I do it?
4396000	4403000	Yes, the answer is yes. It's 10 times faster and cheaper doing it digitally than going in and doing things manually.
4404000	4412000	So the biggest issue is not the cost. The biggest issue is not the technology. The biggest issue is a people problem, okay?
4412000	4421000	This is about multidisciplinary experts understanding all aspects of the problem. It's about having a vision.
4421000	4430000	I love that question. What do I do about IT and manufacturing and them being misaligned? Get them aligned, people. They have to be aligned to solve these problems.
4430000	4437000	So much of what we're talking about here today, this is amazing. NVIDIA has done an amazing job of making sure that technology is available.
4437000	4443000	Fantastic. Developers and ISVs like Sight Machine have made sure that they use that technology and put it into products.
4443000	4447000	And then, you know, Kinetic Vision is helping customers solve problems every day.
4447000	4456000	But what I'm seeing on the front lines is people issues and companies really coming to grips with how to solve these problems in their organization.
4456000	4461000	So try to build those relationships. People get them in front of the right stakeholders.
4461000	4467000	Make sure that you're supporting, you know, your visionary leaders, making sure your visionary leaders are working with their visionary developers.
4467000	4474000	It's a big people challenge, but I know that we'll be able to get there. So thanks again.
4474000	4480000	That's awesome. I'm so glad. That was a great closing remarks. I want to thank all of our amazing guests from Sight Machine, Microsoft, Kinetic Vision,
4480000	4486000	and of course, my colleagues here at NVIDIA for sharing your amazing insight. Each one of you has been extremely helpful.
4486000	4493000	We've seen how Agentec AI, OpenUSD, and Jill Twins can help factory teams spot issues faster and optimize production lines.
4493000	4498000	And it seems like this is just the beginning of what's possible. So a big thanks to all of you who joined us live out there.
4498000	4501000	Great comments and questions. We really love the projects that we shared.
4501000	4506000	If you missed part of today's session, don't worry. The replay is available using the same link you're at right now.
4506000	4510000	Or just go to our YouTube channel anytime, NVIDIA Omniverse. Go to live and you'll find it there.
4510000	4515000	And as I'm showing on the screen now, this kind of experts presentation is valuable to you.
4515000	4522000	I highly encourage you if you can make it to GTC DC. So our first one, Washington DC, you're going to see Jensen there providing a great keynote.
4522000	4528000	So I hope everyone there can make it. I will be there. So let me know if you're going and we can grab a coffee or something together.
4528000	4531000	Until next time, thank you so much for joining us, everybody.
4531000	4536000	It's been an absolute honor to join the panel with you all today and with you all there in the audience.
4536000	4538000	Have a great rest of the day.
4538000	4539000	Thank you so much.
4539000	4540000	Thanks.
4540000	4542000	Thank you.
