1
00:00:00,000 --> 00:00:28,000
Hello, everybody. Welcome to the OpenUSD Insiders livestream. I'm very honored to have you join us today. We have a very special episode with some amazing partners.

2
00:00:28,000 --> 00:00:34,000
We're going to dive into that topic in just a second, but we're going to be covering building an authentic AI powered digital twins.

3
00:00:34,000 --> 00:00:42,000
We would love to invite your questions, your comments. Let us know where you're watching from. If you've got any interesting projects related to this, we'd love to hear about it also.

4
00:00:42,000 --> 00:00:48,000
So go ahead and introduce yourself in the chat right now. We're going to hit the questions. There are probably a few different points during this hour.

5
00:00:48,000 --> 00:00:53,000
So if we don't get you right when you post, we're trying to keep track of them. We'll hit them by the end, most likely.

6
00:00:53,000 --> 00:01:02,000
OK, so thanks for joining us. So right now, I'd like to also very attention to this great learning path we've got here. Brand new new digital twins for physical AI.

7
00:01:02,000 --> 00:01:11,000
We just came back from SeaGraph. A lot of excitement on this on the subject, including at labs and sessions. This QR code will bring you to that brand new learning path.

8
00:01:11,000 --> 00:01:25,000
For those of you who've been watching for a while, a part of the community, you all know that these learning paths are a fantastic free resource, self-paced courses, basically, on a various number of topics, including OpenUSD and robotics.

9
00:01:25,000 --> 00:01:36,000
And now we have digital twins for physical AI. So use that QR code to find it. If you need the link, we'll also put it in the chat, but that is a super helpful resource for all the developers out there.

10
00:01:36,000 --> 00:01:45,000
Here's another great resource, developing and deploying your own omniverse kit apps. This QR code will also bring you the latest information there.

11
00:01:45,000 --> 00:01:57,000
Another great resource for leveraging the community on deploying your own kit app is our Discord server, where we have developers talking about this very topic pretty much on a daily basis.

12
00:01:57,000 --> 00:02:01,000
So engage with other community members there. We'll post a link in the chat.

13
00:02:02,000 --> 00:02:11,000
Amazon Devices and Services achieves major step towards zero-touch manufacturing with NVIDIA. This is also an amazing story that just came out. This QR code will bring you right to there.

14
00:02:11,000 --> 00:02:21,000
If you have any questions on this, feel free to pop on over to the live streams channel. We have our Discord server. Feel free to tag me, and I'll get you someone who can help in the chat on this.

15
00:02:21,000 --> 00:02:27,000
Developers build fast and reliable robot simulations with NVIDIA omniverse libraries.

16
00:02:27,000 --> 00:02:35,000
As you can see, we're all about QR codes today. So here's another QR code for you. That'll bring you right to the link.

17
00:02:35,000 --> 00:02:42,000
And obviously, if you're watching this live, you will get these links in the live chat also. No need to bring your phone up to the screen unless you want to.

18
00:02:42,000 --> 00:02:48,000
You're free to, of course. But feel free to check out that resource. We'll be happy to also help you with that.

19
00:02:48,000 --> 00:02:53,000
And how to instantly render real-world scenes in interactive simulation. Another great article.

20
00:02:53,000 --> 00:03:05,000
These articles are actually a great resource for people because they leverage not only our NVIDIA developers, but also members of partner teams, members of the community.

21
00:03:05,000 --> 00:03:16,000
Really great use cases and workflows from start to finish. So highly encourage you to check out each of these, but this is how to render real-world scenes in interactive simulation.

22
00:03:17,000 --> 00:03:24,000
NVIDIA opens up portals to world of robotics with new omniverse libraries, Cosmos physical AI models and AI computing infrastructure.

23
00:03:24,000 --> 00:03:32,000
Cosmos is an amazing world foundation model we've been talking about a lot lately. This will help give you more insight into how to leverage that to the fullest.

24
00:03:32,000 --> 00:03:39,000
Again, at Seagraph, we just wrapped up a couple weeks ago. It was all about physical AI, robotics, Cosmos.

25
00:03:39,000 --> 00:03:43,000
It really showed a nice transformation in what's happening in the graphics industry.

26
00:03:43,000 --> 00:03:48,000
So feel free to leverage that QR code to get right to that article.

27
00:03:48,000 --> 00:03:57,000
And another exciting thing that also was debuted at Seagraph was our open USD certification.

28
00:03:57,000 --> 00:04:03,000
For those who attended Seagraph in person, you were able to actually take the certification exam for free.

29
00:04:03,000 --> 00:04:09,000
Otherwise, there's a small cost attached if you are taking it online and here's the QR code for that.

30
00:04:09,000 --> 00:04:17,000
I highly recommend anyone out there who's been floating around to open USD for a while, go ahead and challenge yourself to take the certification.

31
00:04:17,000 --> 00:04:24,000
I do think it's a great idea to take that learning path open USD before you take the certification exam.

32
00:04:24,000 --> 00:04:30,000
You're going to get all the information you need by following along with those learning path self-paced courses.

33
00:04:30,000 --> 00:04:41,000
And then you'll have a great certificate at the end so you can show the world that you are open USD certified, making yourself even more knowledgeable and more of a thought leader whenever you make your posts.

34
00:04:41,000 --> 00:04:45,000
These are the links that will give you the various resources we have in the community.

35
00:04:45,000 --> 00:04:57,000
We have our calendar, which is the first link there, which will bring you all the upcoming live streams, office hours, study groups, key events that are happening, including GTC DC, which is coming up.

36
00:04:57,000 --> 00:05:02,000
I can't believe there's not a slide on that. I'm actually going to bring it to the web page and I'll show it to you later. But GTC DC is coming up.

37
00:05:02,000 --> 00:05:10,000
I'll be there. We always post those events in our event calendar. Very easy to subscribe and always find out about the latest.

38
00:05:10,000 --> 00:05:18,000
Our forums are a great place to post your questions. If you have any blockers, absolutely leverage the forums for pretty much everything.

39
00:05:18,000 --> 00:05:26,000
Some exceptions include Isaac Lab, where we prefer you to go to the GitHub page for Isaac Lab, but Isaac Sim has its own section on the forums.

40
00:05:26,000 --> 00:05:40,000
Our Discord server, which I mentioned earlier, that's an amazing resource. We have thousands of developers that are on there posting regularly about their projects and assisting each other on a day-to-day basis.

41
00:05:40,000 --> 00:05:48,000
Lots of great channels. It's well organized to find what you need quickly. Just use the search bar on the top right. Type anywhere you want. You'll get there in a flash.

42
00:05:48,000 --> 00:05:58,000
Finally, that last link is our community page, where you can look up things, including our ambassadors and other resources we have in the community.

43
00:05:58,000 --> 00:06:07,000
That is a lot of information for you. I hope everyone got good notes there. Now I would like to bring in my great colleague, Ganesh here.

44
00:06:07,000 --> 00:06:16,000
Ganesh, how are you doing? It is an honor to have you joining us here today. This is a very special episode we've got today.

45
00:06:16,000 --> 00:06:19,000
I'm building Agentec AI-powered digital twins, isn't it?

46
00:06:19,000 --> 00:06:39,000
Exactly. Thank you, Edmar, for having me again. I'm excited to share the next 15 minutes with Sight Machine, Kinetic Vision and Microsoft on how we are actually taking digital twin directly into operational shop floor experiences.

47
00:06:39,000 --> 00:06:44,000
And then how are we actually marrying that with Agentec AI? I'm really excited to be here. Thank you for inviting me.

48
00:06:44,000 --> 00:06:51,000
Of course. Let's bring in these very special guests. This is super exciting. We have Jeremy here. Hey, Jeremy.

49
00:06:51,000 --> 00:06:59,000
Hey, everybody. I'm from Kinetic Vision. It's so great to see you. Tell us a little about your background in Kinetic Vision.

50
00:06:59,000 --> 00:07:06,000
Yeah, I'm hailing here from Cincinnati, Ohio, a great hub of US manufacturing. I'm the CEO of Kinetic Vision.

51
00:07:06,000 --> 00:07:15,000
We develop and integrate digital twins for manufacturing and supply chain. We're an NVIDIA partner. We're a Microsoft partner and we're a Sight Machine partner.

52
00:07:15,000 --> 00:07:21,000
So really excited to be here. Talk with everybody today. And Ganesh and Edmar, thank you for inviting me.

53
00:07:21,000 --> 00:07:30,000
Of course. I mean, I love to see when partners are working so closely together. You see some amazing things happen with these billion of minds that are working together.

54
00:07:30,000 --> 00:07:37,000
Speaking of brilliant minds, let me bring in my partner, Sasha here also for NVIDIAside. Hey, Sasha, how are you doing?

55
00:07:37,000 --> 00:07:44,000
Good morning and good afternoon or good evening, depending on where you are. Hi, everyone. I'm Sasha Bichon.

56
00:07:44,000 --> 00:07:58,000
I work with Ganesh and Jeremy and the rest of the team and trying to bring together all of the good work we've done here in terms of digitization, if you will, and how we apply that into factories of the future.

57
00:07:58,000 --> 00:08:06,000
Amazing. I'm so glad to have you here. Sasha is an amazing person on our team, so he's going to have great context as well as Ganesh throughout this episode.

58
00:08:06,000 --> 00:08:14,000
Here's a good friend also, a veteran of the live streams. Hey, Drew. Hey, Edmar, how are you doing? How you been, man? It's great to see you again.

59
00:08:14,000 --> 00:08:22,000
Doing good, doing good. Yeah, you too, man. So you have a special role. Why don't you tell me what you do over there at Microsoft?

60
00:08:22,000 --> 00:08:36,000
Well, at Microsoft, basically I work with a group called Industrial Solutions Engineering. So basically we go into customers and innovate with them, with partners like NVIDIA, you guys.

61
00:08:36,000 --> 00:08:50,000
And we also have, I also work with a team that's actually in Houston, Texas. They have an actual facility where we build up hardware, like, for reels, and then, like, with PLCs.

62
00:08:50,000 --> 00:09:01,000
And we then test, like, getting data into the cloud and then doing stuff with it. And yeah, I've been working with Sasha, Ganesh, and you all, Edmar, like, for a while.

63
00:09:02,000 --> 00:09:10,000
Especially Ganesh, causing chaos wherever we go. So it's great to be here and good to see you.

64
00:09:10,000 --> 00:09:20,000
That's awesome. Well, thank you. I mentioned in my promo earlier I've had a lot of fun in rehearsals with this whole team of people. Let's bring in the last two people here. We've got Sheru. Hey, Sheru, how are you doing?

65
00:09:20,000 --> 00:09:22,000
Hello, everyone. Nice to meet you.

66
00:09:22,000 --> 00:09:26,000
Why don't you tell everybody about your role and your background at Site Machine?

67
00:09:26,000 --> 00:09:37,000
Absolutely. Hi, I'm Charu Kalluri. I'm a director of product marketing at Site Machine. Site Machine is an industrial AI platform focused on delivering manufacturing outcomes at scale.

68
00:09:37,000 --> 00:09:48,000
Over the years, we've been working very closely with Microsoft and NVIDIA and our kinetic vision. So just really excited to, again, be here with everybody to talk about the compelling value of all of these technologies put together.

69
00:09:48,000 --> 00:09:53,000
Awesome. We're very excited to have you here. People are going to really love it. We're going to show off in a few minutes.

70
00:09:53,000 --> 00:09:58,000
Speaking of showing off, we've got Sidhir here. How are you doing, Sidhir?

71
00:09:58,000 --> 00:10:22,000
Hey, I'm doing great. Nice to see you all. Super excited to be here. I run engineering at Site Machine and we work a lot with Microsoft and NVIDIA and all of the folks on the squad and others to bring together Site Machine and Microsoft and NVIDIA, integrating omniverse and bringing true value to our management.

72
00:10:23,000 --> 00:10:33,000
That's awesome. Well, listen, I can't think of a better crew to tackle this topic today. So I think everyone should buckle up. I already see a ton of comments coming in.

73
00:10:33,000 --> 00:10:43,000
That's fantastic. We're going to get to some of those in a second, but why don't we set the stage here, Ganesh and Sashi, for what we're going to really dive into here.

74
00:10:44,000 --> 00:11:01,000
Sounds good. So what are we going to see in the next 15 minutes or so? We're going to actually talk about how digital twins are actually shaping and driving the next wave of AI, which is deeply rooted into physical AI.

75
00:11:01,000 --> 00:11:23,000
And then we're going to actually talk about how Site Machine as an ISV has taken the technology from NVIDIA as well as from Microsoft and bringing that to shop floor and factory operations with the help of kinetic visions where they are actually providing a specific key ingredient in building the digital twin itself

76
00:11:23,000 --> 00:11:34,000
and how the 3D assets are actually getting converted into USD and how that is actually driving and powering the whole digital twin physical AI transformation.

77
00:11:34,000 --> 00:11:38,000
So that's what we're going to primarily cover in the next 15 minutes.

78
00:11:38,000 --> 00:11:44,000
All right. Very cool. And I think with that, I think Sheru, are you going to kick things off for us here?

79
00:11:45,000 --> 00:11:46,000
Yeah, let me...

80
00:11:46,000 --> 00:11:47,000
Oh, go ahead.

81
00:11:48,000 --> 00:11:50,000
Sorry, are you going to say something?

82
00:11:50,000 --> 00:11:52,000
Oh, no, sorry. Go ahead, Sheru.

83
00:11:53,000 --> 00:11:54,000
Yeah, go for it.

84
00:11:54,000 --> 00:11:55,000
All right.

85
00:11:56,000 --> 00:12:07,000
I wanted to talk a little bit about the specific manufacturing use cases that we're going to be solving with the solutions, because again, technology to solve real problems is what's very exciting to all of us.

86
00:12:07,000 --> 00:12:11,000
So give me a moment so I can share my screen.

87
00:12:11,000 --> 00:12:17,000
Okay. And while she's going to go ahead and sharing her screen, I definitely invite everyone to start posting your questions and comments.

88
00:12:18,000 --> 00:12:21,000
We will be hitting those throughout the hour here.

89
00:12:22,000 --> 00:12:24,000
And I think, Sheru, I see your screen is ready for me to share.

90
00:12:25,000 --> 00:12:26,000
Yes, ready to share.

91
00:12:26,000 --> 00:12:27,000
All right. Here we go.

92
00:12:29,000 --> 00:12:33,000
All right. Like I just mentioned, we are an industrial AI data platform.

93
00:12:34,000 --> 00:12:39,000
What we wanted to focus on today was what are the critical production challenges on the manufacturing shop floor.

94
00:12:39,000 --> 00:12:45,000
We are seeing operations teams constantly under varying conditions trying to achieve a lot of outcomes at once.

95
00:12:46,000 --> 00:12:54,000
These outcomes are typically things like line throughput increase, which is really how do I make, produce as much as I can and run as efficiently as possible.

96
00:12:54,000 --> 00:12:57,000
Schedule adherence, how do I make sure I'm not behind on production?

97
00:12:58,000 --> 00:13:06,000
How do I maximize my machine efficiency and maximize availability so that every machine is running close to its potential capacity?

98
00:13:07,000 --> 00:13:12,000
So all of these problems are things that we've been solving for over 10 years already for manufacturers.

99
00:13:13,000 --> 00:13:16,000
So that's sort of the landscape of challenges that customers are facing.

100
00:13:17,000 --> 00:13:29,000
But with all of the changes in technology, AI and, you know, innovations coming out, what manufacturers are struggling with most are things like user experiences.

101
00:13:30,000 --> 00:13:33,000
How do you make sure that all of these are adopted on the shop floor?

102
00:13:33,000 --> 00:13:37,000
How do you make sure that everybody in the enterprise has the same view of the line?

103
00:13:38,000 --> 00:13:41,000
And these are very complex lines and they're very large production lines.

104
00:13:42,000 --> 00:13:46,000
If you actually go to these plants, you literally can't see a couple of machines away from you.

105
00:13:47,000 --> 00:13:51,000
You can only see your machine in the next one, but you are impacted by what's happening on the rest of the line.

106
00:13:52,000 --> 00:13:58,000
So understanding that system level, you know, process is extremely important and very challenging to do today.

107
00:13:58,000 --> 00:14:08,000
So our vision, which is now being realized as we speak and being deployed with our clients is really about the power of 3D immersive visualization,

108
00:14:09,000 --> 00:14:13,000
agentic AI for insights, as well as a real-time data platform.

109
00:14:14,000 --> 00:14:19,000
So we have found that this combination of technologies really provides the visual elements so you can see what is happening.

110
00:14:20,000 --> 00:14:23,000
Everybody has a unified view of the enterprise and the line.

111
00:14:23,000 --> 00:14:27,000
Agentic AI generates recommendations under varying line conditions.

112
00:14:28,000 --> 00:14:35,000
So it's adapting to changing scenarios and it's providing the recommendations at the point of consumption against the 3D layer.

113
00:14:36,000 --> 00:14:45,000
So what you can see in this visual and what we'll be digging into and doing a deep dive on with the rest of our amazing team here is really how are these recommendations generated?

114
00:14:46,000 --> 00:14:52,000
How are these visualizations available and how does all of this work together seamlessly?

115
00:14:53,000 --> 00:14:55,000
They give a complete unified experience.

116
00:14:56,000 --> 00:15:06,000
So this is just an example of some of the real-world results we're seeing with our clients and I just wanted to paint the picture for what our manufacturing teams are looking to do.

117
00:15:06,000 --> 00:15:16,000
That's all I have slide-wise so I'm going to stop sharing.

118
00:15:17,000 --> 00:15:18,000
Very cool.

119
00:15:19,000 --> 00:15:20,000
And we have a quick video.

120
00:15:21,000 --> 00:15:23,000
Did you want me to play that now or is that for a little later?

121
00:15:24,000 --> 00:15:25,000
Sure.

122
00:15:25,000 --> 00:15:27,000
I think, yes, I can speak for a minute as the video plays.

123
00:15:28,000 --> 00:15:29,000
Edmar, that would be great.

124
00:15:29,000 --> 00:15:35,000
I think it's really important to understand again what the complexities are on the shop floor.

125
00:15:36,000 --> 00:15:44,000
So when you see these 3D views, you'll really understand and as the video plays, you can see that this is a full view of a bottling line.

126
00:15:45,000 --> 00:15:53,000
Now, this is again, based on the scale, you can't really see how large it actually is, but you can see that in this 3D view, you can see the machine status and speed instantly.

127
00:15:54,000 --> 00:16:00,000
You do not have to talk to operators and radio people or walk across the line or make decisions with incomplete information.

128
00:16:01,000 --> 00:16:08,000
So again, the real focus is on providing those immersive 3D experiences with insights all in one space.

129
00:16:09,000 --> 00:16:14,000
So the goal of this is to turn everybody on the shop floor team into a data-driven decision maker.

130
00:16:15,000 --> 00:16:16,000
Amazing.

131
00:16:16,000 --> 00:16:17,000
Very cool.

132
00:16:17,000 --> 00:16:19,000
That's so helpful to have that visualization.

133
00:16:20,000 --> 00:16:24,000
Obviously, there's a lot of opportunity for companies to leverage this kind of stuff.

134
00:16:24,000 --> 00:16:29,000
I guess one thing I always think about when I see something that looks so amazing like this is the work involved.

135
00:16:30,000 --> 00:16:35,000
I think sometimes people, companies might look at this and be a little overwhelmed or, oh my gosh, what's involved in that?

136
00:16:36,000 --> 00:16:41,000
But I think it's fair to say that people could take a very layered or phased approach to these kinds of scenarios.

137
00:16:42,000 --> 00:16:45,000
You don't have to go all in with everything at once.

138
00:16:46,000 --> 00:16:50,000
I'd be curious to see what you guys all think about suggestions for companies maybe watching that.

139
00:16:50,000 --> 00:16:55,000
What would that layered approach look like or fees for getting started?

140
00:16:57,000 --> 00:17:05,000
I think we can answer that real quick, Edmar, and then as you see what kinetic vision and site machine and the Microsoft team have done here,

141
00:17:06,000 --> 00:17:07,000
it'll unpack all of those.

142
00:17:08,000 --> 00:17:13,000
But I think the key ingredient starts with how the customer thinks about a brownfield operation.

143
00:17:13,000 --> 00:17:19,000
So the example that Charlie was talking about is an existing operational bottling line.

144
00:17:20,000 --> 00:17:22,000
They already have systems and processes in place.

145
00:17:23,000 --> 00:17:26,000
Some of them have been actually deployed over several years.

146
00:17:27,000 --> 00:17:40,000
So it's part of that whole digital transformation journey where you need to actually deploy more advanced and more latest technologies to harvest that data from the shop floor,

147
00:17:40,000 --> 00:17:42,000
which is what site machines systems do.

148
00:17:43,000 --> 00:17:48,000
And then you need to bring that into the cloud, which is what we're going to talk about in a second with the Microsoft architecture.

149
00:17:49,000 --> 00:17:57,000
And then you start applying more advanced digital twin technologies with what we were showing earlier with omniverse and kid app streaming, which is what Sashi is going to talk about.

150
00:17:58,000 --> 00:18:08,000
So there are existing systems in place and then there are add-ons that is required to harness that data sets to really drive the digital transformations and building the digital twins.

151
00:18:08,000 --> 00:18:11,000
So we'll get into those in a second.

152
00:18:12,000 --> 00:18:16,000
I think with that, we're probably going to hand over to Drew and Sashi to really unpack that architecture.

153
00:18:17,000 --> 00:18:18,000
Amazing. Thank you, Ganesh.

154
00:18:20,000 --> 00:18:21,000
Okay, so Drew.

155
00:18:22,000 --> 00:18:23,000
Okay, Drew is fixing his microphone right now.

156
00:18:24,000 --> 00:18:26,000
So we'll let Sashi take the start here.

157
00:18:27,000 --> 00:18:28,000
Sure.

158
00:18:29,000 --> 00:18:33,000
Yeah, so as Drew is fixing his microphone, I think, Drew, are you able to share with me?

159
00:18:34,000 --> 00:18:35,000
Oh, you're on man, that's great.

160
00:18:36,000 --> 00:18:39,000
The slide that you had on our architecture.

161
00:18:40,000 --> 00:18:48,000
Yeah, so Sashi, I'll just show the initial thing we did back in the night first and then I'll pass it to you. Is that cool?

162
00:18:49,000 --> 00:18:50,000
That's great.

163
00:18:51,000 --> 00:18:52,000
All right.

164
00:18:53,000 --> 00:19:02,000
So Microsoft and NVIDIA have been working together very closely for a while now to try to come up with a solution that combines the two platforms.

165
00:19:03,000 --> 00:19:06,000
And a solution that's also scalable, right?

166
00:19:07,000 --> 00:19:15,000
So at Microsoft, we do have this facility in Houston where we build hardware, pull out with PLCs.

167
00:19:16,000 --> 00:19:22,000
What you're looking at right now is a fluid process machine, which has pumps, valves and whatnot.

168
00:19:23,000 --> 00:19:25,000
And you can see the omniverse with the 3D context.

169
00:19:25,000 --> 00:19:28,000
You can see on the left the data from fabric.

170
00:19:29,000 --> 00:19:31,000
That's actually an embedded PBI in a single pane of glass.

171
00:19:32,000 --> 00:19:53,000
And so we created this, demoed it at Ignite with kind of an inspirational architecture for the future, which the whole purpose is to inspire companies like Sight Machine to take a hold of and kind of have like a base template where Microsoft and NVIDIA are like, yep, this is a good approach.

172
00:19:53,000 --> 00:20:02,000
And it gives folks confidence that this is the right way to go when doing like this is an operational use case view.

173
00:20:03,000 --> 00:20:10,000
So essentially then, so post Ignite, we posted an open source accelerator repo.

174
00:20:11,000 --> 00:20:16,000
And then Sight Machine got involved and worked with us and they took it and ran with it.

175
00:20:17,000 --> 00:20:24,000
And then went crazy cool in reality with the Coca-Cola consolidated bottling line.

176
00:20:25,000 --> 00:20:31,000
And let me pass it to Sashi right after I go to the architecture.

177
00:20:32,000 --> 00:20:33,000
So this is the architecture and Sashi take it away.

178
00:20:34,000 --> 00:20:35,000
Yeah, so thanks, Drew.

179
00:20:36,000 --> 00:20:44,000
So what we're showing here is a design pattern, if you will, or architectural design pattern for creating an operational digital twin.

180
00:20:45,000 --> 00:20:46,000
Now, this is an exemplar.

181
00:20:47,000 --> 00:20:53,000
It's not meant to be like the end off, but it gives you an idea of what to consider as you're trying to build out a capability.

182
00:20:54,000 --> 00:21:01,000
And I know in chat there's questions about like, how is this effective in terms of auto realistic renderings for users and so forth.

183
00:21:01,000 --> 00:21:02,000
We'll get to that.

184
00:21:03,000 --> 00:21:07,000
Let me walk you through this and then we can start answering some of those concerns too.

185
00:21:08,000 --> 00:21:15,000
Fundamentally, when we start thinking about these kinds of systems, where we start is usually in the data, right?

186
00:21:16,000 --> 00:21:21,000
And so that's on the left most side of this where you have edge computing, pulling in data.

187
00:21:22,000 --> 00:21:29,000
You're going to take that data and you're going to have to do some kind of processing and staging of it into an environment.

188
00:21:29,000 --> 00:21:33,000
In this case, we're using things like Azure IoT operations.

189
00:21:34,000 --> 00:21:37,000
We're using Arc enabled Kubernetes to do that kind of stage.

190
00:21:38,000 --> 00:21:42,000
Additionally, you'll have some level of 3D scene creation, and that has to also get managed.

191
00:21:43,000 --> 00:21:47,000
So in our case here, we started putting that together with Azure Blob Storage.

192
00:21:48,000 --> 00:21:52,000
These two pieces of information need to get correlated.

193
00:21:53,000 --> 00:21:58,000
And that's where we're correlating it with Omniverse Kit and with Microsoft Fabric and Power BI.

194
00:21:59,000 --> 00:22:07,000
So Fabric, Real-Time Intelligence and Azure Functions will take that data that we just received, convert it into more actionable data,

195
00:22:08,000 --> 00:22:11,000
convert it from, you know, bronze data to silver and gold data.

196
00:22:11,000 --> 00:22:21,000
And then the Power BI elements in block two will start overlaying that data into a user interface for a user.

197
00:22:22,000 --> 00:22:26,000
That's getting combined with the Omniverse Kit app streaming, the photo realistic rendering.

198
00:22:27,000 --> 00:22:30,000
Now, the big question that most people will have is, well, okay, you've got these two streams.

199
00:22:31,000 --> 00:22:36,000
You've got this 3D enrichment or 3D data and you've got this IoT data, but how do you connect that?

200
00:22:37,000 --> 00:22:39,000
And that's where it's kind of powerful.

201
00:22:39,000 --> 00:22:41,000
We put in enrichment into the USD.

202
00:22:42,000 --> 00:22:49,000
So the USD files that we create in Azure Blob Storage with the 3D data, we enrich them with ID information and other metadata.

203
00:22:50,000 --> 00:22:52,000
I sometimes refer to that as syntactical sugar.

204
00:22:53,000 --> 00:22:57,000
And that's what's brought in into Omniverse Kit and provides the front end.

205
00:22:58,000 --> 00:23:04,000
And the front end is able to then map between the data sources coming in from Fabric to the data source in Omniverse

206
00:23:04,000 --> 00:23:09,000
and give a user an immersive 3D environment plus the dashboarding effect.

207
00:23:10,000 --> 00:23:12,000
So why, right?

208
00:23:13,000 --> 00:23:14,000
Like, okay, this is great.

209
00:23:15,000 --> 00:23:20,000
And one of the questions is usually, so you can do in 3D, but how does this really help?

210
00:23:21,000 --> 00:23:28,000
Now, if we can imagine in the world, as we're progressing into decision making, we want engineers, users and developers

211
00:23:29,000 --> 00:23:38,000
to be able to quickly contextualize the space that they're in and be able to solve problems in that space.

212
00:23:39,000 --> 00:23:48,000
Fundamentally, if you've got an area in a factory or you have a stoppage or a blockage and you want to understand where that is,

213
00:23:49,000 --> 00:23:50,000
what's the context around it?

214
00:23:51,000 --> 00:23:56,000
Maybe you're looking at understanding, okay, what's the ingress path to get to that location to service something?

215
00:23:56,000 --> 00:23:58,000
Or what do I need to change?

216
00:23:59,000 --> 00:24:00,000
You need an immersive 3D environment.

217
00:24:01,000 --> 00:24:07,000
The photo realism part of it helps you in understanding both what you might do as a human operator,

218
00:24:08,000 --> 00:24:14,000
but also what you might do downstream when you're doing things like computer vision or other AI genetic kind of workflows

219
00:24:15,000 --> 00:24:18,000
for simulation purposes, for training, what I've been so far.

220
00:24:19,000 --> 00:24:24,000
So this is kind of like the starting point in which you can then have downstream use cases.

221
00:24:24,000 --> 00:24:33,000
And the starting point then becomes the same single pane of glass that you would use for both operations and for simulation environments

222
00:24:34,000 --> 00:24:35,000
and what if analysis and so forth.

223
00:24:36,000 --> 00:24:43,000
With that, I'd love to hand it over to Jeremy and he can talk through how we created the digital twin.

224
00:24:47,000 --> 00:24:48,000
It's great. Thank you, Sasha. It's awesome.

225
00:24:48,000 --> 00:24:53,000
Jeremy, looks like you have the floor now. Are you ready?

226
00:24:54,000 --> 00:24:55,000
I'm ready.

227
00:24:56,000 --> 00:24:57,000
No pressure. No pressure.

228
00:24:58,000 --> 00:24:59,000
No, so let's see here.

229
00:25:00,000 --> 00:25:01,000
Edmar, I've got a couple of things. There you go.

230
00:25:02,000 --> 00:25:03,000
All right, you're reading my mind here.

231
00:25:04,000 --> 00:25:05,000
So let's start with this.

232
00:25:06,000 --> 00:25:16,000
This is really just a basic representation of what we do to create the beautiful 3D asset that is inside

233
00:25:16,000 --> 00:25:19,000
of factory operate or inside of site machine.

234
00:25:20,000 --> 00:25:23,000
And all I do is just, you know, we have a lot of people on this call.

235
00:25:24,000 --> 00:25:25,000
They're going to be at varying levels of knowledge about all this.

236
00:25:26,000 --> 00:25:29,000
I saw some great questions about, you know, how do you create these assets?

237
00:25:30,000 --> 00:25:31,000
What are the steps?

238
00:25:32,000 --> 00:25:33,000
And I'm going to take some time to go through that.

239
00:25:34,000 --> 00:25:41,000
But, you know, Edmar, you asked one very interesting thing about, hey, how does a company take like a layered approach to this?

240
00:25:41,000 --> 00:25:49,000
And because we are, you know, integrating these solutions, we meet our customers where they're at.

241
00:25:50,000 --> 00:25:51,000
And that's really important.

242
00:25:52,000 --> 00:25:57,000
And so they may not be ready for a full blown site machine digital twin.

243
00:25:58,000 --> 00:25:59,000
They may need something a little more basic than that.

244
00:26:00,000 --> 00:26:03,000
So we developed this really simple process called the choir activate optimize.

245
00:26:03,000 --> 00:26:12,000
And then, and then at the end, you know, collaboration through an amazing platform like omniverse really brings all of your stakeholders together.

246
00:26:13,000 --> 00:26:17,000
So from the choir standpoint, we're just, we're just talking about scan data, get your data.

247
00:26:18,000 --> 00:26:19,000
Most companies do not have a hold of their data.

248
00:26:20,000 --> 00:26:27,000
Activate it using real simple software and then optimize it using data analysis that site machine provides or simulation.

249
00:26:28,000 --> 00:26:38,000
And then in the end, when you have all of this fantastic compute available, you can immerse yourself and really create a connected experience with all of your users.

250
00:26:39,000 --> 00:26:46,000
And so I also saw a question about, you know, how do you know when to use like a really high fidelity 3D model versus really basic representation?

251
00:26:49,000 --> 00:26:51,000
You know, I always err towards immersiveness.

252
00:26:52,000 --> 00:26:57,000
If you can do immersiveness without friction, then the human, we are humans.

253
00:26:58,000 --> 00:26:59,000
The human experience is going to be better.

254
00:27:00,000 --> 00:27:01,000
You're going to be inside the digital twin.

255
00:27:02,000 --> 00:27:06,000
So the more that you, where you get into issues is where you have, you know, lots of data.

256
00:27:07,000 --> 00:27:12,000
It's complex or you, you know, can't display it very well or there's a lot of friction and understanding it.

257
00:27:13,000 --> 00:27:17,000
But if you can without friction err towards immersiveness, you're always going to be in a better spot.

258
00:27:17,000 --> 00:27:22,000
Sometimes you need a little bit of visionary leadership there to, you know, kind of push an organization that direction.

259
00:27:23,000 --> 00:27:24,000
But that's where we always tend towards.

260
00:27:25,000 --> 00:27:30,000
So, you know, I have a really simple video that makes this stuff look simple and feel simple.

261
00:27:31,000 --> 00:27:32,000
It's, I haven't labeled this video one.

262
00:27:33,000 --> 00:27:34,000
I don't know if you can flip over there real quick.

263
00:27:36,000 --> 00:27:37,000
Let me see. I'm looking at it. I see.

264
00:27:38,000 --> 00:27:39,000
Oh, yeah, I do see it. Let's see.

265
00:27:40,000 --> 00:27:41,000
I got three. I got all kinds of content.

266
00:27:41,000 --> 00:27:44,000
Even small delays can snowball into big disruptions.

267
00:27:44,000 --> 00:27:49,000
That's where digital twins come in with our acquire, activate, optimize process.

268
00:27:50,000 --> 00:27:52,000
It's fast, easy and low risk.

269
00:27:53,000 --> 00:28:01,000
We start by scanning your facility in stunning detail up to 10 times faster than traditional methods with zero disruption to operations.

270
00:28:02,000 --> 00:28:15,000
That scan becomes a powerful 3D digital twin, enabling virtual design, remote tours and supporting system updates, layouts, training and safety planning.

271
00:28:16,000 --> 00:28:22,000
In just 60 days, we uncover eight to 10 times first year ROI with payback in less than three months.

272
00:28:23,000 --> 00:28:31,000
AI delivers smart insights and actions to drive fast solutions and improve overall operation effectiveness.

273
00:28:32,000 --> 00:28:34,000
Dashboards track performance in real time.

274
00:28:35,000 --> 00:28:44,000
AI flags a labeler jam, triggers a fix and recommends a second forklift and dual labeler to boost material movement and throughput.

275
00:28:45,000 --> 00:28:46,000
Have a problem or want to try a change?

276
00:28:47,000 --> 00:28:48,000
Simulate it first.

277
00:28:49,000 --> 00:28:50,000
No risk, no downtime.

278
00:28:50,000 --> 00:28:53,000
Your digital twin drives smarter decisions.

279
00:28:54,000 --> 00:29:02,000
Once it works in one facility, it scales easily, making digital twins the perfect solution to transform your operations.

280
00:29:04,000 --> 00:29:09,000
Okay, Edmar, that was marketing glitz from our marketing department.

281
00:29:09,000 --> 00:29:10,000
Makes it look simple.

282
00:29:11,000 --> 00:29:14,000
Well, I gotta tell you, there was a couple of pieces of information that were super compelling.

283
00:29:15,000 --> 00:29:19,000
The ROI of three months, that's pretty amazing.

284
00:29:20,000 --> 00:29:24,000
And also the granularity of detail, down to five millimeters.

285
00:29:25,000 --> 00:29:26,000
It's pretty wild.

286
00:29:27,000 --> 00:29:30,000
Yeah, so can you queue up, there's something called Video 2 in there?

287
00:29:31,000 --> 00:29:35,000
So this is going to be a little more like, what does it look like when you do this stuff?

288
00:29:36,000 --> 00:29:40,000
Like, who's doing what? When are they doing it? What programs are they using?

289
00:29:40,000 --> 00:29:45,000
So just queue that up, that'll be another minute, and then I'll talk a little more after that.

290
00:29:46,000 --> 00:29:47,000
Okay, here we go.

291
00:29:48,000 --> 00:29:51,000
And there's no audio on this.

292
00:29:52,000 --> 00:29:59,000
So really all we're showing is just a case study here of taking this acquire, activate, optimize process to a real facility.

293
00:30:00,000 --> 00:30:01,000
This is a distribution center.

294
00:30:02,000 --> 00:30:03,000
So Brian's out there scanning it.

295
00:30:04,000 --> 00:30:06,000
He looks a lot like that little iconography we made of him.

296
00:30:07,000 --> 00:30:13,000
We use real, we take that data, grab that 3D data, and then we use some other NVIDIA partners.

297
00:30:14,000 --> 00:30:21,000
This is Preview 3D on this particular project to really quickly get measurements, get panos.

298
00:30:22,000 --> 00:30:25,000
This is a simulation tool that we happen to use called FlexSim.

299
00:30:27,000 --> 00:30:32,000
And just, you know, once again, an alternate process, not related to what we're doing with site machine here,

300
00:30:32,000 --> 00:30:35,000
but another method to optimize the site.

301
00:30:36,000 --> 00:30:40,000
Taking those, we're actually doing virtual camera simulation here just to make sure,

302
00:30:41,000 --> 00:30:46,000
even stuff that's as simple as get your camera set up in the right place before you install them, that can be all done virtually.

303
00:30:47,000 --> 00:30:50,000
And it saves a lot of time and it saves big mess ups.

304
00:30:51,000 --> 00:30:56,000
So just a couple of visuals of a recent case study, that's a real project we did for a real customer.

305
00:30:56,000 --> 00:31:01,000
And we did deliver, that was like a three month payback.

306
00:31:02,000 --> 00:31:05,000
And there's some big decisions being made off of what we found digitally.

307
00:31:06,000 --> 00:31:08,000
The biggest thing is not disrupting the operation.

308
00:31:09,000 --> 00:31:10,000
That's kind of the biggest thing.

309
00:31:11,000 --> 00:31:13,000
And then just queue up my PowerPoint if you wouldn't mind.

310
00:31:14,000 --> 00:31:15,000
Okay, let me move this one first.

311
00:31:17,000 --> 00:31:19,000
And then let me see, your PowerPoint is right here.

312
00:31:20,000 --> 00:31:25,000
Yeah, I win the contest for most media presentations.

313
00:31:26,000 --> 00:31:27,000
I love it.

314
00:31:30,000 --> 00:31:31,000
Okay, we saw that, we saw that.

315
00:31:32,000 --> 00:31:33,000
Okay, so how do we do this?

316
00:31:34,000 --> 00:31:38,000
First of all, do not underestimate, you need some great people to do this work.

317
00:31:39,000 --> 00:31:44,000
Kinetic Vision has a lot of amazing people, but we're talking about between site machine and Kinetic Vision.

318
00:31:45,000 --> 00:31:51,000
We have data scientists, we have mechanical engineers, we've got machine learning engineers, we've got software engineers, technical artists.

319
00:31:52,000 --> 00:31:53,000
It's a diverse team.

320
00:31:53,000 --> 00:32:03,000
So when you're making decisions about building even just the 3D asset, it's really helpful to have subject matter expertise alongside your tech artists when you're building that asset.

321
00:32:04,000 --> 00:32:07,000
So just a little bit about, you know, do not forget about the people.

322
00:32:08,000 --> 00:32:13,000
AI is amazing, but we, at least for probably the next five years, we're still going to need people.

323
00:32:15,000 --> 00:32:20,000
Okay, little nuts and bolts on like, what do you use to go do this?

324
00:32:21,000 --> 00:32:24,000
We're, what we're doing, I'll look at the end here really quickly.

325
00:32:25,000 --> 00:32:36,000
We're, you know, we're publishing a kit app, you know, kit app USD that's streaming within an omniverse, like visual context, and that's part of the site machine application.

326
00:32:37,000 --> 00:32:45,000
And so what we're doing is we're delivering a USD asset that can deliver that high fidelity, fully realistic, interactive view.

327
00:32:46,000 --> 00:32:47,000
What we start with is this 3D scan.

328
00:32:47,000 --> 00:32:49,000
There's a lot of choices here, everybody.

329
00:32:50,000 --> 00:32:52,000
We happen to use Naviz, Faro and Leica.

330
00:32:53,000 --> 00:32:56,000
Naviz scanners are very fast, down to five millimeter accuracy.

331
00:32:57,000 --> 00:32:59,000
You're doing a lidar scan, capturing millions of points as you go.

332
00:33:00,000 --> 00:33:03,000
Faro and Leica are more terrestrial scans.

333
00:33:04,000 --> 00:33:10,000
And so you're setting your, you know, you're setting your tripods and you're capturing data, but they're much more accurate and you get a lot of higher fidelity.

334
00:33:10,000 --> 00:33:14,000
So we typically use Naviz for scanning a full site.

335
00:33:15,000 --> 00:33:27,000
And then if we have particular machines where you really need really accurate user interface details and accurate, like mechanical details, we'll go in and re scan with like a Faro or Leica to get those high details.

336
00:33:28,000 --> 00:33:34,000
From a reality capture perspective, we're taking those scans and we're activating them with the real simple off the shelf software that we integrate.

337
00:33:35,000 --> 00:33:37,000
You have a few choices there too.

338
00:33:37,000 --> 00:33:39,000
There's a big ecosystem out there.

339
00:33:41,000 --> 00:33:44,000
We use preview 3D here at Kinetic Vision.

340
00:33:45,000 --> 00:33:49,000
And let's see, we also use Reality Cloud Studio.

341
00:33:50,000 --> 00:33:52,000
So these are two great programs.

342
00:33:53,000 --> 00:34:02,000
There's a handful of them out there, but what's nice, we work with mostly big companies and a lot of them prefer to procure software instead of using something open source, something that's supported.

343
00:34:03,000 --> 00:34:04,000
But there's open source options also.

344
00:34:05,000 --> 00:34:15,000
Then, you know, once we get that reality capture data, which includes really high resolution, panographic images, sometimes we're creating a mesh that's got materials applied to it.

345
00:34:16,000 --> 00:34:20,000
We then pull that into a 3D digital content creation package.

346
00:34:21,000 --> 00:34:24,000
Pick your package, 3D Studio Max, Blender, Maya.

347
00:34:25,000 --> 00:34:26,000
There's a lot of great choices there.

348
00:34:27,000 --> 00:34:29,000
We happen to use all three of these.

349
00:34:29,000 --> 00:34:32,000
And then you're following traditional artist workflows.

350
00:34:33,000 --> 00:34:34,000
You're either doing direct modeling.

351
00:34:35,000 --> 00:34:37,000
You're using a model library and maybe bringing a model in.

352
00:34:38,000 --> 00:34:41,000
You're referencing those scans for the geometry sizes.

353
00:34:42,000 --> 00:34:44,000
You're perhaps re-topologizing some geometry.

354
00:34:45,000 --> 00:34:47,000
And then you're building your assets in 3D Studio Max.

355
00:34:48,000 --> 00:34:49,000
I'll put a footnote here.

356
00:34:50,000 --> 00:34:58,000
There's a lot of exciting, exciting technology around the generative creation of these assets.

357
00:34:59,000 --> 00:35:05,000
NVIDIA's got some great open source libraries out there that they're publishing with their research teams.

358
00:35:06,000 --> 00:35:07,000
And, you know, they're worth checking out.

359
00:35:08,000 --> 00:35:18,000
We're not fully using them in these workflows yet, but there's going to be a whole slew of software packages and workflows available around using generative AI.

360
00:35:19,000 --> 00:35:20,000
So, you know, we're going to be using them for 3D.

361
00:35:21,000 --> 00:35:24,000
And then, you know, once we have that 3D Studio Max or Blender asset,

362
00:35:25,000 --> 00:35:29,000
in order to access it programmatically within Sight Machine,

363
00:35:30,000 --> 00:35:32,000
we're, you know, grouping portions of that USB file.

364
00:35:33,000 --> 00:35:35,000
We're making them available so they're triggered by a sensor.

365
00:35:36,000 --> 00:35:37,000
We're setting camera views.

366
00:35:38,000 --> 00:35:42,000
And so what we're doing that in is just a little application that we built called Data Vision.

367
00:35:43,000 --> 00:35:44,000
It's built on the Omniverse SDK.

368
00:35:44,000 --> 00:35:50,000
And it's really using those great resources from the Omniverse SDK to build 3D application.

369
00:35:51,000 --> 00:35:55,000
This allows us to layer in some extra data that Sight Machine needs to hook up to their platform.

370
00:35:56,000 --> 00:35:58,000
So, and that's most of it.

371
00:35:59,000 --> 00:36:01,000
I'm sure there'll be some questions, but just to cap it off,

372
00:36:02,000 --> 00:36:08,000
this is just one of an asset from one of our pieces, one of our recent projects with Sight Machine,

373
00:36:08,000 --> 00:36:15,000
just showing these steps, going from 3D Point Cloud to Reality Capture Asset to Assimilation Asset

374
00:36:16,000 --> 00:36:22,000
to a really beautiful photorealistic asset with animation done through the Omniverse SDK.

375
00:36:23,000 --> 00:36:25,000
And that's what I got for you, people.

376
00:36:26,000 --> 00:36:29,000
That was really cool. Show me. I didn't see that before. That was really amazing.

377
00:36:30,000 --> 00:36:33,000
I know. Ganesh, you're always, yeah. Ganesh is like, he's like, you never show me anything.

378
00:36:34,000 --> 00:36:36,000
Like, I just got to see the latest stuff. So, yeah, there you go.

379
00:36:37,000 --> 00:36:43,000
I love it. That is so wild. I think, yeah, it's a lot of impressed people watching in the chat as well.

380
00:36:44,000 --> 00:36:49,000
Very cool. Let me see. And I think I'm going to leave this on for me for one second.

381
00:36:50,000 --> 00:36:55,000
And there we go. Okay, cool. That's wild. So, anyone who's just joining us, thank you.

382
00:36:56,000 --> 00:36:58,000
Welcome to the stream. We're talking with Sight Machine, Microsoft Kinetic Vision,

383
00:36:59,000 --> 00:37:05,000
and of course, folks from the NVIDIA team about how Agentec AI and digital twins are transforming manufacturing operations.

384
00:37:06,000 --> 00:37:09,000
Be sure to stay active in the chat. We see a lot of questions and comments coming through.

385
00:37:10,000 --> 00:37:16,000
We'll try to address those. But that was fantastic. Thank you so much for carrying us through that really nice journey, Jeremy.

386
00:37:18,000 --> 00:37:19,000
You are welcome.

387
00:37:20,000 --> 00:37:25,000
Okay. All right. So, of course, now that we've talked about scanning and creating USD,

388
00:37:26,000 --> 00:37:31,000
I think we have our friend, Sudhir here, who's going to bring us into, to bring us home here, so to speak.

389
00:37:32,000 --> 00:37:36,000
All right. Can you share my screen?

390
00:37:37,000 --> 00:37:40,000
Yes, let me see if I can. Yeah, I think I got it right here.

391
00:37:44,000 --> 00:37:45,000
Okay, we can see it.

392
00:37:46,000 --> 00:37:47,000
Awesome. Awesome.

393
00:37:48,000 --> 00:37:58,000
Thanks, everyone. Super exciting. So you guys saw how Shashi and Blue presented the reference architecture that was done at Ignite.

394
00:37:58,000 --> 00:38:07,000
Super cool, right? Shado presented the use case that we are talking to customers about and how we are showing value with this ecosystem.

395
00:38:08,000 --> 00:38:18,000
And then Jeremy presented how they take all these scans on the factory and convert them into meaningful USDs that we can then use.

396
00:38:18,000 --> 00:38:31,000
I'm going to just put it together to show you how Sight Machine took all these pieces together and built an architecture that shows value to our customers with all of these pieces put together.

397
00:38:32,000 --> 00:38:40,000
So here's the technical architecture diagram. It's a flavor of the reference architecture that you saw earlier.

398
00:38:40,000 --> 00:38:50,000
I'm going to highlight some of the changes we did or how we added on our technologies to make this even more compelling for our job customers.

399
00:38:51,000 --> 00:39:01,000
So first off to recap Sight Machine is a AI manufacturing data platform. We take data from all these data sources that you see on the left hand side.

400
00:39:01,000 --> 00:39:12,000
We standardize them, convert them into standard data models, thereby enabling things like analytics, general AI, digital twins and so on.

401
00:39:13,000 --> 00:39:28,000
So here you can see the first step going through the steps here is our factory connect application that Sight Machines application that runs on IoT operations as Shashi was mentioning in the Unable Kubernetes cluster.

402
00:39:28,000 --> 00:39:32,000
This gets all the data and passes it on to the next step.

403
00:39:33,000 --> 00:39:49,000
We also have this data powering the factory operate and factory build platforms which are Sight Machines proprietary platforms to process and model the data for use in the kit application as well as for further analysis and AI.

404
00:39:50,000 --> 00:39:57,000
All of this is running on the Microsoft Azure ecosystem to deliver a scalable unified solution.

405
00:39:58,000 --> 00:40:05,000
So let's look at each component of it by drilling down into each and see each aspect in more detail.

406
00:40:06,000 --> 00:40:15,000
So first off the data ingestion piece, right? So factory connect ingest data. The first step is to ingest data from the edge.

407
00:40:16,000 --> 00:40:21,000
We are able to ingest data from a variety of manufacturing data sources like PLCs, historians, etc.

408
00:40:21,000 --> 00:40:31,000
Factory connect problems as we mentioned in the Arc-enabled Kubernetes cluster, which offers an extensible, composable view to represent the line.

409
00:40:32,000 --> 00:40:34,000
So that's the first step.

410
00:40:35,000 --> 00:40:43,000
The second step is we use IoT operations here that reduces the complexity to build the end-to-end solution.

411
00:40:43,000 --> 00:40:50,000
IoT operations enables us to connect the cloud and edge using bi-directional messaging.

412
00:40:51,000 --> 00:41:01,000
On the second piece here, we have the 3D scans of the factory. This is what Jeremy is talking about. These are created. You saw all the details. I'm not going to go into it again.

413
00:41:02,000 --> 00:41:16,000
These scans are segmented into Assemblies, Machines, Components. All those are in the USD format, which is then loaded into Azure Blob Storage for consumption by us and by NVIDIA on viewers.

414
00:41:17,000 --> 00:41:24,000
With this data and leveraging Azure AI services, Site Machine is able to provide effective insights.

415
00:41:25,000 --> 00:41:38,000
Next up is the scalable cloud platform. On this cloud, once we have the data transferred from the edge to the cloud, Site Machine is manufacturing data platform.

416
00:41:39,000 --> 00:41:46,000
This powers all the data to factory operate. All this runs seamlessly in Azure cloud and Microsoft Fabric.

417
00:41:47,000 --> 00:41:55,000
IoT operations sends data to the cloud via Fabric Azure Event Hubs, where I even streams in Fabric.

418
00:41:56,000 --> 00:42:05,000
This is where Site Machine is able to process the data and create those standardized data models that I talked about that represent the entire line end-to-end.

419
00:42:06,000 --> 00:42:18,000
With this data and then leveraging AI services from Azure, Site Machine is able to provide effective insights like agentic recommendations, which we will look at shortly.

420
00:42:19,000 --> 00:42:30,000
The third piece here is the omniverse kit extensions. We are leveraging NVIDIA omniverse kit app streaming, obviously.

421
00:42:31,000 --> 00:42:40,000
Now that we have data from Site Machine's data platform, we built a couple of kit extensions to integrate with the kit app streaming application.

422
00:42:40,000 --> 00:42:51,000
The first one as Shashi was alluding to takes real-time data from the edge as well as the model data from our factory build application to annotate the USD

423
00:42:52,000 --> 00:43:00,000
so that we can get contextualized data for these twins that Jeremy is so beautifully generated. Example things like filler speed.

424
00:43:01,000 --> 00:43:11,000
That gives you the context of each machine, each asset on the line layered on with meaningful data from the Site Machine application.

425
00:43:12,000 --> 00:43:19,000
The second piece is the operate extension. That's the one that handles rendering of this contextual data.

426
00:43:20,000 --> 00:43:28,000
Example, creating a billboard on top of the machine to show you the name of the filler, the attributes, how it's running, its status, and so on.

427
00:43:29,000 --> 00:43:38,000
It also handles things like zooming into a particular asset, events on the UI, showing where a fault is.

428
00:43:39,000 --> 00:43:48,000
It responds to data changes, like if the filler speed changes, you'll immediately see the change in omniverse, in our UI, and so on.

429
00:43:48,000 --> 00:43:58,000
We see that in a demo or a short piece. It automatically syncs up. Everything is instantly available on the UI and in omniverse.

430
00:43:59,000 --> 00:44:06,000
Events in the UI are in React and it's communicated to the kit application.

431
00:44:07,000 --> 00:44:14,000
Now let's look at how the UI piece works. The last piece here is the seamless UI integration.

432
00:44:14,000 --> 00:44:24,000
On the front end, Factory Operate is a React application. We embed the omniverse viewport into Operate using NVIDIA provided packages.

433
00:44:25,000 --> 00:44:31,000
Every user then connects to a stream from the kit app to show this viewer in the UI.

434
00:44:31,000 --> 00:44:44,000
In order to improve efficiency, we've implemented things like stream management to pre-created cash streams, creating the stream pools, showing instant availability in the UI.

435
00:44:45,000 --> 00:44:50,000
Events in the UI are passed to Kit App via WebRTC and Y-Saver.

436
00:44:50,000 --> 00:45:04,000
Anything that happens in the Kit application, you can have events reflecting in the UI and things the user interacting or data streaming from the UI can interact real time with the omniverse application.

437
00:45:05,000 --> 00:45:19,000
The whole stream is contextualized to the View and React labels and other annotations in the stream provide a seamless view of the line with recommendations from agent.ai layered on top of it.

438
00:45:22,000 --> 00:45:24,000
Very nice.

439
00:45:24,000 --> 00:45:34,000
Okay, you got it. Okay, cool. That was fantastic. We got a lot of great comments as you were showing your presentation there.

440
00:45:35,000 --> 00:45:41,000
Good questions too. Is there anything you guys want to adjust before we start tackling some of these questions?

441
00:45:42,000 --> 00:45:49,000
I want to show up and then we can jump into questions. Give me one sec.

442
00:45:50,000 --> 00:45:57,000
I see omniverse ambassador John Mitchell from BMW is watching today.

443
00:45:58,000 --> 00:46:08,000
We're showing great interest in connecting with members of our panel here, which is great. BMW of course is doing amazing things in the digital twin world with their factories and robotics.

444
00:46:09,000 --> 00:46:12,000
Okay, so do you guys see it right here, right?

445
00:46:14,000 --> 00:46:16,000
Yes, that is correct.

446
00:46:17,000 --> 00:46:22,000
Okay, awesome. So just show a quick demo, putting it all together.

447
00:46:23,000 --> 00:46:34,000
Charo showed this briefly in the video. This is a live version of our factory operate application running with omniverse get up streaming embedded in our React application.

448
00:46:35,000 --> 00:46:39,000
As you can see, this is an entire view of the line. It's a modeling line.

449
00:46:39,000 --> 00:46:51,000
On the left hand side, you can see a bunch of, you know, metrics for the line. What's the machine efficiency, what's the filler speed, different fillers, production volume, what flavor is going through the line and so on.

450
00:46:52,000 --> 00:47:04,000
And at the bottom of the screen, you can see all the assets and their individual attributes like the filler one, it seems like has a fault, filler two is running well, and so on and so forth.

451
00:47:04,000 --> 00:47:17,000
And you can see the same data being leveraged and shown in the omniverse get up streaming as well as billboards, as I mentioned, using the web RTC cons that we talked about.

452
00:47:18,000 --> 00:47:24,000
Now, if I want to look in detail, I can see, okay, this filler one has a fault. So let me draw down into that.

453
00:47:24,000 --> 00:47:36,000
Now, and you can see we zoom in to the asset. And not only do we zoom in, we are able to highlight specific areas of the machine where the fault actually occurred.

454
00:47:37,000 --> 00:47:46,000
So it looks like a conveyor jam. It reflects the red section is this is the place where the operator needs to focus on to fix the issue, right.

455
00:47:46,000 --> 00:48:00,000
So this is super powerful. This is where we talked about how kinetic vision is helping us build the USD segmented into components so that we can leverage our data and pinpoint exact locations.

456
00:48:01,000 --> 00:48:10,000
So not only do we give calls this we also give exact locations and recommendations for operators to immediately fix this on the shop floor.

457
00:48:10,000 --> 00:48:21,000
Let me zoom back out a bit. The other thing you can see here with this purple screen is it says line is running well but not at peak efficiency. I'm going to take ways to optimize.

458
00:48:22,000 --> 00:48:33,000
And this is where a genetic AI recommendation engine comes into play, right. So not only are you seeing like current statuses and basic ways to fix it.

459
00:48:33,000 --> 00:48:52,000
You're also seeing things like hey, the filler speed should be increased from 630 to 700. That's what our agent to get I recommend just the optimal speed for the filler or something to do with the tackle to update their settings to accommodate the next set of packaging or a wrapper.

460
00:48:52,000 --> 00:49:02,000
What is there to replace and similarly, you can now say hey, go to the rapper. Let me see what's going on. Hey, prepare to wrap replace the rap material.

461
00:49:03,000 --> 00:49:15,000
AI has detected that you might run out of the rap soon enough so that's the time for your operator to go on set up the rap get it put it in place so that you don't have an issue you don't have stoppages.

462
00:49:15,000 --> 00:49:32,000
You don't have down times. So very, very powerful leveraging of our data and leveraging of all the 3D assets that Jeremy showed the army was kept up streaming platform bringing it all together to show immense value for our customers.

463
00:49:33,000 --> 00:49:35,000
I will stop right there.

464
00:49:35,000 --> 00:49:45,000
Wow, amazing. I don't think anybody wants you to stop where everyone was blown away. You're going live and everyone can obviously you notice the time and date up up on the right. It was not faked.

465
00:49:46,000 --> 00:49:59,000
Real time. That's pretty amazing. That's a great question. The comments in the chat. Do you any any more or less thoughts before we start tackling some of these questions. I know we've been discussing something in the background which to tackle.

466
00:50:00,000 --> 00:50:12,000
Okay, alright, so let's go. We have we've got nine minutes, so which means we got we got a hustle. So let's try to let's try to keep our answers as concise as possible and we can refer people to our discord.

467
00:50:13,000 --> 00:50:26,000
Amelia is going to set up a thread on our discord server specifically for this live stream. So whether you're watching this live or the replay, you can go to that thread and you could continue to ask questions, conversations and hopefully with other viewers will also go there too.

468
00:50:26,000 --> 00:50:42,000
So you can chat with them on it. Alright, so let's do the little lightning round of questions here. I think earlier on actually we had a good question. I think for a first site machine so sure if you want to take this one about how do you approach a client engagement and when client stakeholders have different priorities.

469
00:50:43,000 --> 00:50:45,000
Who do you prioritize and why.

470
00:50:46,000 --> 00:51:05,000
Yeah, so the answer to that is we have to prioritize everybody. He actually worked with both it and we provide a complete solution without the agreement and agreement across these functions and now now we're also seeing a separate a function which really collaborates with it and

471
00:51:06,000 --> 00:51:16,000
and we provide daily solutions to one in the enterprise. So we go all the way we address security, plant connectivity, as well as operational data and insights.

472
00:51:17,000 --> 00:51:27,000
So the real answer is you cannot afford to prioritize one over the other because then you're not going to have a successful plant level transformation that scales.

473
00:51:27,000 --> 00:51:36,000
Okay, very, very super helpful and just a quick note we had to wave goodbye to Sasha a minute ago he had none of the meaning you had to go do so thank you Sasha for always helping out great to have you here.

474
00:51:37,000 --> 00:51:44,000
Okay, we also I'm looking so we have a couple of different chat channels where we're keeping track of some of these questions I'm looking at our internal chat here where I see a few things that were discussed.

475
00:51:44,000 --> 00:51:55,000
Sure you just mentioned you saw a good question from Victor. Amazing collab curious with this foundation which type of simulation becomes more feasible high fidelity asset level modeling or broader plant level.

476
00:51:56,000 --> 00:51:59,000
What if situational modeling did you want to tackle this one.

477
00:51:59,000 --> 00:52:13,000
Sure, I'd also like to invite maybe so they're in others if they have any specific ideas just to talk about this but we are thinking a lot about what a situational modeling and scenario modeling.

478
00:52:14,000 --> 00:52:24,000
Which means that if I have a change in raw material or if I add another machine to my line. How does the capacity change what will my line look like and what should I be planning for.

479
00:52:24,000 --> 00:52:32,000
Ideally we help them make real decisions by seeing things like if you add another machine you can reduce all of your weekend shifts or eliminate them all together.

480
00:52:33,000 --> 00:52:44,000
And again the focus is on providing broad scenario level guidance with with our simulations but if anyone has additional thoughts and everything I'd love to invite them as well.

481
00:52:45,000 --> 00:52:46,000
Yeah, yeah.

482
00:52:47,000 --> 00:52:48,000
I have one point to add as well.

483
00:52:48,000 --> 00:52:54,000
Yeah, so it's sort of I would say both right like at the first step.

484
00:52:55,000 --> 00:53:17,000
We're trying to get as accurate a representation of the models as possible right and then try to solve real problems like these agentic recommendations or what's happening on the shop floor and so on so forth and for that perhaps you don't need like the super super high fidelity still high fidelity as you could see.

485
00:53:18,000 --> 00:53:38,000
But as as you were talking about but there are definitely use cases when you're looking at animations and like real time playbacks of you know how things happen and how it caused a fault or what have you where a more realistic representation is absolutely essential.

486
00:53:39,000 --> 00:53:44,000
So both are different use cases and you know both are something we are looking at that to finish.

487
00:53:44,000 --> 00:54:02,000
Yeah, so but what what you guys said is spot on but specifically on the asset level or simulation specific question that Victor is asking that we were talking about the bottling line use case the single most expensive asset that's there in the line is a filler and then of course the next one is a packer right.

488
00:54:02,000 --> 00:54:16,000
So there are filler level simulations that can also be done to understand spillage inefficiencies things that are supposed to be followed by certain string guidelines of course you cannot stop a filler to run those scenarios.

489
00:54:17,000 --> 00:54:19,000
So that's where simulation comes really handy.

490
00:54:20,000 --> 00:54:32,000
So to think about the whole digital twin journey that we were discussing in the last few minutes is to break down into two separate journeys one is the whole operational digital twin and the other one is a simulation digital twin and they both kind of go hand in hand.

491
00:54:33,000 --> 00:54:48,000
So the filler simulation is one of the examples where you could be thinking about fluid level simulations and the CFD style simulations as well where we were actually in track with a third party simulation provider that can run that kind of filler level simulations as well.

492
00:54:49,000 --> 00:55:00,000
It's a it's a team play it's not like in a one size fits all and one partner provides all the different experience in the services you bring the right ingredients for the right kind of job to get the results.

493
00:55:01,000 --> 00:55:02,000
The business results that you expect.

494
00:55:03,000 --> 00:55:04,000
Great.

495
00:55:04,000 --> 00:55:05,000
Okay that's super helpful.

496
00:55:05,000 --> 00:55:12,000
And so this this gentleman or a gentle lady has asked us a couple of times in chat so I know they're eager to for this answer.

497
00:55:12,000 --> 00:55:23,000
This is an interesting question obviously because we're talking about very developer kind of focused pipeline but we have a lot of creators out there in the world who really want to ramp up and and contribute to these challenges.

498
00:55:24,000 --> 00:55:37,000
At C graph recently there was a session if Amelia has has a second maybe she can try to find that session I think the moment live the other day for everyone to watch now we had a session about as it's really as if you're a 3D content creator how do you upscale for the physical AI.

499
00:55:38,000 --> 00:55:41,000
Does anyone have any any suggestions for Yash here.

500
00:55:42,000 --> 00:55:45,000
I think I think this is a great question for kinetic vision and Jeremy do you want to take that.

501
00:55:47,000 --> 00:55:58,000
Yeah so I actually saw this question I thought wow we're actually going to be answering some of the one of these questions so you know I the things that I laid out in my presentation.

502
00:55:58,000 --> 00:56:03,000
It is a lot about tools curiosity and skill sets right so.

503
00:56:04,000 --> 00:56:20,000
Really just you know for this type of simulated world we need these types of tools we're using Navis scanning we're using reality capture and then we're using what you know if you're if you're a creator you're already using this great set of 3D content content creation tools.

504
00:56:21,000 --> 00:56:28,000
You know in Maya 3D Studio Max substance you know those are those are all the set of tools that you're using so.

505
00:56:29,000 --> 00:56:47,000
I think really using the those tools and the guidance on how to get to that level simulated world I think is you know from from the from those software vendors is you know going to be really important but the other thing is the collaboration what I'm.

506
00:56:47,000 --> 00:56:55,000
You know a lot of these questions what I'm hearing is they all relate to collaboration and communication.

507
00:56:56,000 --> 00:57:08,000
These are big problems we're creating an entire simulated world so that means that we need all of the skill sets and expertise from each of the individuals that places something in that in that virtual world.

508
00:57:08,000 --> 00:57:23,000
I'm sorry Ganesh I'm not sure when you learned about fillers but you know to make a good decision about how to make a filler run operate properly and it's optimal speed is probably not your your expertise you know enough to be dangerous.

509
00:57:24,000 --> 00:57:32,000
But working with that person who either sold the filler to this customer or who operates the filler every day is really important.

510
00:57:32,000 --> 00:57:40,000
And so you know you have these hard skills but the soft skills are just as important making sure that you are building that network of experts and you're collaborating them.

511
00:57:41,000 --> 00:57:51,000
Collaborating with them as you build that simulated world I know this is a developer webinar and we want to hear about you know programming languages and programs but I cannot underestimate.

512
00:57:52,000 --> 00:58:04,000
The this is where the I saw some you know I'll pivot here to I see things you know comments in here about well you know once we you know once we get rid of the humans where how do we you know manage the narrative.

513
00:58:05,000 --> 00:58:18,000
Don't get rid of the humans that the humans like we're stuck doing so much BS now elevate the humans and continue to build those relationships use the relationships to build that simulated world OK I'm going crazy here.

514
00:58:19,000 --> 00:58:23,000
I'm going to save that quote when I have an extra view with my boss so thank you.

515
00:58:23,000 --> 00:58:24,000
Cool.

516
00:58:24,000 --> 00:58:25,000
That's great.

517
00:58:25,000 --> 00:58:26,000
You're out of here at March.

518
00:58:26,000 --> 00:58:27,000
All right.

519
00:58:27,000 --> 00:58:33,000
So speaking about programming languages about this question came in from LinkedIn about CUDA programming and the a models.

520
00:58:33,000 --> 00:58:34,000
Don't want to tackle this one.

521
00:58:36,000 --> 00:58:41,000
I don't know if we have a CUDA expert here but no I don't think I don't think it feels like a.

522
00:58:41,000 --> 00:58:42,000
Yeah.

523
00:58:43,000 --> 00:58:44,000
Yeah.

524
00:58:44,000 --> 00:58:45,000
So we'll.

525
00:58:46,000 --> 00:58:47,000
Sorry.

526
00:58:47,000 --> 00:59:02,000
CUDA is foundational like every single thing that that you see that's built on top of machine learning and artificial intelligence that allows the GPU to accelerate it is built on CUDA every single every single operation is built on CUDA.

527
00:59:03,000 --> 00:59:10,000
Nobody talks about CUDA anymore because it's so foundational you have to have it to make these operations move quickly on a video graphics card.

528
00:59:10,000 --> 00:59:19,000
So you know it's really any any anything that you're using to accelerate the training of a neural network the inferencing of a neural network a scene graph.

529
00:59:19,000 --> 00:59:23,000
Every single one of those operations is built on the CUDA libraries.

530
00:59:24,000 --> 00:59:28,000
Great great information and may ask the panel here because I know we're at time.

531
00:59:28,000 --> 00:59:31,000
Did you want to hard stop we take a couple of questions.

532
00:59:32,000 --> 00:59:33,000
Okay great.

533
00:59:33,000 --> 00:59:41,000
Okay here's one coming in from Jesus who is asking if we can describe their app streaming over Azure has experienced latency etc.

534
00:59:42,000 --> 00:59:49,000
I believe the question means the Ocast the on US kid upstreaming that's available on Azure marketplace.

535
00:59:50,000 --> 00:59:51,000
It is available.

536
00:59:51,000 --> 00:59:56,000
It is a co-sale ready fully transactable marketplace listing.

537
00:59:56,000 --> 01:00:03,000
So some of the work that we saw in this in this session is actually using that same Ocast.

538
01:00:03,000 --> 01:00:12,000
So as a developer you can actually go to Azure marketplace today and get started follow the GitHub repo the same things at what site machine did.

539
01:00:12,000 --> 01:00:17,000
So the work that site machine did was following that ignite repo that drew was talking about.

540
01:00:17,000 --> 01:00:24,000
So that's the close partnership that we have between NVIDIA and Microsoft to showcase how to get started.

541
01:00:24,000 --> 01:00:30,000
How you can actually take those containers the kid upstreaming containers deployed on your own.

542
01:00:30,000 --> 01:00:37,000
Kubernetes clusters on Azure leverage the a 10s that are running on Azure commercials and get started.

543
01:00:37,000 --> 01:00:48,000
So it all basically starts from that kid upstreaming and then build the entire solutions like what's it was talking about with that front end web applications and integrating it into that web app.

544
01:00:48,000 --> 01:00:50,000
So it's it's all there.

545
01:00:50,000 --> 01:00:54,000
It's all available on that marketplace and get a repo for that we release at ignite.

546
01:00:54,000 --> 01:01:01,000
I think we should also admire you said that discord will have it so we should also share that GitHub repo as well for any developer to get started today.

547
01:01:02,000 --> 01:01:03,000
Okay, we'll do that.

548
01:01:03,000 --> 01:01:05,000
I think Amelia posted that thread in the chat a little earlier.

549
01:01:05,000 --> 01:01:07,000
We'll also add to the description afterwards.

550
01:01:07,000 --> 01:01:09,000
We got another question here from LinkedIn.

551
01:01:09,000 --> 01:01:13,000
Alan, this is probably great for everybody, but definitely Ganesh.

552
01:01:13,000 --> 01:01:24,000
This is, you know, obviously when when when we have business folks from different companies watching these kinds of topics and they realize, you know what, I should take a look at omniverse or take a look at open USD.

553
01:01:25,000 --> 01:01:26,000
What would you say, Ganesh?

554
01:01:26,000 --> 01:01:33,000
What what's what what is your pitch to have these have these leaders actually take a serious look at adoption.

555
01:01:33,000 --> 01:01:39,000
So I think I think it's all I believe channel kind of laid this out at the beginning of the session.

556
01:01:39,000 --> 01:01:47,000
It all starts with the the KPI, the business KPI and the real like immediate material impact.

557
01:01:47,000 --> 01:01:57,000
So the modeling line use case that Charlie was talking about, there was an operational efficiency gain or throughput improvement or yield improvement.

558
01:01:57,000 --> 01:02:00,000
That's where that's where this entire kind of journey started off.

559
01:02:00,000 --> 01:02:09,000
So try to understand how what that KPI would look like and it can be it can be related to going back to what Jeremy was talking about.

560
01:02:09,000 --> 01:02:14,000
If you take the filler simulation example, there are subject matter experts who have been doing that for decades.

561
01:02:14,000 --> 01:02:22,000
So but if they are looking at a specific business KPI that they want to hit through a simulation scenario, then then start with that.

562
01:02:22,000 --> 01:02:31,000
See where all of these new new technologies and the digital transformation, the digital twin, the simulation workflows are going to actually cause an impact.

563
01:02:31,000 --> 01:02:46,000
In case of psych machine, they zeroed in on an OE improvement, took their platform to Microsoft and then media technology took the help from kinetic vision and showcase that example with a double digit operational efficiency improvement.

564
01:02:46,000 --> 01:02:50,000
If I remember right, Charlie, I think it was like a 10 to 15 percent.

565
01:02:50,000 --> 01:03:01,000
You guys were targeting like one to two percent and that itself was like a big impact, both top line and bottom line, but they were able to showcase a double digit impact of 10 to 15 percent on top line and bottom line.

566
01:03:01,000 --> 01:03:06,000
Or I wouldn't say top line of bottom line, but the operational efficiency gain that has impact both top line and bottom line.

567
01:03:06,000 --> 01:03:08,000
So that's that's where it starts.

568
01:03:08,000 --> 01:03:15,000
It always starts with the business value and the KPI and then start with that one use case.

569
01:03:15,000 --> 01:03:18,000
Look at your existing stack of technologies that you have.

570
01:03:18,000 --> 01:03:27,000
See what the gap exists, adopt the technologies to the right level rather than like throwing everything out of the window and start from scratch.

571
01:03:27,000 --> 01:03:28,000
That never works.

572
01:03:28,000 --> 01:03:37,000
It's way too expensive and then see the incremental gain and then keep expanding from there from one line to multiple lines from one factory to multiple factories and see whether that really sticks.

573
01:03:37,000 --> 01:03:40,000
So that's that's how I think I would think about.

574
01:03:40,000 --> 01:03:45,000
I'm open to other inputs and ideas from folks here.

575
01:03:45,000 --> 01:03:49,000
Oh, I've always got a hot take.

576
01:03:49,000 --> 01:03:59,000
So, you know, for organizations when you're thinking about your omniverse investment, you know, omniverse is an, you know, an entire ecosystem.

577
01:03:59,000 --> 01:04:02,000
So you can engage it in different ways.

578
01:04:02,000 --> 01:04:13,000
This engagement through an ISV like site machine is actually a very low friction engagement because your organization probably already has Azure resources.

579
01:04:13,000 --> 01:04:17,000
And now that Microsoft has made this capability available through Azure.

580
01:04:17,000 --> 01:04:20,000
Now this is just a SaaS purchase.

581
01:04:20,000 --> 01:04:26,000
You're just calling up a company like site machine and you're saying, Hey, I would like to use your software to see my game.

582
01:04:27,000 --> 01:04:33,000
And then you're, and then it's utilizing either the SaaS platform or your cloud Azure private tenant.

583
01:04:33,000 --> 01:04:38,000
And that's a very low, that's a very low friction way to invest in omniverse.

584
01:04:38,000 --> 01:04:44,000
You may be a different type of organization and say, Hey, we have a very unique manufacturing process.

585
01:04:44,000 --> 01:04:48,000
A site machine doesn't meet all of our requirements.

586
01:04:48,000 --> 01:04:54,000
We want to build our own applications to be a technology leader in our space and lead everybody else.

587
01:04:54,000 --> 01:05:04,000
That's where then you would make the type of investment with omniverse via omniverse cloud or OBE to bring your developers and actually have them building their own applications.

588
01:05:04,000 --> 01:05:13,000
If you're an organization who can build your own applications and you want to be a leader in your technology, then that's how you would invest in omniverse.

589
01:05:13,000 --> 01:05:15,000
And that's like a strategic decision.

590
01:05:15,000 --> 01:05:23,000
Are we a developer of applications here within our company or are we a procurer of ISV applications to solve our problems?

591
01:05:23,000 --> 01:05:28,000
And that's a key decision point between leaders. Are you developing or are you procuring software?

592
01:05:28,000 --> 01:05:31,000
That's great, great, great context. Amazing.

593
01:05:31,000 --> 01:05:33,000
Okay, we got another question here.

594
01:05:33,000 --> 01:05:39,000
And actually, Jeremy, enter it quickly if you can, but I think you covered this earlier.

595
01:05:39,000 --> 01:05:46,000
How helpful is photorealistic effects when used by GUI engineers or technicians in practice?

596
01:05:46,000 --> 01:05:51,000
Yeah, I mean, I think about this as communication, right? Who are you communicating with?

597
01:05:51,000 --> 01:06:01,000
The more photorealistic your output, then the broader your collaboration is with all in your organization.

598
01:06:01,000 --> 01:06:09,000
Yes, if you're engineer to engineer, you may not need photorealistic effects to be able to solve a certain problem and change a variable.

599
01:06:09,000 --> 01:06:23,000
But if you are making a big investment and you need to communicate to leaders on opening up the budget to go ahead and make some huge cost savings for the company,

600
01:06:23,000 --> 01:06:27,000
you probably do need some kind of photorealistic output.

601
01:06:27,000 --> 01:06:31,000
And people, NVIDIA is going to give it to us for free, right?

602
01:06:31,000 --> 01:06:34,000
I'm not for free. You got to buy GPUs, okay?

603
01:06:34,000 --> 01:06:38,000
But the highly photorealistic effects should all be free in the end.

604
01:06:38,000 --> 01:06:42,000
Yes, there's some friction now, but that friction is going to decrease over time.

605
01:06:42,000 --> 01:06:46,000
Everything, you know, compute becomes cheaper over time.

606
01:06:46,000 --> 01:06:54,000
And so I do think it's very important, especially in creating these connections through your organization to get real work done.

607
01:06:54,000 --> 01:07:03,000
And the photorealism to Jeremy's point, today it feels like it's a human-to-machine interaction,

608
01:07:03,000 --> 01:07:08,000
but the future is actually towards massive amount of automations through physical AI.

609
01:07:08,000 --> 01:07:11,000
It's a lot of machine-to-machine training.

610
01:07:11,000 --> 01:07:17,000
So for a robot to really train and learn, it needs that physical accuracy.

611
01:07:17,000 --> 01:07:20,000
And that's why photorealism is super important.

612
01:07:20,000 --> 01:07:25,000
And when we're building assets, Jeremy and I have had this conversation many times.

613
01:07:25,000 --> 01:07:29,000
When we're building these 3D-USD assets, today it's about operational twin.

614
01:07:29,000 --> 01:07:33,000
But really, that's just the stepping stone.

615
01:07:33,000 --> 01:07:37,000
The real goal is to drive through that entire journey of operational simulation

616
01:07:37,000 --> 01:07:41,000
and ultimately for physical AI and robotics, right?

617
01:07:41,000 --> 01:07:44,000
And when you're thinking about that journey, you really need that photorealism

618
01:07:44,000 --> 01:07:47,000
and really high-fidelity, accurate USD assets.

619
01:07:47,000 --> 01:07:51,000
Yeah, we should do another hour on synthetic training data for vision systems.

620
01:07:51,000 --> 01:07:52,000
Exactly.

621
01:07:52,000 --> 01:07:55,000
Yeah, that is really important. That's the technology side.

622
01:07:55,000 --> 01:07:58,000
I'm always trying to cover the technology side and the human side.

623
01:07:58,000 --> 01:08:03,000
So sorry, I shouldn't have left that one off. That's like a huge, huge thing.

624
01:08:03,000 --> 01:08:05,000
Well, this is an unrelated note.

625
01:08:05,000 --> 01:08:11,000
Curious how one decides optimal resolutions of point clouds that can be fused with realistic data.

626
01:08:11,000 --> 01:08:15,000
Do you have any thoughts on this one?

627
01:08:15,000 --> 01:08:18,000
Yeah, these are all...

628
01:08:18,000 --> 01:08:23,000
It's like what's available and fast, right, and can solve the problem.

629
01:08:23,000 --> 01:08:28,000
It's a multi-variable solution and it's always changing over time.

630
01:08:28,000 --> 01:08:35,000
So we've chosen a certain type of resolution because we're talking about, I'll say,

631
01:08:35,000 --> 01:08:40,000
human-scale, facility-scale decisions, right?

632
01:08:40,000 --> 01:08:45,000
We also operate three North Star Industrial CT scanners here.

633
01:08:45,000 --> 01:08:47,000
So that's a completely different technology.

634
01:08:47,000 --> 01:08:49,000
It scans down to the micron level.

635
01:08:49,000 --> 01:08:59,000
We can detect the porosity and the femur of a mouse or a void in a solder pad on a BGA.

636
01:08:59,000 --> 01:09:04,000
So you really need to use the right technology for the job,

637
01:09:04,000 --> 01:09:06,000
but it's going to be some mixture of those things, right?

638
01:09:06,000 --> 01:09:10,000
The expense of the technology, the value of the problem you're solving.

639
01:09:10,000 --> 01:09:13,000
But if you have questions, hey, you know who you can call.

640
01:09:13,000 --> 01:09:15,000
Yeah, that's great. Well, thank you.

641
01:09:15,000 --> 01:09:19,000
It's fascinating. I never thought of a mouse femur before.

642
01:09:19,000 --> 01:09:25,000
So Rich is asking on LinkedIn about, is this workflow available with academia?

643
01:09:25,000 --> 01:09:27,000
What do you mean, of course? Why not?

644
01:09:27,000 --> 01:09:32,000
We do have an HDR team here actually at NVIDIA that I can help put you in touch with

645
01:09:32,000 --> 01:09:35,000
if you have specific questions about your school working.

646
01:09:35,000 --> 01:09:37,000
But Ganesh, I don't know if you have any other thoughts on this.

647
01:09:37,000 --> 01:09:39,000
No, this is great.

648
01:09:39,000 --> 01:09:42,000
Thank you for hosting it. Thank you for inviting everybody.

649
01:09:42,000 --> 01:09:45,000
And thank you Psych Machine for an awesome work.

650
01:09:45,000 --> 01:09:51,000
I know we had a really short ramp of getting all of this integrated up and running in six months.

651
01:09:51,000 --> 01:09:54,000
Thank you Kinetic Vision for an incredible partnership and support.

652
01:09:54,000 --> 01:09:58,000
You guys have been awesome with NVIDIA for over the years and of course for Microsoft.

653
01:09:58,000 --> 01:10:01,000
So thank you Drew and the entire team.

654
01:10:01,000 --> 01:10:06,000
What we started off like a year ago, it's really like blossoming and this is just the beginning.

655
01:10:06,000 --> 01:10:08,000
We're going to grow even more.

656
01:10:08,000 --> 01:10:12,000
And Ed Mar, I hope you will invite us again with more exciting things.

657
01:10:12,000 --> 01:10:16,000
Of course. I always like to ask this as a last round.

658
01:10:16,000 --> 01:10:18,000
Ganesh, you just went. So you passed this.

659
01:10:18,000 --> 01:10:22,000
But does anyone have any last kind of words of wisdom that you want people to really remember?

660
01:10:22,000 --> 01:10:25,000
What's your key takeaway you want people to come away with?

661
01:10:25,000 --> 01:10:27,000
We'll start with, we'll go backwards actually.

662
01:10:27,000 --> 01:10:32,000
So Drew, what's your key takeaway here if we want people to remember?

663
01:10:32,000 --> 01:10:37,000
Key takeaway. NVIDIA and Microsoft have a beautiful relationship.

664
01:10:37,000 --> 01:10:44,000
And we're also trying to design industrial standards together as well.

665
01:10:44,000 --> 01:10:52,000
So just stay in contact with us and hopefully it gives you confidence to build whatever you all dream up.

666
01:10:52,000 --> 01:10:54,000
So that's what my takeaway is.

667
01:10:54,000 --> 01:10:57,000
Amazing. Great words Drew. I'm glad you went.

668
01:10:57,000 --> 01:11:01,000
Sheru, you're next.

669
01:11:02,000 --> 01:11:07,000
Oh, okay. Yeah. So I think the key takeaway I'd love for everyone to remember is, you know,

670
01:11:07,000 --> 01:11:16,000
the domain specific intelligence and architecture that Sight Machine provides is critical for manufacturers to actually deliver real value.

671
01:11:16,000 --> 01:11:19,000
And that can be a great framework for other verticals as well, right?

672
01:11:19,000 --> 01:11:25,000
Understanding the entire technology stack that's necessary for AI and data transformation, I think,

673
01:11:25,000 --> 01:11:27,000
is becoming much more fascinating.

674
01:11:27,000 --> 01:11:36,000
But you know, understanding each layer and what we provide in addition to Microsoft and NVIDIA platforms would be a great thing to keep in mind.

675
01:11:36,000 --> 01:11:39,000
Great words, Sheru. Thank you.

676
01:11:39,000 --> 01:11:41,000
Sudhir, what's on your mind?

677
01:11:41,000 --> 01:11:48,000
Yeah, I'll channel Jeremy a little bit and say this, you know, sort of the two modes come to mind, right?

678
01:11:48,000 --> 01:11:59,000
Like this partnership and what we've achieved showcases what can be done with the power of omniverse and Microsoft Azure and Fabric, right?

679
01:11:59,000 --> 01:12:12,000
And if somebody who's like really wants to put on a developer hat and play with it and build these technologies, like this is the, you know, sort of blueprint on how it can be done and how it has actually been done.

680
01:12:12,000 --> 01:12:24,000
That said, as Sheru mentioned, a lot is based on data and the domain expertise and so on, and that's where Sight Machine comes in.

681
01:12:24,000 --> 01:12:40,000
And so if you have looking for a solution that offers end-to-end with the data, with all the integrations in place and has done the heavy lifting for you, please feel free to contact us and we have the solution for you. Thank you.

682
01:12:40,000 --> 01:12:45,000
Amazing. Okay, Jeremy, you have the last word here.

683
01:12:45,000 --> 01:12:47,000
I'm afraid.

684
01:12:47,000 --> 01:13:02,000
Yeah, well, so in manufacturing and supply chain, most people's operations run around 50% of the nameplate of what it was designed for, okay?

685
01:13:02,000 --> 01:13:09,000
At every big company, these are billion-dollar problems and they're only going to be solved digitally.

686
01:13:09,000 --> 01:13:16,000
So if you're looking at like, how much does this cost? Should I do it?

687
01:13:16,000 --> 01:13:23,000
Yes, the answer is yes. It's 10 times faster and cheaper doing it digitally than going in and doing things manually.

688
01:13:24,000 --> 01:13:32,000
So the biggest issue is not the cost. The biggest issue is not the technology. The biggest issue is a people problem, okay?

689
01:13:32,000 --> 01:13:41,000
This is about multidisciplinary experts understanding all aspects of the problem. It's about having a vision.

690
01:13:41,000 --> 01:13:50,000
I love that question. What do I do about IT and manufacturing and them being misaligned? Get them aligned, people. They have to be aligned to solve these problems.

691
01:13:50,000 --> 01:13:57,000
So much of what we're talking about here today, this is amazing. NVIDIA has done an amazing job of making sure that technology is available.

692
01:13:57,000 --> 01:14:03,000
Fantastic. Developers and ISVs like Sight Machine have made sure that they use that technology and put it into products.

693
01:14:03,000 --> 01:14:07,000
And then, you know, Kinetic Vision is helping customers solve problems every day.

694
01:14:07,000 --> 01:14:16,000
But what I'm seeing on the front lines is people issues and companies really coming to grips with how to solve these problems in their organization.

695
01:14:16,000 --> 01:14:21,000
So try to build those relationships. People get them in front of the right stakeholders.

696
01:14:21,000 --> 01:14:27,000
Make sure that you're supporting, you know, your visionary leaders, making sure your visionary leaders are working with their visionary developers.

697
01:14:27,000 --> 01:14:34,000
It's a big people challenge, but I know that we'll be able to get there. So thanks again.

698
01:14:34,000 --> 01:14:40,000
That's awesome. I'm so glad. That was a great closing remarks. I want to thank all of our amazing guests from Sight Machine, Microsoft, Kinetic Vision,

699
01:14:40,000 --> 01:14:46,000
and of course, my colleagues here at NVIDIA for sharing your amazing insight. Each one of you has been extremely helpful.

700
01:14:46,000 --> 01:14:53,000
We've seen how Agentec AI, OpenUSD, and Jill Twins can help factory teams spot issues faster and optimize production lines.

701
01:14:53,000 --> 01:14:58,000
And it seems like this is just the beginning of what's possible. So a big thanks to all of you who joined us live out there.

702
01:14:58,000 --> 01:15:01,000
Great comments and questions. We really love the projects that we shared.

703
01:15:01,000 --> 01:15:06,000
If you missed part of today's session, don't worry. The replay is available using the same link you're at right now.

704
01:15:06,000 --> 01:15:10,000
Or just go to our YouTube channel anytime, NVIDIA Omniverse. Go to live and you'll find it there.

705
01:15:10,000 --> 01:15:15,000
And as I'm showing on the screen now, this kind of experts presentation is valuable to you.

706
01:15:15,000 --> 01:15:22,000
I highly encourage you if you can make it to GTC DC. So our first one, Washington DC, you're going to see Jensen there providing a great keynote.

707
01:15:22,000 --> 01:15:28,000
So I hope everyone there can make it. I will be there. So let me know if you're going and we can grab a coffee or something together.

708
01:15:28,000 --> 01:15:31,000
Until next time, thank you so much for joining us, everybody.

709
01:15:31,000 --> 01:15:36,000
It's been an absolute honor to join the panel with you all today and with you all there in the audience.

710
01:15:36,000 --> 01:15:38,000
Have a great rest of the day.

711
01:15:38,000 --> 01:15:39,000
Thank you so much.

712
01:15:39,000 --> 01:15:40,000
Thanks.

713
01:15:40,000 --> 01:15:42,000
Thank you.

