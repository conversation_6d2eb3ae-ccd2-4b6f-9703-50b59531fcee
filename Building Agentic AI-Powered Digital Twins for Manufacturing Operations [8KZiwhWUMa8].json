{"text": " Hello, everybody. Welcome to the OpenUSD Insiders livestream. I'm very honored to have you join us today. We have a very special episode with some amazing partners. We're going to dive into that topic in just a second, but we're going to be covering building an authentic AI powered digital twins. We would love to invite your questions, your comments. Let us know where you're watching from. If you've got any interesting projects related to this, we'd love to hear about it also. So go ahead and introduce yourself in the chat right now. We're going to hit the questions. There are probably a few different points during this hour. So if we don't get you right when you post, we're trying to keep track of them. We'll hit them by the end, most likely. OK, so thanks for joining us. So right now, I'd like to also very attention to this great learning path we've got here. Brand new new digital twins for physical AI. We just came back from SeaGraph. A lot of excitement on this on the subject, including at labs and sessions. This QR code will bring you to that brand new learning path. For those of you who've been watching for a while, a part of the community, you all know that these learning paths are a fantastic free resource, self-paced courses, basically, on a various number of topics, including OpenUSD and robotics. And now we have digital twins for physical AI. So use that QR code to find it. If you need the link, we'll also put it in the chat, but that is a super helpful resource for all the developers out there. Here's another great resource, developing and deploying your own omniverse kit apps. This QR code will also bring you the latest information there. Another great resource for leveraging the community on deploying your own kit app is our Discord server, where we have developers talking about this very topic pretty much on a daily basis. So engage with other community members there. We'll post a link in the chat. Amazon Devices and Services achieves major step towards zero-touch manufacturing with NVIDIA. This is also an amazing story that just came out. This QR code will bring you right to there. If you have any questions on this, feel free to pop on over to the live streams channel. We have our Discord server. Feel free to tag me, and I'll get you someone who can help in the chat on this. Developers build fast and reliable robot simulations with NVIDIA omniverse libraries. As you can see, we're all about QR codes today. So here's another QR code for you. That'll bring you right to the link. And obviously, if you're watching this live, you will get these links in the live chat also. No need to bring your phone up to the screen unless you want to. You're free to, of course. But feel free to check out that resource. We'll be happy to also help you with that. And how to instantly render real-world scenes in interactive simulation. Another great article. These articles are actually a great resource for people because they leverage not only our NVIDIA developers, but also members of partner teams, members of the community. Really great use cases and workflows from start to finish. So highly encourage you to check out each of these, but this is how to render real-world scenes in interactive simulation. NVIDIA opens up portals to world of robotics with new omniverse libraries, Cosmos physical AI models and AI computing infrastructure. Cosmos is an amazing world foundation model we've been talking about a lot lately. This will help give you more insight into how to leverage that to the fullest. Again, at Seagraph, we just wrapped up a couple weeks ago. It was all about physical AI, robotics, Cosmos. It really showed a nice transformation in what's happening in the graphics industry. So feel free to leverage that QR code to get right to that article. And another exciting thing that also was debuted at Seagraph was our open USD certification. For those who attended Seagraph in person, you were able to actually take the certification exam for free. Otherwise, there's a small cost attached if you are taking it online and here's the QR code for that. I highly recommend anyone out there who's been floating around to open USD for a while, go ahead and challenge yourself to take the certification. I do think it's a great idea to take that learning path open USD before you take the certification exam. You're going to get all the information you need by following along with those learning path self-paced courses. And then you'll have a great certificate at the end so you can show the world that you are open USD certified, making yourself even more knowledgeable and more of a thought leader whenever you make your posts. These are the links that will give you the various resources we have in the community. We have our calendar, which is the first link there, which will bring you all the upcoming live streams, office hours, study groups, key events that are happening, including GTC DC, which is coming up. I can't believe there's not a slide on that. I'm actually going to bring it to the web page and I'll show it to you later. But GTC DC is coming up. I'll be there. We always post those events in our event calendar. Very easy to subscribe and always find out about the latest. Our forums are a great place to post your questions. If you have any blockers, absolutely leverage the forums for pretty much everything. Some exceptions include Isaac Lab, where we prefer you to go to the GitHub page for Isaac Lab, but Isaac Sim has its own section on the forums. Our Discord server, which I mentioned earlier, that's an amazing resource. We have thousands of developers that are on there posting regularly about their projects and assisting each other on a day-to-day basis. Lots of great channels. It's well organized to find what you need quickly. Just use the search bar on the top right. Type anywhere you want. You'll get there in a flash. Finally, that last link is our community page, where you can look up things, including our ambassadors and other resources we have in the community. That is a lot of information for you. I hope everyone got good notes there. Now I would like to bring in my great colleague, Ganesh here. Ganesh, how are you doing? It is an honor to have you joining us here today. This is a very special episode we've got today. I'm building Agentec AI-powered digital twins, isn't it? Exactly. Thank you, Edmar, for having me again. I'm excited to share the next 15 minutes with Sight Machine, Kinetic Vision and Microsoft on how we are actually taking digital twin directly into operational shop floor experiences. And then how are we actually marrying that with Agentec AI? I'm really excited to be here. Thank you for inviting me. Of course. Let's bring in these very special guests. This is super exciting. We have Jeremy here. Hey, Jeremy. Hey, everybody. I'm from Kinetic Vision. It's so great to see you. Tell us a little about your background in Kinetic Vision. Yeah, I'm hailing here from Cincinnati, Ohio, a great hub of US manufacturing. I'm the CEO of Kinetic Vision. We develop and integrate digital twins for manufacturing and supply chain. We're an NVIDIA partner. We're a Microsoft partner and we're a Sight Machine partner. So really excited to be here. Talk with everybody today. And Ganesh and Edmar, thank you for inviting me. Of course. I mean, I love to see when partners are working so closely together. You see some amazing things happen with these billion of minds that are working together. Speaking of brilliant minds, let me bring in my partner, Sasha here also for NVIDIAside. Hey, Sasha, how are you doing? Good morning and good afternoon or good evening, depending on where you are. Hi, everyone. I'm Sasha Bichon. I work with Ganesh and Jeremy and the rest of the team and trying to bring together all of the good work we've done here in terms of digitization, if you will, and how we apply that into factories of the future. Amazing. I'm so glad to have you here. Sasha is an amazing person on our team, so he's going to have great context as well as Ganesh throughout this episode. Here's a good friend also, a veteran of the live streams. Hey, Drew. Hey, Edmar, how are you doing? How you been, man? It's great to see you again. Doing good, doing good. Yeah, you too, man. So you have a special role. Why don't you tell me what you do over there at Microsoft? Well, at Microsoft, basically I work with a group called Industrial Solutions Engineering. So basically we go into customers and innovate with them, with partners like NVIDIA, you guys. And we also have, I also work with a team that's actually in Houston, Texas. They have an actual facility where we build up hardware, like, for reels, and then, like, with PLCs. And we then test, like, getting data into the cloud and then doing stuff with it. And yeah, I've been working with Sasha, Ganesh, and you all, Edmar, like, for a while. Especially Ganesh, causing chaos wherever we go. So it's great to be here and good to see you. That's awesome. Well, thank you. I mentioned in my promo earlier I've had a lot of fun in rehearsals with this whole team of people. Let's bring in the last two people here. We've got Sheru. Hey, Sheru, how are you doing? Hello, everyone. Nice to meet you. Why don't you tell everybody about your role and your background at Site Machine? Absolutely. Hi, I'm Charu Kalluri. I'm a director of product marketing at Site Machine. Site Machine is an industrial AI platform focused on delivering manufacturing outcomes at scale. Over the years, we've been working very closely with Microsoft and NVIDIA and our kinetic vision. So just really excited to, again, be here with everybody to talk about the compelling value of all of these technologies put together. Awesome. We're very excited to have you here. People are going to really love it. We're going to show off in a few minutes. Speaking of showing off, we've got Sidhir here. How are you doing, Sidhir? Hey, I'm doing great. Nice to see you all. Super excited to be here. I run engineering at Site Machine and we work a lot with Microsoft and NVIDIA and all of the folks on the squad and others to bring together Site Machine and Microsoft and NVIDIA, integrating omniverse and bringing true value to our management. That's awesome. Well, listen, I can't think of a better crew to tackle this topic today. So I think everyone should buckle up. I already see a ton of comments coming in. That's fantastic. We're going to get to some of those in a second, but why don't we set the stage here, Ganesh and Sashi, for what we're going to really dive into here. Sounds good. So what are we going to see in the next 15 minutes or so? We're going to actually talk about how digital twins are actually shaping and driving the next wave of AI, which is deeply rooted into physical AI. And then we're going to actually talk about how Site Machine as an ISV has taken the technology from NVIDIA as well as from Microsoft and bringing that to shop floor and factory operations with the help of kinetic visions where they are actually providing a specific key ingredient in building the digital twin itself and how the 3D assets are actually getting converted into USD and how that is actually driving and powering the whole digital twin physical AI transformation. So that's what we're going to primarily cover in the next 15 minutes. All right. Very cool. And I think with that, I think Sheru, are you going to kick things off for us here? Yeah, let me... Oh, go ahead. Sorry, are you going to say something? Oh, no, sorry. Go ahead, Sheru. Yeah, go for it. All right. I wanted to talk a little bit about the specific manufacturing use cases that we're going to be solving with the solutions, because again, technology to solve real problems is what's very exciting to all of us. So give me a moment so I can share my screen. Okay. And while she's going to go ahead and sharing her screen, I definitely invite everyone to start posting your questions and comments. We will be hitting those throughout the hour here. And I think, Sheru, I see your screen is ready for me to share. Yes, ready to share. All right. Here we go. All right. Like I just mentioned, we are an industrial AI data platform. What we wanted to focus on today was what are the critical production challenges on the manufacturing shop floor. We are seeing operations teams constantly under varying conditions trying to achieve a lot of outcomes at once. These outcomes are typically things like line throughput increase, which is really how do I make, produce as much as I can and run as efficiently as possible. Schedule adherence, how do I make sure I'm not behind on production? How do I maximize my machine efficiency and maximize availability so that every machine is running close to its potential capacity? So all of these problems are things that we've been solving for over 10 years already for manufacturers. So that's sort of the landscape of challenges that customers are facing. But with all of the changes in technology, AI and, you know, innovations coming out, what manufacturers are struggling with most are things like user experiences. How do you make sure that all of these are adopted on the shop floor? How do you make sure that everybody in the enterprise has the same view of the line? And these are very complex lines and they're very large production lines. If you actually go to these plants, you literally can't see a couple of machines away from you. You can only see your machine in the next one, but you are impacted by what's happening on the rest of the line. So understanding that system level, you know, process is extremely important and very challenging to do today. So our vision, which is now being realized as we speak and being deployed with our clients is really about the power of 3D immersive visualization, agentic AI for insights, as well as a real-time data platform. So we have found that this combination of technologies really provides the visual elements so you can see what is happening. Everybody has a unified view of the enterprise and the line. Agentic AI generates recommendations under varying line conditions. So it's adapting to changing scenarios and it's providing the recommendations at the point of consumption against the 3D layer. So what you can see in this visual and what we'll be digging into and doing a deep dive on with the rest of our amazing team here is really how are these recommendations generated? How are these visualizations available and how does all of this work together seamlessly? They give a complete unified experience. So this is just an example of some of the real-world results we're seeing with our clients and I just wanted to paint the picture for what our manufacturing teams are looking to do. That's all I have slide-wise so I'm going to stop sharing. Very cool. And we have a quick video. Did you want me to play that now or is that for a little later? Sure. I think, yes, I can speak for a minute as the video plays. Edmar, that would be great. I think it's really important to understand again what the complexities are on the shop floor. So when you see these 3D views, you'll really understand and as the video plays, you can see that this is a full view of a bottling line. Now, this is again, based on the scale, you can't really see how large it actually is, but you can see that in this 3D view, you can see the machine status and speed instantly. You do not have to talk to operators and radio people or walk across the line or make decisions with incomplete information. So again, the real focus is on providing those immersive 3D experiences with insights all in one space. So the goal of this is to turn everybody on the shop floor team into a data-driven decision maker. Amazing. Very cool. That's so helpful to have that visualization. Obviously, there's a lot of opportunity for companies to leverage this kind of stuff. I guess one thing I always think about when I see something that looks so amazing like this is the work involved. I think sometimes people, companies might look at this and be a little overwhelmed or, oh my gosh, what's involved in that? But I think it's fair to say that people could take a very layered or phased approach to these kinds of scenarios. You don't have to go all in with everything at once. I'd be curious to see what you guys all think about suggestions for companies maybe watching that. What would that layered approach look like or fees for getting started? I think we can answer that real quick, Edmar, and then as you see what kinetic vision and site machine and the Microsoft team have done here, it'll unpack all of those. But I think the key ingredient starts with how the customer thinks about a brownfield operation. So the example that Charlie was talking about is an existing operational bottling line. They already have systems and processes in place. Some of them have been actually deployed over several years. So it's part of that whole digital transformation journey where you need to actually deploy more advanced and more latest technologies to harvest that data from the shop floor, which is what site machines systems do. And then you need to bring that into the cloud, which is what we're going to talk about in a second with the Microsoft architecture. And then you start applying more advanced digital twin technologies with what we were showing earlier with omniverse and kid app streaming, which is what Sashi is going to talk about. So there are existing systems in place and then there are add-ons that is required to harness that data sets to really drive the digital transformations and building the digital twins. So we'll get into those in a second. I think with that, we're probably going to hand over to Drew and Sashi to really unpack that architecture. Amazing. Thank you, Ganesh. Okay, so Drew. Okay, Drew is fixing his microphone right now. So we'll let Sashi take the start here. Sure. Yeah, so as Drew is fixing his microphone, I think, Drew, are you able to share with me? Oh, you're on man, that's great. The slide that you had on our architecture. Yeah, so Sashi, I'll just show the initial thing we did back in the night first and then I'll pass it to you. Is that cool? That's great. All right. So Microsoft and NVIDIA have been working together very closely for a while now to try to come up with a solution that combines the two platforms. And a solution that's also scalable, right? So at Microsoft, we do have this facility in Houston where we build hardware, pull out with PLCs. What you're looking at right now is a fluid process machine, which has pumps, valves and whatnot. And you can see the omniverse with the 3D context. You can see on the left the data from fabric. That's actually an embedded PBI in a single pane of glass. And so we created this, demoed it at Ignite with kind of an inspirational architecture for the future, which the whole purpose is to inspire companies like Sight Machine to take a hold of and kind of have like a base template where Microsoft and NVIDIA are like, yep, this is a good approach. And it gives folks confidence that this is the right way to go when doing like this is an operational use case view. So essentially then, so post Ignite, we posted an open source accelerator repo. And then Sight Machine got involved and worked with us and they took it and ran with it. And then went crazy cool in reality with the Coca-Cola consolidated bottling line. And let me pass it to Sashi right after I go to the architecture. So this is the architecture and Sashi take it away. Yeah, so thanks, Drew. So what we're showing here is a design pattern, if you will, or architectural design pattern for creating an operational digital twin. Now, this is an exemplar. It's not meant to be like the end off, but it gives you an idea of what to consider as you're trying to build out a capability. And I know in chat there's questions about like, how is this effective in terms of auto realistic renderings for users and so forth. We'll get to that. Let me walk you through this and then we can start answering some of those concerns too. Fundamentally, when we start thinking about these kinds of systems, where we start is usually in the data, right? And so that's on the left most side of this where you have edge computing, pulling in data. You're going to take that data and you're going to have to do some kind of processing and staging of it into an environment. In this case, we're using things like Azure IoT operations. We're using Arc enabled Kubernetes to do that kind of stage. Additionally, you'll have some level of 3D scene creation, and that has to also get managed. So in our case here, we started putting that together with Azure Blob Storage. These two pieces of information need to get correlated. And that's where we're correlating it with Omniverse Kit and with Microsoft Fabric and Power BI. So Fabric, Real-Time Intelligence and Azure Functions will take that data that we just received, convert it into more actionable data, convert it from, you know, bronze data to silver and gold data. And then the Power BI elements in block two will start overlaying that data into a user interface for a user. That's getting combined with the Omniverse Kit app streaming, the photo realistic rendering. Now, the big question that most people will have is, well, okay, you've got these two streams. You've got this 3D enrichment or 3D data and you've got this IoT data, but how do you connect that? And that's where it's kind of powerful. We put in enrichment into the USD. So the USD files that we create in Azure Blob Storage with the 3D data, we enrich them with ID information and other metadata. I sometimes refer to that as syntactical sugar. And that's what's brought in into Omniverse Kit and provides the front end. And the front end is able to then map between the data sources coming in from Fabric to the data source in Omniverse and give a user an immersive 3D environment plus the dashboarding effect. So why, right? Like, okay, this is great. And one of the questions is usually, so you can do in 3D, but how does this really help? Now, if we can imagine in the world, as we're progressing into decision making, we want engineers, users and developers to be able to quickly contextualize the space that they're in and be able to solve problems in that space. Fundamentally, if you've got an area in a factory or you have a stoppage or a blockage and you want to understand where that is, what's the context around it? Maybe you're looking at understanding, okay, what's the ingress path to get to that location to service something? Or what do I need to change? You need an immersive 3D environment. The photo realism part of it helps you in understanding both what you might do as a human operator, but also what you might do downstream when you're doing things like computer vision or other AI genetic kind of workflows for simulation purposes, for training, what I've been so far. So this is kind of like the starting point in which you can then have downstream use cases. And the starting point then becomes the same single pane of glass that you would use for both operations and for simulation environments and what if analysis and so forth. With that, I'd love to hand it over to Jeremy and he can talk through how we created the digital twin. It's great. Thank you, Sasha. It's awesome. Jeremy, looks like you have the floor now. Are you ready? I'm ready. No pressure. No pressure. No, so let's see here. Edmar, I've got a couple of things. There you go. All right, you're reading my mind here. So let's start with this. This is really just a basic representation of what we do to create the beautiful 3D asset that is inside of factory operate or inside of site machine. And all I do is just, you know, we have a lot of people on this call. They're going to be at varying levels of knowledge about all this. I saw some great questions about, you know, how do you create these assets? What are the steps? And I'm going to take some time to go through that. But, you know, Edmar, you asked one very interesting thing about, hey, how does a company take like a layered approach to this? And because we are, you know, integrating these solutions, we meet our customers where they're at. And that's really important. And so they may not be ready for a full blown site machine digital twin. They may need something a little more basic than that. So we developed this really simple process called the choir activate optimize. And then, and then at the end, you know, collaboration through an amazing platform like omniverse really brings all of your stakeholders together. So from the choir standpoint, we're just, we're just talking about scan data, get your data. Most companies do not have a hold of their data. Activate it using real simple software and then optimize it using data analysis that site machine provides or simulation. And then in the end, when you have all of this fantastic compute available, you can immerse yourself and really create a connected experience with all of your users. And so I also saw a question about, you know, how do you know when to use like a really high fidelity 3D model versus really basic representation? You know, I always err towards immersiveness. If you can do immersiveness without friction, then the human, we are humans. The human experience is going to be better. You're going to be inside the digital twin. So the more that you, where you get into issues is where you have, you know, lots of data. It's complex or you, you know, can't display it very well or there's a lot of friction and understanding it. But if you can without friction err towards immersiveness, you're always going to be in a better spot. Sometimes you need a little bit of visionary leadership there to, you know, kind of push an organization that direction. But that's where we always tend towards. So, you know, I have a really simple video that makes this stuff look simple and feel simple. It's, I haven't labeled this video one. I don't know if you can flip over there real quick. Let me see. I'm looking at it. I see. Oh, yeah, I do see it. Let's see. I got three. I got all kinds of content. Even small delays can snowball into big disruptions. That's where digital twins come in with our acquire, activate, optimize process. It's fast, easy and low risk. We start by scanning your facility in stunning detail up to 10 times faster than traditional methods with zero disruption to operations. That scan becomes a powerful 3D digital twin, enabling virtual design, remote tours and supporting system updates, layouts, training and safety planning. In just 60 days, we uncover eight to 10 times first year ROI with payback in less than three months. AI delivers smart insights and actions to drive fast solutions and improve overall operation effectiveness. Dashboards track performance in real time. AI flags a labeler jam, triggers a fix and recommends a second forklift and dual labeler to boost material movement and throughput. Have a problem or want to try a change? Simulate it first. No risk, no downtime. Your digital twin drives smarter decisions. Once it works in one facility, it scales easily, making digital twins the perfect solution to transform your operations. Okay, Edmar, that was marketing glitz from our marketing department. Makes it look simple. Well, I gotta tell you, there was a couple of pieces of information that were super compelling. The ROI of three months, that's pretty amazing. And also the granularity of detail, down to five millimeters. It's pretty wild. Yeah, so can you queue up, there's something called Video 2 in there? So this is going to be a little more like, what does it look like when you do this stuff? Like, who's doing what? When are they doing it? What programs are they using? So just queue that up, that'll be another minute, and then I'll talk a little more after that. Okay, here we go. And there's no audio on this. So really all we're showing is just a case study here of taking this acquire, activate, optimize process to a real facility. This is a distribution center. So Brian's out there scanning it. He looks a lot like that little iconography we made of him. We use real, we take that data, grab that 3D data, and then we use some other NVIDIA partners. This is Preview 3D on this particular project to really quickly get measurements, get panos. This is a simulation tool that we happen to use called FlexSim. And just, you know, once again, an alternate process, not related to what we're doing with site machine here, but another method to optimize the site. Taking those, we're actually doing virtual camera simulation here just to make sure, even stuff that's as simple as get your camera set up in the right place before you install them, that can be all done virtually. And it saves a lot of time and it saves big mess ups. So just a couple of visuals of a recent case study, that's a real project we did for a real customer. And we did deliver, that was like a three month payback. And there's some big decisions being made off of what we found digitally. The biggest thing is not disrupting the operation. That's kind of the biggest thing. And then just queue up my PowerPoint if you wouldn't mind. Okay, let me move this one first. And then let me see, your PowerPoint is right here. Yeah, I win the contest for most media presentations. I love it. Okay, we saw that, we saw that. Okay, so how do we do this? First of all, do not underestimate, you need some great people to do this work. Kinetic Vision has a lot of amazing people, but we're talking about between site machine and Kinetic Vision. We have data scientists, we have mechanical engineers, we've got machine learning engineers, we've got software engineers, technical artists. It's a diverse team. So when you're making decisions about building even just the 3D asset, it's really helpful to have subject matter expertise alongside your tech artists when you're building that asset. So just a little bit about, you know, do not forget about the people. AI is amazing, but we, at least for probably the next five years, we're still going to need people. Okay, little nuts and bolts on like, what do you use to go do this? We're, what we're doing, I'll look at the end here really quickly. We're, you know, we're publishing a kit app, you know, kit app USD that's streaming within an omniverse, like visual context, and that's part of the site machine application. And so what we're doing is we're delivering a USD asset that can deliver that high fidelity, fully realistic, interactive view. What we start with is this 3D scan. There's a lot of choices here, everybody. We happen to use Naviz, Faro and Leica. Naviz scanners are very fast, down to five millimeter accuracy. You're doing a lidar scan, capturing millions of points as you go. Faro and Leica are more terrestrial scans. And so you're setting your, you know, you're setting your tripods and you're capturing data, but they're much more accurate and you get a lot of higher fidelity. So we typically use Naviz for scanning a full site. And then if we have particular machines where you really need really accurate user interface details and accurate, like mechanical details, we'll go in and re scan with like a Faro or Leica to get those high details. From a reality capture perspective, we're taking those scans and we're activating them with the real simple off the shelf software that we integrate. You have a few choices there too. There's a big ecosystem out there. We use preview 3D here at Kinetic Vision. And let's see, we also use Reality Cloud Studio. So these are two great programs. There's a handful of them out there, but what's nice, we work with mostly big companies and a lot of them prefer to procure software instead of using something open source, something that's supported. But there's open source options also. Then, you know, once we get that reality capture data, which includes really high resolution, panographic images, sometimes we're creating a mesh that's got materials applied to it. We then pull that into a 3D digital content creation package. Pick your package, 3D Studio Max, Blender, Maya. There's a lot of great choices there. We happen to use all three of these. And then you're following traditional artist workflows. You're either doing direct modeling. You're using a model library and maybe bringing a model in. You're referencing those scans for the geometry sizes. You're perhaps re-topologizing some geometry. And then you're building your assets in 3D Studio Max. I'll put a footnote here. There's a lot of exciting, exciting technology around the generative creation of these assets. NVIDIA's got some great open source libraries out there that they're publishing with their research teams. And, you know, they're worth checking out. We're not fully using them in these workflows yet, but there's going to be a whole slew of software packages and workflows available around using generative AI. So, you know, we're going to be using them for 3D. And then, you know, once we have that 3D Studio Max or Blender asset, in order to access it programmatically within Sight Machine, we're, you know, grouping portions of that USB file. We're making them available so they're triggered by a sensor. We're setting camera views. And so what we're doing that in is just a little application that we built called Data Vision. It's built on the Omniverse SDK. And it's really using those great resources from the Omniverse SDK to build 3D application. This allows us to layer in some extra data that Sight Machine needs to hook up to their platform. So, and that's most of it. I'm sure there'll be some questions, but just to cap it off, this is just one of an asset from one of our pieces, one of our recent projects with Sight Machine, just showing these steps, going from 3D Point Cloud to Reality Capture Asset to Assimilation Asset to a really beautiful photorealistic asset with animation done through the Omniverse SDK. And that's what I got for you, people. That was really cool. Show me. I didn't see that before. That was really amazing. I know. Ganesh, you're always, yeah. Ganesh is like, he's like, you never show me anything. Like, I just got to see the latest stuff. So, yeah, there you go. I love it. That is so wild. I think, yeah, it's a lot of impressed people watching in the chat as well. Very cool. Let me see. And I think I'm going to leave this on for me for one second. And there we go. Okay, cool. That's wild. So, anyone who's just joining us, thank you. Welcome to the stream. We're talking with Sight Machine, Microsoft Kinetic Vision, and of course, folks from the NVIDIA team about how Agentec AI and digital twins are transforming manufacturing operations. Be sure to stay active in the chat. We see a lot of questions and comments coming through. We'll try to address those. But that was fantastic. Thank you so much for carrying us through that really nice journey, Jeremy. You are welcome. Okay. All right. So, of course, now that we've talked about scanning and creating USD, I think we have our friend, Sudhir here, who's going to bring us into, to bring us home here, so to speak. All right. Can you share my screen? Yes, let me see if I can. Yeah, I think I got it right here. Okay, we can see it. Awesome. Awesome. Thanks, everyone. Super exciting. So you guys saw how Shashi and Blue presented the reference architecture that was done at Ignite. Super cool, right? Shado presented the use case that we are talking to customers about and how we are showing value with this ecosystem. And then Jeremy presented how they take all these scans on the factory and convert them into meaningful USDs that we can then use. I'm going to just put it together to show you how Sight Machine took all these pieces together and built an architecture that shows value to our customers with all of these pieces put together. So here's the technical architecture diagram. It's a flavor of the reference architecture that you saw earlier. I'm going to highlight some of the changes we did or how we added on our technologies to make this even more compelling for our job customers. So first off to recap Sight Machine is a AI manufacturing data platform. We take data from all these data sources that you see on the left hand side. We standardize them, convert them into standard data models, thereby enabling things like analytics, general AI, digital twins and so on. So here you can see the first step going through the steps here is our factory connect application that Sight Machines application that runs on IoT operations as Shashi was mentioning in the Unable Kubernetes cluster. This gets all the data and passes it on to the next step. We also have this data powering the factory operate and factory build platforms which are Sight Machines proprietary platforms to process and model the data for use in the kit application as well as for further analysis and AI. All of this is running on the Microsoft Azure ecosystem to deliver a scalable unified solution. So let's look at each component of it by drilling down into each and see each aspect in more detail. So first off the data ingestion piece, right? So factory connect ingest data. The first step is to ingest data from the edge. We are able to ingest data from a variety of manufacturing data sources like PLCs, historians, etc. Factory connect problems as we mentioned in the Arc-enabled Kubernetes cluster, which offers an extensible, composable view to represent the line. So that's the first step. The second step is we use IoT operations here that reduces the complexity to build the end-to-end solution. IoT operations enables us to connect the cloud and edge using bi-directional messaging. On the second piece here, we have the 3D scans of the factory. This is what Jeremy is talking about. These are created. You saw all the details. I'm not going to go into it again. These scans are segmented into Assemblies, Machines, Components. All those are in the USD format, which is then loaded into Azure Blob Storage for consumption by us and by NVIDIA on viewers. With this data and leveraging Azure AI services, Site Machine is able to provide effective insights. Next up is the scalable cloud platform. On this cloud, once we have the data transferred from the edge to the cloud, Site Machine is manufacturing data platform. This powers all the data to factory operate. All this runs seamlessly in Azure cloud and Microsoft Fabric. IoT operations sends data to the cloud via Fabric Azure Event Hubs, where I even streams in Fabric. This is where Site Machine is able to process the data and create those standardized data models that I talked about that represent the entire line end-to-end. With this data and then leveraging AI services from Azure, Site Machine is able to provide effective insights like agentic recommendations, which we will look at shortly. The third piece here is the omniverse kit extensions. We are leveraging NVIDIA omniverse kit app streaming, obviously. Now that we have data from Site Machine's data platform, we built a couple of kit extensions to integrate with the kit app streaming application. The first one as Shashi was alluding to takes real-time data from the edge as well as the model data from our factory build application to annotate the USD so that we can get contextualized data for these twins that Jeremy is so beautifully generated. Example things like filler speed. That gives you the context of each machine, each asset on the line layered on with meaningful data from the Site Machine application. The second piece is the operate extension. That's the one that handles rendering of this contextual data. Example, creating a billboard on top of the machine to show you the name of the filler, the attributes, how it's running, its status, and so on. It also handles things like zooming into a particular asset, events on the UI, showing where a fault is. It responds to data changes, like if the filler speed changes, you'll immediately see the change in omniverse, in our UI, and so on. We see that in a demo or a short piece. It automatically syncs up. Everything is instantly available on the UI and in omniverse. Events in the UI are in React and it's communicated to the kit application. Now let's look at how the UI piece works. The last piece here is the seamless UI integration. On the front end, Factory Operate is a React application. We embed the omniverse viewport into Operate using NVIDIA provided packages. Every user then connects to a stream from the kit app to show this viewer in the UI. In order to improve efficiency, we've implemented things like stream management to pre-created cash streams, creating the stream pools, showing instant availability in the UI. Events in the UI are passed to Kit App via WebRTC and Y-Saver. Anything that happens in the Kit application, you can have events reflecting in the UI and things the user interacting or data streaming from the UI can interact real time with the omniverse application. The whole stream is contextualized to the View and React labels and other annotations in the stream provide a seamless view of the line with recommendations from agent.ai layered on top of it. Very nice. Okay, you got it. Okay, cool. That was fantastic. We got a lot of great comments as you were showing your presentation there. Good questions too. Is there anything you guys want to adjust before we start tackling some of these questions? I want to show up and then we can jump into questions. Give me one sec. I see omniverse ambassador John Mitchell from BMW is watching today. We're showing great interest in connecting with members of our panel here, which is great. BMW of course is doing amazing things in the digital twin world with their factories and robotics. Okay, so do you guys see it right here, right? Yes, that is correct. Okay, awesome. So just show a quick demo, putting it all together. Charo showed this briefly in the video. This is a live version of our factory operate application running with omniverse get up streaming embedded in our React application. As you can see, this is an entire view of the line. It's a modeling line. On the left hand side, you can see a bunch of, you know, metrics for the line. What's the machine efficiency, what's the filler speed, different fillers, production volume, what flavor is going through the line and so on. And at the bottom of the screen, you can see all the assets and their individual attributes like the filler one, it seems like has a fault, filler two is running well, and so on and so forth. And you can see the same data being leveraged and shown in the omniverse get up streaming as well as billboards, as I mentioned, using the web RTC cons that we talked about. Now, if I want to look in detail, I can see, okay, this filler one has a fault. So let me draw down into that. Now, and you can see we zoom in to the asset. And not only do we zoom in, we are able to highlight specific areas of the machine where the fault actually occurred. So it looks like a conveyor jam. It reflects the red section is this is the place where the operator needs to focus on to fix the issue, right. So this is super powerful. This is where we talked about how kinetic vision is helping us build the USD segmented into components so that we can leverage our data and pinpoint exact locations. So not only do we give calls this we also give exact locations and recommendations for operators to immediately fix this on the shop floor. Let me zoom back out a bit. The other thing you can see here with this purple screen is it says line is running well but not at peak efficiency. I'm going to take ways to optimize. And this is where a genetic AI recommendation engine comes into play, right. So not only are you seeing like current statuses and basic ways to fix it. You're also seeing things like hey, the filler speed should be increased from 630 to 700. That's what our agent to get I recommend just the optimal speed for the filler or something to do with the tackle to update their settings to accommodate the next set of packaging or a wrapper. What is there to replace and similarly, you can now say hey, go to the rapper. Let me see what's going on. Hey, prepare to wrap replace the rap material. AI has detected that you might run out of the rap soon enough so that's the time for your operator to go on set up the rap get it put it in place so that you don't have an issue you don't have stoppages. You don't have down times. So very, very powerful leveraging of our data and leveraging of all the 3D assets that Jeremy showed the army was kept up streaming platform bringing it all together to show immense value for our customers. I will stop right there. Wow, amazing. I don't think anybody wants you to stop where everyone was blown away. You're going live and everyone can obviously you notice the time and date up up on the right. It was not faked. Real time. That's pretty amazing. That's a great question. The comments in the chat. Do you any any more or less thoughts before we start tackling some of these questions. I know we've been discussing something in the background which to tackle. Okay, alright, so let's go. We have we've got nine minutes, so which means we got we got a hustle. So let's try to let's try to keep our answers as concise as possible and we can refer people to our discord. Amelia is going to set up a thread on our discord server specifically for this live stream. So whether you're watching this live or the replay, you can go to that thread and you could continue to ask questions, conversations and hopefully with other viewers will also go there too. So you can chat with them on it. Alright, so let's do the little lightning round of questions here. I think earlier on actually we had a good question. I think for a first site machine so sure if you want to take this one about how do you approach a client engagement and when client stakeholders have different priorities. Who do you prioritize and why. Yeah, so the answer to that is we have to prioritize everybody. He actually worked with both it and we provide a complete solution without the agreement and agreement across these functions and now now we're also seeing a separate a function which really collaborates with it and and we provide daily solutions to one in the enterprise. So we go all the way we address security, plant connectivity, as well as operational data and insights. So the real answer is you cannot afford to prioritize one over the other because then you're not going to have a successful plant level transformation that scales. Okay, very, very super helpful and just a quick note we had to wave goodbye to Sasha a minute ago he had none of the meaning you had to go do so thank you Sasha for always helping out great to have you here. Okay, we also I'm looking so we have a couple of different chat channels where we're keeping track of some of these questions I'm looking at our internal chat here where I see a few things that were discussed. Sure you just mentioned you saw a good question from Victor. Amazing collab curious with this foundation which type of simulation becomes more feasible high fidelity asset level modeling or broader plant level. What if situational modeling did you want to tackle this one. Sure, I'd also like to invite maybe so they're in others if they have any specific ideas just to talk about this but we are thinking a lot about what a situational modeling and scenario modeling. Which means that if I have a change in raw material or if I add another machine to my line. How does the capacity change what will my line look like and what should I be planning for. Ideally we help them make real decisions by seeing things like if you add another machine you can reduce all of your weekend shifts or eliminate them all together. And again the focus is on providing broad scenario level guidance with with our simulations but if anyone has additional thoughts and everything I'd love to invite them as well. Yeah, yeah. I have one point to add as well. Yeah, so it's sort of I would say both right like at the first step. We're trying to get as accurate a representation of the models as possible right and then try to solve real problems like these agentic recommendations or what's happening on the shop floor and so on so forth and for that perhaps you don't need like the super super high fidelity still high fidelity as you could see. But as as you were talking about but there are definitely use cases when you're looking at animations and like real time playbacks of you know how things happen and how it caused a fault or what have you where a more realistic representation is absolutely essential. So both are different use cases and you know both are something we are looking at that to finish. Yeah, so but what what you guys said is spot on but specifically on the asset level or simulation specific question that Victor is asking that we were talking about the bottling line use case the single most expensive asset that's there in the line is a filler and then of course the next one is a packer right. So there are filler level simulations that can also be done to understand spillage inefficiencies things that are supposed to be followed by certain string guidelines of course you cannot stop a filler to run those scenarios. So that's where simulation comes really handy. So to think about the whole digital twin journey that we were discussing in the last few minutes is to break down into two separate journeys one is the whole operational digital twin and the other one is a simulation digital twin and they both kind of go hand in hand. So the filler simulation is one of the examples where you could be thinking about fluid level simulations and the CFD style simulations as well where we were actually in track with a third party simulation provider that can run that kind of filler level simulations as well. It's a it's a team play it's not like in a one size fits all and one partner provides all the different experience in the services you bring the right ingredients for the right kind of job to get the results. The business results that you expect. Great. Okay that's super helpful. And so this this gentleman or a gentle lady has asked us a couple of times in chat so I know they're eager to for this answer. This is an interesting question obviously because we're talking about very developer kind of focused pipeline but we have a lot of creators out there in the world who really want to ramp up and and contribute to these challenges. At C graph recently there was a session if Amelia has has a second maybe she can try to find that session I think the moment live the other day for everyone to watch now we had a session about as it's really as if you're a 3D content creator how do you upscale for the physical AI. Does anyone have any any suggestions for Yash here. I think I think this is a great question for kinetic vision and Jeremy do you want to take that. Yeah so I actually saw this question I thought wow we're actually going to be answering some of the one of these questions so you know I the things that I laid out in my presentation. It is a lot about tools curiosity and skill sets right so. Really just you know for this type of simulated world we need these types of tools we're using Navis scanning we're using reality capture and then we're using what you know if you're if you're a creator you're already using this great set of 3D content content creation tools. You know in Maya 3D Studio Max substance you know those are those are all the set of tools that you're using so. I think really using the those tools and the guidance on how to get to that level simulated world I think is you know from from the from those software vendors is you know going to be really important but the other thing is the collaboration what I'm. You know a lot of these questions what I'm hearing is they all relate to collaboration and communication. These are big problems we're creating an entire simulated world so that means that we need all of the skill sets and expertise from each of the individuals that places something in that in that virtual world. I'm sorry Ganesh I'm not sure when you learned about fillers but you know to make a good decision about how to make a filler run operate properly and it's optimal speed is probably not your your expertise you know enough to be dangerous. But working with that person who either sold the filler to this customer or who operates the filler every day is really important. And so you know you have these hard skills but the soft skills are just as important making sure that you are building that network of experts and you're collaborating them. Collaborating with them as you build that simulated world I know this is a developer webinar and we want to hear about you know programming languages and programs but I cannot underestimate. The this is where the I saw some you know I'll pivot here to I see things you know comments in here about well you know once we you know once we get rid of the humans where how do we you know manage the narrative. Don't get rid of the humans that the humans like we're stuck doing so much BS now elevate the humans and continue to build those relationships use the relationships to build that simulated world OK I'm going crazy here. I'm going to save that quote when I have an extra view with my boss so thank you. Cool. That's great. You're out of here at March. All right. So speaking about programming languages about this question came in from LinkedIn about CUDA programming and the a models. Don't want to tackle this one. I don't know if we have a CUDA expert here but no I don't think I don't think it feels like a. Yeah. Yeah. So we'll. Sorry. CUDA is foundational like every single thing that that you see that's built on top of machine learning and artificial intelligence that allows the GPU to accelerate it is built on CUDA every single every single operation is built on CUDA. Nobody talks about CUDA anymore because it's so foundational you have to have it to make these operations move quickly on a video graphics card. So you know it's really any any anything that you're using to accelerate the training of a neural network the inferencing of a neural network a scene graph. Every single one of those operations is built on the CUDA libraries. Great great information and may ask the panel here because I know we're at time. Did you want to hard stop we take a couple of questions. Okay great. Okay here's one coming in from Jesus who is asking if we can describe their app streaming over Azure has experienced latency etc. I believe the question means the Ocast the on US kid upstreaming that's available on Azure marketplace. It is available. It is a co-sale ready fully transactable marketplace listing. So some of the work that we saw in this in this session is actually using that same Ocast. So as a developer you can actually go to Azure marketplace today and get started follow the GitHub repo the same things at what site machine did. So the work that site machine did was following that ignite repo that drew was talking about. So that's the close partnership that we have between NVIDIA and Microsoft to showcase how to get started. How you can actually take those containers the kid upstreaming containers deployed on your own. Kubernetes clusters on Azure leverage the a 10s that are running on Azure commercials and get started. So it all basically starts from that kid upstreaming and then build the entire solutions like what's it was talking about with that front end web applications and integrating it into that web app. So it's it's all there. It's all available on that marketplace and get a repo for that we release at ignite. I think we should also admire you said that discord will have it so we should also share that GitHub repo as well for any developer to get started today. Okay, we'll do that. I think Amelia posted that thread in the chat a little earlier. We'll also add to the description afterwards. We got another question here from LinkedIn. Alan, this is probably great for everybody, but definitely Ganesh. This is, you know, obviously when when when we have business folks from different companies watching these kinds of topics and they realize, you know what, I should take a look at omniverse or take a look at open USD. What would you say, Ganesh? What what's what what is your pitch to have these have these leaders actually take a serious look at adoption. So I think I think it's all I believe channel kind of laid this out at the beginning of the session. It all starts with the the KPI, the business KPI and the real like immediate material impact. So the modeling line use case that Charlie was talking about, there was an operational efficiency gain or throughput improvement or yield improvement. That's where that's where this entire kind of journey started off. So try to understand how what that KPI would look like and it can be it can be related to going back to what Jeremy was talking about. If you take the filler simulation example, there are subject matter experts who have been doing that for decades. So but if they are looking at a specific business KPI that they want to hit through a simulation scenario, then then start with that. See where all of these new new technologies and the digital transformation, the digital twin, the simulation workflows are going to actually cause an impact. In case of psych machine, they zeroed in on an OE improvement, took their platform to Microsoft and then media technology took the help from kinetic vision and showcase that example with a double digit operational efficiency improvement. If I remember right, Charlie, I think it was like a 10 to 15 percent. You guys were targeting like one to two percent and that itself was like a big impact, both top line and bottom line, but they were able to showcase a double digit impact of 10 to 15 percent on top line and bottom line. Or I wouldn't say top line of bottom line, but the operational efficiency gain that has impact both top line and bottom line. So that's that's where it starts. It always starts with the business value and the KPI and then start with that one use case. Look at your existing stack of technologies that you have. See what the gap exists, adopt the technologies to the right level rather than like throwing everything out of the window and start from scratch. That never works. It's way too expensive and then see the incremental gain and then keep expanding from there from one line to multiple lines from one factory to multiple factories and see whether that really sticks. So that's that's how I think I would think about. I'm open to other inputs and ideas from folks here. Oh, I've always got a hot take. So, you know, for organizations when you're thinking about your omniverse investment, you know, omniverse is an, you know, an entire ecosystem. So you can engage it in different ways. This engagement through an ISV like site machine is actually a very low friction engagement because your organization probably already has Azure resources. And now that Microsoft has made this capability available through Azure. Now this is just a SaaS purchase. You're just calling up a company like site machine and you're saying, Hey, I would like to use your software to see my game. And then you're, and then it's utilizing either the SaaS platform or your cloud Azure private tenant. And that's a very low, that's a very low friction way to invest in omniverse. You may be a different type of organization and say, Hey, we have a very unique manufacturing process. A site machine doesn't meet all of our requirements. We want to build our own applications to be a technology leader in our space and lead everybody else. That's where then you would make the type of investment with omniverse via omniverse cloud or OBE to bring your developers and actually have them building their own applications. If you're an organization who can build your own applications and you want to be a leader in your technology, then that's how you would invest in omniverse. And that's like a strategic decision. Are we a developer of applications here within our company or are we a procurer of ISV applications to solve our problems? And that's a key decision point between leaders. Are you developing or are you procuring software? That's great, great, great context. Amazing. Okay, we got another question here. And actually, Jeremy, enter it quickly if you can, but I think you covered this earlier. How helpful is photorealistic effects when used by GUI engineers or technicians in practice? Yeah, I mean, I think about this as communication, right? Who are you communicating with? The more photorealistic your output, then the broader your collaboration is with all in your organization. Yes, if you're engineer to engineer, you may not need photorealistic effects to be able to solve a certain problem and change a variable. But if you are making a big investment and you need to communicate to leaders on opening up the budget to go ahead and make some huge cost savings for the company, you probably do need some kind of photorealistic output. And people, NVIDIA is going to give it to us for free, right? I'm not for free. You got to buy GPUs, okay? But the highly photorealistic effects should all be free in the end. Yes, there's some friction now, but that friction is going to decrease over time. Everything, you know, compute becomes cheaper over time. And so I do think it's very important, especially in creating these connections through your organization to get real work done. And the photorealism to Jeremy's point, today it feels like it's a human-to-machine interaction, but the future is actually towards massive amount of automations through physical AI. It's a lot of machine-to-machine training. So for a robot to really train and learn, it needs that physical accuracy. And that's why photorealism is super important. And when we're building assets, Jeremy and I have had this conversation many times. When we're building these 3D-USD assets, today it's about operational twin. But really, that's just the stepping stone. The real goal is to drive through that entire journey of operational simulation and ultimately for physical AI and robotics, right? And when you're thinking about that journey, you really need that photorealism and really high-fidelity, accurate USD assets. Yeah, we should do another hour on synthetic training data for vision systems. Exactly. Yeah, that is really important. That's the technology side. I'm always trying to cover the technology side and the human side. So sorry, I shouldn't have left that one off. That's like a huge, huge thing. Well, this is an unrelated note. Curious how one decides optimal resolutions of point clouds that can be fused with realistic data. Do you have any thoughts on this one? Yeah, these are all... It's like what's available and fast, right, and can solve the problem. It's a multi-variable solution and it's always changing over time. So we've chosen a certain type of resolution because we're talking about, I'll say, human-scale, facility-scale decisions, right? We also operate three North Star Industrial CT scanners here. So that's a completely different technology. It scans down to the micron level. We can detect the porosity and the femur of a mouse or a void in a solder pad on a BGA. So you really need to use the right technology for the job, but it's going to be some mixture of those things, right? The expense of the technology, the value of the problem you're solving. But if you have questions, hey, you know who you can call. Yeah, that's great. Well, thank you. It's fascinating. I never thought of a mouse femur before. So Rich is asking on LinkedIn about, is this workflow available with academia? What do you mean, of course? Why not? We do have an HDR team here actually at NVIDIA that I can help put you in touch with if you have specific questions about your school working. But Ganesh, I don't know if you have any other thoughts on this. No, this is great. Thank you for hosting it. Thank you for inviting everybody. And thank you Psych Machine for an awesome work. I know we had a really short ramp of getting all of this integrated up and running in six months. Thank you Kinetic Vision for an incredible partnership and support. You guys have been awesome with NVIDIA for over the years and of course for Microsoft. So thank you Drew and the entire team. What we started off like a year ago, it's really like blossoming and this is just the beginning. We're going to grow even more. And Ed Mar, I hope you will invite us again with more exciting things. Of course. I always like to ask this as a last round. Ganesh, you just went. So you passed this. But does anyone have any last kind of words of wisdom that you want people to really remember? What's your key takeaway you want people to come away with? We'll start with, we'll go backwards actually. So Drew, what's your key takeaway here if we want people to remember? Key takeaway. NVIDIA and Microsoft have a beautiful relationship. And we're also trying to design industrial standards together as well. So just stay in contact with us and hopefully it gives you confidence to build whatever you all dream up. So that's what my takeaway is. Amazing. Great words Drew. I'm glad you went. Sheru, you're next. Oh, okay. Yeah. So I think the key takeaway I'd love for everyone to remember is, you know, the domain specific intelligence and architecture that Sight Machine provides is critical for manufacturers to actually deliver real value. And that can be a great framework for other verticals as well, right? Understanding the entire technology stack that's necessary for AI and data transformation, I think, is becoming much more fascinating. But you know, understanding each layer and what we provide in addition to Microsoft and NVIDIA platforms would be a great thing to keep in mind. Great words, Sheru. Thank you. Sudhir, what's on your mind? Yeah, I'll channel Jeremy a little bit and say this, you know, sort of the two modes come to mind, right? Like this partnership and what we've achieved showcases what can be done with the power of omniverse and Microsoft Azure and Fabric, right? And if somebody who's like really wants to put on a developer hat and play with it and build these technologies, like this is the, you know, sort of blueprint on how it can be done and how it has actually been done. That said, as Sheru mentioned, a lot is based on data and the domain expertise and so on, and that's where Sight Machine comes in. And so if you have looking for a solution that offers end-to-end with the data, with all the integrations in place and has done the heavy lifting for you, please feel free to contact us and we have the solution for you. Thank you. Amazing. Okay, Jeremy, you have the last word here. I'm afraid. Yeah, well, so in manufacturing and supply chain, most people's operations run around 50% of the nameplate of what it was designed for, okay? At every big company, these are billion-dollar problems and they're only going to be solved digitally. So if you're looking at like, how much does this cost? Should I do it? Yes, the answer is yes. It's 10 times faster and cheaper doing it digitally than going in and doing things manually. So the biggest issue is not the cost. The biggest issue is not the technology. The biggest issue is a people problem, okay? This is about multidisciplinary experts understanding all aspects of the problem. It's about having a vision. I love that question. What do I do about IT and manufacturing and them being misaligned? Get them aligned, people. They have to be aligned to solve these problems. So much of what we're talking about here today, this is amazing. NVIDIA has done an amazing job of making sure that technology is available. Fantastic. Developers and ISVs like Sight Machine have made sure that they use that technology and put it into products. And then, you know, Kinetic Vision is helping customers solve problems every day. But what I'm seeing on the front lines is people issues and companies really coming to grips with how to solve these problems in their organization. So try to build those relationships. People get them in front of the right stakeholders. Make sure that you're supporting, you know, your visionary leaders, making sure your visionary leaders are working with their visionary developers. It's a big people challenge, but I know that we'll be able to get there. So thanks again. That's awesome. I'm so glad. That was a great closing remarks. I want to thank all of our amazing guests from Sight Machine, Microsoft, Kinetic Vision, and of course, my colleagues here at NVIDIA for sharing your amazing insight. Each one of you has been extremely helpful. We've seen how Agentec AI, OpenUSD, and Jill Twins can help factory teams spot issues faster and optimize production lines. And it seems like this is just the beginning of what's possible. So a big thanks to all of you who joined us live out there. Great comments and questions. We really love the projects that we shared. If you missed part of today's session, don't worry. The replay is available using the same link you're at right now. Or just go to our YouTube channel anytime, NVIDIA Omniverse. Go to live and you'll find it there. And as I'm showing on the screen now, this kind of experts presentation is valuable to you. I highly encourage you if you can make it to GTC DC. So our first one, Washington DC, you're going to see Jensen there providing a great keynote. So I hope everyone there can make it. I will be there. So let me know if you're going and we can grab a coffee or something together. Until next time, thank you so much for joining us, everybody. It's been an absolute honor to join the panel with you all today and with you all there in the audience. Have a great rest of the day. Thank you so much. Thanks. Thank you.", "segments": [{"id": 0, "seek": 0, "start": 0.0, "end": 28.0, "text": " Hello, everybody. Welcome to the OpenUSD Insiders livestream. I'm very honored to have you join us today. We have a very special episode with some amazing partners.", "tokens": [50364, 2425, 11, 2201, 13, 4027, 281, 264, 7238, 3447, 35, 9442, 6936, 29782, 13, 286, 478, 588, 14556, 281, 362, 291, 3917, 505, 965, 13, 492, 362, 257, 588, 2121, 3500, 365, 512, 2243, 4462, 13, 51764], "temperature": 0.0, "avg_logprob": -0.2472599744796753, "compression_ratio": 1.2615384615384615, "no_speech_prob": 0.041321031749248505}, {"id": 1, "seek": 2800, "start": 28.0, "end": 34.0, "text": " We're going to dive into that topic in just a second, but we're going to be covering building an authentic AI powered digital twins.", "tokens": [50364, 492, 434, 516, 281, 9192, 666, 300, 4829, 294, 445, 257, 1150, 11, 457, 321, 434, 516, 281, 312, 10322, 2390, 364, 12466, 7318, 17786, 4562, 22555, 13, 50664], "temperature": 0.0, "avg_logprob": -0.1328319251960051, "compression_ratio": 1.7243401759530792, "no_speech_prob": 0.6880810260772705}, {"id": 2, "seek": 2800, "start": 34.0, "end": 42.0, "text": " We would love to invite your questions, your comments. Let us know where you're watching from. If you've got any interesting projects related to this, we'd love to hear about it also.", "tokens": [50664, 492, 576, 959, 281, 7980, 428, 1651, 11, 428, 3053, 13, 961, 505, 458, 689, 291, 434, 1976, 490, 13, 759, 291, 600, 658, 604, 1880, 4455, 4077, 281, 341, 11, 321, 1116, 959, 281, 1568, 466, 309, 611, 13, 51064], "temperature": 0.0, "avg_logprob": -0.1328319251960051, "compression_ratio": 1.7243401759530792, "no_speech_prob": 0.6880810260772705}, {"id": 3, "seek": 2800, "start": 42.0, "end": 48.0, "text": " So go ahead and introduce yourself in the chat right now. We're going to hit the questions. There are probably a few different points during this hour.", "tokens": [51064, 407, 352, 2286, 293, 5366, 1803, 294, 264, 5081, 558, 586, 13, 492, 434, 516, 281, 2045, 264, 1651, 13, 821, 366, 1391, 257, 1326, 819, 2793, 1830, 341, 1773, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1328319251960051, "compression_ratio": 1.7243401759530792, "no_speech_prob": 0.6880810260772705}, {"id": 4, "seek": 2800, "start": 48.0, "end": 53.0, "text": " So if we don't get you right when you post, we're trying to keep track of them. We'll hit them by the end, most likely.", "tokens": [51364, 407, 498, 321, 500, 380, 483, 291, 558, 562, 291, 2183, 11, 321, 434, 1382, 281, 1066, 2837, 295, 552, 13, 492, 603, 2045, 552, 538, 264, 917, 11, 881, 3700, 13, 51614], "temperature": 0.0, "avg_logprob": -0.1328319251960051, "compression_ratio": 1.7243401759530792, "no_speech_prob": 0.6880810260772705}, {"id": 5, "seek": 5300, "start": 53.0, "end": 62.0, "text": " OK, so thanks for joining us. So right now, I'd like to also very attention to this great learning path we've got here. Brand new new digital twins for physical AI.", "tokens": [50364, 2264, 11, 370, 3231, 337, 5549, 505, 13, 407, 558, 586, 11, 286, 1116, 411, 281, 611, 588, 3202, 281, 341, 869, 2539, 3100, 321, 600, 658, 510, 13, 11119, 777, 777, 4562, 22555, 337, 4001, 7318, 13, 50814], "temperature": 0.0, "avg_logprob": -0.17926932544243046, "compression_ratio": 1.4778761061946903, "no_speech_prob": 0.3552197813987732}, {"id": 6, "seek": 5300, "start": 62.0, "end": 71.0, "text": " We just came back from SeaGraph. A lot of excitement on this on the subject, including at labs and sessions. This QR code will bring you to that brand new learning path.", "tokens": [50814, 492, 445, 1361, 646, 490, 11352, 38, 2662, 13, 316, 688, 295, 14755, 322, 341, 322, 264, 3983, 11, 3009, 412, 20339, 293, 11081, 13, 639, 32784, 3089, 486, 1565, 291, 281, 300, 3360, 777, 2539, 3100, 13, 51264], "temperature": 0.0, "avg_logprob": -0.17926932544243046, "compression_ratio": 1.4778761061946903, "no_speech_prob": 0.3552197813987732}, {"id": 7, "seek": 7100, "start": 71.0, "end": 85.0, "text": " For those of you who've been watching for a while, a part of the community, you all know that these learning paths are a fantastic free resource, self-paced courses, basically, on a various number of topics, including OpenUSD and robotics.", "tokens": [50364, 1171, 729, 295, 291, 567, 600, 668, 1976, 337, 257, 1339, 11, 257, 644, 295, 264, 1768, 11, 291, 439, 458, 300, 613, 2539, 14518, 366, 257, 5456, 1737, 7684, 11, 2698, 12, 47038, 7712, 11, 1936, 11, 322, 257, 3683, 1230, 295, 8378, 11, 3009, 7238, 3447, 35, 293, 34145, 13, 51064], "temperature": 0.0, "avg_logprob": -0.13763470019934312, "compression_ratio": 1.556338028169014, "no_speech_prob": 0.5060539245605469}, {"id": 8, "seek": 7100, "start": 85.0, "end": 96.0, "text": " And now we have digital twins for physical AI. So use that QR code to find it. If you need the link, we'll also put it in the chat, but that is a super helpful resource for all the developers out there.", "tokens": [51064, 400, 586, 321, 362, 4562, 22555, 337, 4001, 7318, 13, 407, 764, 300, 32784, 3089, 281, 915, 309, 13, 759, 291, 643, 264, 2113, 11, 321, 603, 611, 829, 309, 294, 264, 5081, 11, 457, 300, 307, 257, 1687, 4961, 7684, 337, 439, 264, 8849, 484, 456, 13, 51614], "temperature": 0.0, "avg_logprob": -0.13763470019934312, "compression_ratio": 1.556338028169014, "no_speech_prob": 0.5060539245605469}, {"id": 9, "seek": 9600, "start": 96.0, "end": 105.0, "text": " Here's another great resource, developing and deploying your own omniverse kit apps. This QR code will also bring you the latest information there.", "tokens": [50364, 1692, 311, 1071, 869, 7684, 11, 6416, 293, 34198, 428, 1065, 36874, 5376, 8260, 7733, 13, 639, 32784, 3089, 486, 611, 1565, 291, 264, 6792, 1589, 456, 13, 50814], "temperature": 0.0, "avg_logprob": -0.10388001628305721, "compression_ratio": 1.6829268292682926, "no_speech_prob": 0.08848055452108383}, {"id": 10, "seek": 9600, "start": 105.0, "end": 117.0, "text": " Another great resource for leveraging the community on deploying your own kit app is our Discord server, where we have developers talking about this very topic pretty much on a daily basis.", "tokens": [50814, 3996, 869, 7684, 337, 32666, 264, 1768, 322, 34198, 428, 1065, 8260, 724, 307, 527, 32623, 7154, 11, 689, 321, 362, 8849, 1417, 466, 341, 588, 4829, 1238, 709, 322, 257, 5212, 5143, 13, 51414], "temperature": 0.0, "avg_logprob": -0.10388001628305721, "compression_ratio": 1.6829268292682926, "no_speech_prob": 0.08848055452108383}, {"id": 11, "seek": 9600, "start": 117.0, "end": 121.0, "text": " So engage with other community members there. We'll post a link in the chat.", "tokens": [51414, 407, 4683, 365, 661, 1768, 2679, 456, 13, 492, 603, 2183, 257, 2113, 294, 264, 5081, 13, 51614], "temperature": 0.0, "avg_logprob": -0.10388001628305721, "compression_ratio": 1.6829268292682926, "no_speech_prob": 0.08848055452108383}, {"id": 12, "seek": 12100, "start": 122.0, "end": 131.0, "text": " Amazon Devices and Services achieves major step towards zero-touch manufacturing with NVIDIA. This is also an amazing story that just came out. This QR code will bring you right to there.", "tokens": [50414, 6795, 9096, 1473, 293, 12124, 3538, 977, 2563, 1823, 3030, 4018, 12, 83, 2220, 11096, 365, 426, 3958, 6914, 13, 639, 307, 611, 364, 2243, 1657, 300, 445, 1361, 484, 13, 639, 32784, 3089, 486, 1565, 291, 558, 281, 456, 13, 50864], "temperature": 0.0, "avg_logprob": -0.10288130385535103, "compression_ratio": 1.5932203389830508, "no_speech_prob": 0.1223498061299324}, {"id": 13, "seek": 12100, "start": 131.0, "end": 141.0, "text": " If you have any questions on this, feel free to pop on over to the live streams channel. We have our Discord server. Feel free to tag me, and I'll get you someone who can help in the chat on this.", "tokens": [50864, 759, 291, 362, 604, 1651, 322, 341, 11, 841, 1737, 281, 1665, 322, 670, 281, 264, 1621, 15842, 2269, 13, 492, 362, 527, 32623, 7154, 13, 14113, 1737, 281, 6162, 385, 11, 293, 286, 603, 483, 291, 1580, 567, 393, 854, 294, 264, 5081, 322, 341, 13, 51364], "temperature": 0.0, "avg_logprob": -0.10288130385535103, "compression_ratio": 1.5932203389830508, "no_speech_prob": 0.1223498061299324}, {"id": 14, "seek": 12100, "start": 141.0, "end": 147.0, "text": " Developers build fast and reliable robot simulations with NVIDIA omniverse libraries.", "tokens": [51364, 11442, 433, 1322, 2370, 293, 12924, 7881, 35138, 365, 426, 3958, 6914, 36874, 5376, 15148, 13, 51664], "temperature": 0.0, "avg_logprob": -0.10288130385535103, "compression_ratio": 1.5932203389830508, "no_speech_prob": 0.1223498061299324}, {"id": 15, "seek": 14700, "start": 147.0, "end": 155.0, "text": " As you can see, we're all about QR codes today. So here's another QR code for you. That'll bring you right to the link.", "tokens": [50364, 1018, 291, 393, 536, 11, 321, 434, 439, 466, 32784, 14211, 965, 13, 407, 510, 311, 1071, 32784, 3089, 337, 291, 13, 663, 603, 1565, 291, 558, 281, 264, 2113, 13, 50764], "temperature": 0.0, "avg_logprob": -0.09165842556259007, "compression_ratio": 1.6208333333333333, "no_speech_prob": 0.06217572093009949}, {"id": 16, "seek": 14700, "start": 155.0, "end": 162.0, "text": " And obviously, if you're watching this live, you will get these links in the live chat also. No need to bring your phone up to the screen unless you want to.", "tokens": [50764, 400, 2745, 11, 498, 291, 434, 1976, 341, 1621, 11, 291, 486, 483, 613, 6123, 294, 264, 1621, 5081, 611, 13, 883, 643, 281, 1565, 428, 2593, 493, 281, 264, 2568, 5969, 291, 528, 281, 13, 51114], "temperature": 0.0, "avg_logprob": -0.09165842556259007, "compression_ratio": 1.6208333333333333, "no_speech_prob": 0.06217572093009949}, {"id": 17, "seek": 14700, "start": 162.0, "end": 168.0, "text": " You're free to, of course. But feel free to check out that resource. We'll be happy to also help you with that.", "tokens": [51114, 509, 434, 1737, 281, 11, 295, 1164, 13, 583, 841, 1737, 281, 1520, 484, 300, 7684, 13, 492, 603, 312, 2055, 281, 611, 854, 291, 365, 300, 13, 51414], "temperature": 0.0, "avg_logprob": -0.09165842556259007, "compression_ratio": 1.6208333333333333, "no_speech_prob": 0.06217572093009949}, {"id": 18, "seek": 16800, "start": 168.0, "end": 173.0, "text": " And how to instantly render real-world scenes in interactive simulation. Another great article.", "tokens": [50364, 400, 577, 281, 13518, 15529, 957, 12, 13217, 8026, 294, 15141, 16575, 13, 3996, 869, 7222, 13, 50614], "temperature": 0.0, "avg_logprob": -0.11729255799324281, "compression_ratio": 1.7364341085271318, "no_speech_prob": 0.7289167642593384}, {"id": 19, "seek": 16800, "start": 173.0, "end": 185.0, "text": " These articles are actually a great resource for people because they leverage not only our NVIDIA developers, but also members of partner teams, members of the community.", "tokens": [50614, 1981, 11290, 366, 767, 257, 869, 7684, 337, 561, 570, 436, 13982, 406, 787, 527, 426, 3958, 6914, 8849, 11, 457, 611, 2679, 295, 4975, 5491, 11, 2679, 295, 264, 1768, 13, 51214], "temperature": 0.0, "avg_logprob": -0.11729255799324281, "compression_ratio": 1.7364341085271318, "no_speech_prob": 0.7289167642593384}, {"id": 20, "seek": 16800, "start": 185.0, "end": 196.0, "text": " Really great use cases and workflows from start to finish. So highly encourage you to check out each of these, but this is how to render real-world scenes in interactive simulation.", "tokens": [51214, 4083, 869, 764, 3331, 293, 43461, 490, 722, 281, 2413, 13, 407, 5405, 5373, 291, 281, 1520, 484, 1184, 295, 613, 11, 457, 341, 307, 577, 281, 15529, 957, 12, 13217, 8026, 294, 15141, 16575, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11729255799324281, "compression_ratio": 1.7364341085271318, "no_speech_prob": 0.7289167642593384}, {"id": 21, "seek": 19600, "start": 197.0, "end": 204.0, "text": " NVIDIA opens up portals to world of robotics with new omniverse libraries, Cosmos physical AI models and AI computing infrastructure.", "tokens": [50414, 426, 3958, 6914, 9870, 493, 2436, 1124, 281, 1002, 295, 34145, 365, 777, 36874, 5376, 15148, 11, 15855, 3415, 4001, 7318, 5245, 293, 7318, 15866, 6896, 13, 50764], "temperature": 0.0, "avg_logprob": -0.13982689380645752, "compression_ratio": 1.5461538461538462, "no_speech_prob": 0.03810469061136246}, {"id": 22, "seek": 19600, "start": 204.0, "end": 212.0, "text": " Cosmos is an amazing world foundation model we've been talking about a lot lately. This will help give you more insight into how to leverage that to the fullest.", "tokens": [50764, 15855, 3415, 307, 364, 2243, 1002, 7030, 2316, 321, 600, 668, 1417, 466, 257, 688, 12881, 13, 639, 486, 854, 976, 291, 544, 11269, 666, 577, 281, 13982, 300, 281, 264, 45154, 13, 51164], "temperature": 0.0, "avg_logprob": -0.13982689380645752, "compression_ratio": 1.5461538461538462, "no_speech_prob": 0.03810469061136246}, {"id": 23, "seek": 19600, "start": 212.0, "end": 219.0, "text": " Again, at Seagraph, we just wrapped up a couple weeks ago. It was all about physical AI, robotics, Cosmos.", "tokens": [51164, 3764, 11, 412, 1100, 559, 2662, 11, 321, 445, 14226, 493, 257, 1916, 3259, 2057, 13, 467, 390, 439, 466, 4001, 7318, 11, 34145, 11, 15855, 3415, 13, 51514], "temperature": 0.0, "avg_logprob": -0.13982689380645752, "compression_ratio": 1.5461538461538462, "no_speech_prob": 0.03810469061136246}, {"id": 24, "seek": 21900, "start": 219.0, "end": 223.0, "text": " It really showed a nice transformation in what's happening in the graphics industry.", "tokens": [50364, 467, 534, 4712, 257, 1481, 9887, 294, 437, 311, 2737, 294, 264, 11837, 3518, 13, 50564], "temperature": 0.0, "avg_logprob": -0.0968235433101654, "compression_ratio": 1.5644444444444445, "no_speech_prob": 0.08971207588911057}, {"id": 25, "seek": 21900, "start": 223.0, "end": 228.0, "text": " So feel free to leverage that QR code to get right to that article.", "tokens": [50564, 407, 841, 1737, 281, 13982, 300, 32784, 3089, 281, 483, 558, 281, 300, 7222, 13, 50814], "temperature": 0.0, "avg_logprob": -0.0968235433101654, "compression_ratio": 1.5644444444444445, "no_speech_prob": 0.08971207588911057}, {"id": 26, "seek": 21900, "start": 228.0, "end": 237.0, "text": " And another exciting thing that also was debuted at Seagraph was our open USD certification.", "tokens": [50814, 400, 1071, 4670, 551, 300, 611, 390, 33392, 412, 1100, 559, 2662, 390, 527, 1269, 24375, 21775, 13, 51264], "temperature": 0.0, "avg_logprob": -0.0968235433101654, "compression_ratio": 1.5644444444444445, "no_speech_prob": 0.08971207588911057}, {"id": 27, "seek": 21900, "start": 237.0, "end": 243.0, "text": " For those who attended Seagraph in person, you were able to actually take the certification exam for free.", "tokens": [51264, 1171, 729, 567, 15990, 1100, 559, 2662, 294, 954, 11, 291, 645, 1075, 281, 767, 747, 264, 21775, 1139, 337, 1737, 13, 51564], "temperature": 0.0, "avg_logprob": -0.0968235433101654, "compression_ratio": 1.5644444444444445, "no_speech_prob": 0.08971207588911057}, {"id": 28, "seek": 24300, "start": 243.0, "end": 249.0, "text": " Otherwise, there's a small cost attached if you are taking it online and here's the QR code for that.", "tokens": [50364, 10328, 11, 456, 311, 257, 1359, 2063, 8570, 498, 291, 366, 1940, 309, 2950, 293, 510, 311, 264, 32784, 3089, 337, 300, 13, 50664], "temperature": 0.0, "avg_logprob": -0.11147127605619885, "compression_ratio": 1.713235294117647, "no_speech_prob": 0.05512120574712753}, {"id": 29, "seek": 24300, "start": 249.0, "end": 257.0, "text": " I highly recommend anyone out there who's been floating around to open USD for a while, go ahead and challenge yourself to take the certification.", "tokens": [50664, 286, 5405, 2748, 2878, 484, 456, 567, 311, 668, 12607, 926, 281, 1269, 24375, 337, 257, 1339, 11, 352, 2286, 293, 3430, 1803, 281, 747, 264, 21775, 13, 51064], "temperature": 0.0, "avg_logprob": -0.11147127605619885, "compression_ratio": 1.713235294117647, "no_speech_prob": 0.05512120574712753}, {"id": 30, "seek": 24300, "start": 257.0, "end": 264.0, "text": " I do think it's a great idea to take that learning path open USD before you take the certification exam.", "tokens": [51064, 286, 360, 519, 309, 311, 257, 869, 1558, 281, 747, 300, 2539, 3100, 1269, 24375, 949, 291, 747, 264, 21775, 1139, 13, 51414], "temperature": 0.0, "avg_logprob": -0.11147127605619885, "compression_ratio": 1.713235294117647, "no_speech_prob": 0.05512120574712753}, {"id": 31, "seek": 24300, "start": 264.0, "end": 270.0, "text": " You're going to get all the information you need by following along with those learning path self-paced courses.", "tokens": [51414, 509, 434, 516, 281, 483, 439, 264, 1589, 291, 643, 538, 3480, 2051, 365, 729, 2539, 3100, 2698, 12, 47038, 7712, 13, 51714], "temperature": 0.0, "avg_logprob": -0.11147127605619885, "compression_ratio": 1.713235294117647, "no_speech_prob": 0.05512120574712753}, {"id": 32, "seek": 27000, "start": 270.0, "end": 281.0, "text": " And then you'll have a great certificate at the end so you can show the world that you are open USD certified, making yourself even more knowledgeable and more of a thought leader whenever you make your posts.", "tokens": [50364, 400, 550, 291, 603, 362, 257, 869, 15953, 412, 264, 917, 370, 291, 393, 855, 264, 1002, 300, 291, 366, 1269, 24375, 18580, 11, 1455, 1803, 754, 544, 33800, 293, 544, 295, 257, 1194, 5263, 5699, 291, 652, 428, 12300, 13, 50914], "temperature": 0.0, "avg_logprob": -0.10517854690551758, "compression_ratio": 1.7054794520547945, "no_speech_prob": 0.04578465595841408}, {"id": 33, "seek": 27000, "start": 281.0, "end": 285.0, "text": " These are the links that will give you the various resources we have in the community.", "tokens": [50914, 1981, 366, 264, 6123, 300, 486, 976, 291, 264, 3683, 3593, 321, 362, 294, 264, 1768, 13, 51114], "temperature": 0.0, "avg_logprob": -0.10517854690551758, "compression_ratio": 1.7054794520547945, "no_speech_prob": 0.04578465595841408}, {"id": 34, "seek": 27000, "start": 285.0, "end": 297.0, "text": " We have our calendar, which is the first link there, which will bring you all the upcoming live streams, office hours, study groups, key events that are happening, including GTC DC, which is coming up.", "tokens": [51114, 492, 362, 527, 12183, 11, 597, 307, 264, 700, 2113, 456, 11, 597, 486, 1565, 291, 439, 264, 11500, 1621, 15842, 11, 3398, 2496, 11, 2979, 3935, 11, 2141, 3931, 300, 366, 2737, 11, 3009, 17530, 34, 9114, 11, 597, 307, 1348, 493, 13, 51714], "temperature": 0.0, "avg_logprob": -0.10517854690551758, "compression_ratio": 1.7054794520547945, "no_speech_prob": 0.04578465595841408}, {"id": 35, "seek": 29700, "start": 297.0, "end": 302.0, "text": " I can't believe there's not a slide on that. I'm actually going to bring it to the web page and I'll show it to you later. But GTC DC is coming up.", "tokens": [50364, 286, 393, 380, 1697, 456, 311, 406, 257, 4137, 322, 300, 13, 286, 478, 767, 516, 281, 1565, 309, 281, 264, 3670, 3028, 293, 286, 603, 855, 309, 281, 291, 1780, 13, 583, 17530, 34, 9114, 307, 1348, 493, 13, 50614], "temperature": 0.0, "avg_logprob": -0.10923074792932581, "compression_ratio": 1.6597014925373135, "no_speech_prob": 0.261017769575119}, {"id": 36, "seek": 29700, "start": 302.0, "end": 310.0, "text": " I'll be there. We always post those events in our event calendar. Very easy to subscribe and always find out about the latest.", "tokens": [50614, 286, 603, 312, 456, 13, 492, 1009, 2183, 729, 3931, 294, 527, 2280, 12183, 13, 4372, 1858, 281, 3022, 293, 1009, 915, 484, 466, 264, 6792, 13, 51014], "temperature": 0.0, "avg_logprob": -0.10923074792932581, "compression_ratio": 1.6597014925373135, "no_speech_prob": 0.261017769575119}, {"id": 37, "seek": 29700, "start": 310.0, "end": 318.0, "text": " Our forums are a great place to post your questions. If you have any blockers, absolutely leverage the forums for pretty much everything.", "tokens": [51014, 2621, 26998, 366, 257, 869, 1081, 281, 2183, 428, 1651, 13, 759, 291, 362, 604, 3461, 433, 11, 3122, 13982, 264, 26998, 337, 1238, 709, 1203, 13, 51414], "temperature": 0.0, "avg_logprob": -0.10923074792932581, "compression_ratio": 1.6597014925373135, "no_speech_prob": 0.261017769575119}, {"id": 38, "seek": 29700, "start": 318.0, "end": 326.0, "text": " Some exceptions include Isaac Lab, where we prefer you to go to the GitHub page for Isaac Lab, but <PERSON> has its own section on the forums.", "tokens": [51414, 2188, 22847, 4090, 22505, 10137, 11, 689, 321, 4382, 291, 281, 352, 281, 264, 23331, 3028, 337, 22505, 10137, 11, 457, 22505, 3998, 575, 1080, 1065, 3541, 322, 264, 26998, 13, 51814], "temperature": 0.0, "avg_logprob": -0.10923074792932581, "compression_ratio": 1.6597014925373135, "no_speech_prob": 0.261017769575119}, {"id": 39, "seek": 32600, "start": 326.0, "end": 340.0, "text": " Our Discord server, which I mentioned earlier, that's an amazing resource. We have thousands of developers that are on there posting regularly about their projects and assisting each other on a day-to-day basis.", "tokens": [50364, 2621, 32623, 7154, 11, 597, 286, 2835, 3071, 11, 300, 311, 364, 2243, 7684, 13, 492, 362, 5383, 295, 8849, 300, 366, 322, 456, 15978, 11672, 466, 641, 4455, 293, 40368, 1184, 661, 322, 257, 786, 12, 1353, 12, 810, 5143, 13, 51064], "temperature": 0.0, "avg_logprob": -0.09022290679230087, "compression_ratio": 1.5179282868525896, "no_speech_prob": 0.022537998855113983}, {"id": 40, "seek": 32600, "start": 340.0, "end": 348.0, "text": " Lots of great channels. It's well organized to find what you need quickly. Just use the search bar on the top right. Type anywhere you want. You'll get there in a flash.", "tokens": [51064, 15908, 295, 869, 9235, 13, 467, 311, 731, 9983, 281, 915, 437, 291, 643, 2661, 13, 1449, 764, 264, 3164, 2159, 322, 264, 1192, 558, 13, 15576, 4992, 291, 528, 13, 509, 603, 483, 456, 294, 257, 7319, 13, 51464], "temperature": 0.0, "avg_logprob": -0.09022290679230087, "compression_ratio": 1.5179282868525896, "no_speech_prob": 0.022537998855113983}, {"id": 41, "seek": 34800, "start": 348.0, "end": 358.0, "text": " Finally, that last link is our community page, where you can look up things, including our ambassadors and other resources we have in the community.", "tokens": [50364, 6288, 11, 300, 1036, 2113, 307, 527, 1768, 3028, 11, 689, 291, 393, 574, 493, 721, 11, 3009, 527, 44235, 293, 661, 3593, 321, 362, 294, 264, 1768, 13, 50864], "temperature": 0.0, "avg_logprob": -0.1259752628850002, "compression_ratio": 1.6245059288537549, "no_speech_prob": 0.46285343170166016}, {"id": 42, "seek": 34800, "start": 358.0, "end": 367.0, "text": " That is a lot of information for you. I hope everyone got good notes there. Now I would like to bring in my great colleague, <PERSON><PERSON><PERSON> here.", "tokens": [50864, 663, 307, 257, 688, 295, 1589, 337, 291, 13, 286, 1454, 1518, 658, 665, 5570, 456, 13, 823, 286, 576, 411, 281, 1565, 294, 452, 869, 13532, 11, 460, 12779, 71, 510, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1259752628850002, "compression_ratio": 1.6245059288537549, "no_speech_prob": 0.46285343170166016}, {"id": 43, "seek": 34800, "start": 367.0, "end": 376.0, "text": " <PERSON><PERSON><PERSON>, how are you doing? It is an honor to have you joining us here today. This is a very special episode we've got today.", "tokens": [51314, 460, 12779, 71, 11, 577, 366, 291, 884, 30, 467, 307, 364, 5968, 281, 362, 291, 5549, 505, 510, 965, 13, 639, 307, 257, 588, 2121, 3500, 321, 600, 658, 965, 13, 51764], "temperature": 0.0, "avg_logprob": -0.1259752628850002, "compression_ratio": 1.6245059288537549, "no_speech_prob": 0.46285343170166016}, {"id": 44, "seek": 37600, "start": 376.0, "end": 379.0, "text": " I'm building Agentec AI-powered digital twins, isn't it?", "tokens": [50364, 286, 478, 2390, 2725, 1576, 66, 7318, 12, 27178, 4562, 22555, 11, 1943, 380, 309, 30, 50514], "temperature": 0.0, "avg_logprob": -0.20064577430185646, "compression_ratio": 1.58203125, "no_speech_prob": 0.10953336954116821}, {"id": 45, "seek": 37600, "start": 379.0, "end": 399.0, "text": " Exactly. Thank you, <PERSON><PERSON>, for having me again. I'm excited to share the next 15 minutes with Sight Machine, Kinetic Vision and Microsoft on how we are actually taking digital twin directly into operational shop floor experiences.", "tokens": [50514, 7587, 13, 1044, 291, 11, 3977, 6209, 11, 337, 1419, 385, 797, 13, 286, 478, 2919, 281, 2073, 264, 958, 2119, 2077, 365, 318, 397, 22155, 11, 27950, 3532, 25170, 293, 8116, 322, 577, 321, 366, 767, 1940, 4562, 18397, 3838, 666, 16607, 3945, 4123, 5235, 13, 51514], "temperature": 0.0, "avg_logprob": -0.20064577430185646, "compression_ratio": 1.58203125, "no_speech_prob": 0.10953336954116821}, {"id": 46, "seek": 37600, "start": 399.0, "end": 404.0, "text": " And then how are we actually marrying that with <PERSON><PERSON> AI? I'm really excited to be here. Thank you for inviting me.", "tokens": [51514, 400, 550, 577, 366, 321, 767, 36376, 300, 365, 2725, 1576, 66, 7318, 30, 286, 478, 534, 2919, 281, 312, 510, 13, 1044, 291, 337, 18202, 385, 13, 51764], "temperature": 0.0, "avg_logprob": -0.20064577430185646, "compression_ratio": 1.58203125, "no_speech_prob": 0.10953336954116821}, {"id": 47, "seek": 40400, "start": 404.0, "end": 411.0, "text": " Of course. Let's bring in these very special guests. This is super exciting. We have <PERSON> here. Hey, <PERSON>.", "tokens": [50364, 2720, 1164, 13, 961, 311, 1565, 294, 613, 588, 2121, 9804, 13, 639, 307, 1687, 4670, 13, 492, 362, 17809, 510, 13, 1911, 11, 17809, 13, 50714], "temperature": 0.0, "avg_logprob": -0.12863798038933866, "compression_ratio": 1.5401785714285714, "no_speech_prob": 0.155458465218544}, {"id": 48, "seek": 40400, "start": 411.0, "end": 419.0, "text": " Hey, everybody. I'm from Kinetic Vision. It's so great to see you. Tell us a little about your background in Kinetic Vision.", "tokens": [50714, 1911, 11, 2201, 13, 286, 478, 490, 27950, 3532, 25170, 13, 467, 311, 370, 869, 281, 536, 291, 13, 5115, 505, 257, 707, 466, 428, 3678, 294, 27950, 3532, 25170, 13, 51114], "temperature": 0.0, "avg_logprob": -0.12863798038933866, "compression_ratio": 1.5401785714285714, "no_speech_prob": 0.155458465218544}, {"id": 49, "seek": 40400, "start": 419.0, "end": 426.0, "text": " Yeah, I'm hailing here from Cincinnati, Ohio, a great hub of US manufacturing. I'm the CEO of Kinetic Vision.", "tokens": [51114, 865, 11, 286, 478, 324, 4883, 510, 490, 45951, 11, 14469, 11, 257, 869, 11838, 295, 2546, 11096, 13, 286, 478, 264, 9282, 295, 27950, 3532, 25170, 13, 51464], "temperature": 0.0, "avg_logprob": -0.12863798038933866, "compression_ratio": 1.5401785714285714, "no_speech_prob": 0.155458465218544}, {"id": 50, "seek": 42600, "start": 426.0, "end": 435.0, "text": " We develop and integrate digital twins for manufacturing and supply chain. We're an NVIDIA partner. We're a Microsoft partner and we're a Sight Machine partner.", "tokens": [50364, 492, 1499, 293, 13365, 4562, 22555, 337, 11096, 293, 5847, 5021, 13, 492, 434, 364, 426, 3958, 6914, 4975, 13, 492, 434, 257, 8116, 4975, 293, 321, 434, 257, 318, 397, 22155, 4975, 13, 50814], "temperature": 0.0, "avg_logprob": -0.1190502690333946, "compression_ratio": 1.5970695970695972, "no_speech_prob": 0.45848095417022705}, {"id": 51, "seek": 42600, "start": 435.0, "end": 441.0, "text": " So really excited to be here. Talk with everybody today. And <PERSON><PERSON><PERSON> and <PERSON><PERSON>, thank you for inviting me.", "tokens": [50814, 407, 534, 2919, 281, 312, 510, 13, 8780, 365, 2201, 965, 13, 400, 460, 12779, 71, 293, 3977, 6209, 11, 1309, 291, 337, 18202, 385, 13, 51114], "temperature": 0.0, "avg_logprob": -0.1190502690333946, "compression_ratio": 1.5970695970695972, "no_speech_prob": 0.45848095417022705}, {"id": 52, "seek": 42600, "start": 441.0, "end": 450.0, "text": " Of course. I mean, I love to see when partners are working so closely together. You see some amazing things happen with these billion of minds that are working together.", "tokens": [51114, 2720, 1164, 13, 286, 914, 11, 286, 959, 281, 536, 562, 4462, 366, 1364, 370, 8185, 1214, 13, 509, 536, 512, 2243, 721, 1051, 365, 613, 5218, 295, 9634, 300, 366, 1364, 1214, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1190502690333946, "compression_ratio": 1.5970695970695972, "no_speech_prob": 0.45848095417022705}, {"id": 53, "seek": 45000, "start": 450.0, "end": 457.0, "text": " Speaking of brilliant minds, let me bring in my partner, <PERSON> here also for NVIDIAside. Hey, <PERSON>, how are you doing?", "tokens": [50364, 13069, 295, 10248, 9634, 11, 718, 385, 1565, 294, 452, 4975, 11, 29276, 510, 611, 337, 426, 3958, 6914, 1812, 13, 1911, 11, 29276, 11, 577, 366, 291, 884, 30, 50714], "temperature": 0.0, "avg_logprob": -0.11604515008166828, "compression_ratio": 1.6236162361623616, "no_speech_prob": 0.228164404630661}, {"id": 54, "seek": 45000, "start": 457.0, "end": 464.0, "text": " Good morning and good afternoon or good evening, depending on where you are. Hi, everyone. I'm <PERSON>.", "tokens": [50714, 2205, 2446, 293, 665, 6499, 420, 665, 5634, 11, 5413, 322, 689, 291, 366, 13, 2421, 11, 1518, 13, 286, 478, 29276, 363, 480, 266, 13, 51064], "temperature": 0.0, "avg_logprob": -0.11604515008166828, "compression_ratio": 1.6236162361623616, "no_speech_prob": 0.228164404630661}, {"id": 55, "seek": 45000, "start": 464.0, "end": 478.0, "text": " I work with <PERSON><PERSON><PERSON> and <PERSON> and the rest of the team and trying to bring together all of the good work we've done here in terms of digitization, if you will, and how we apply that into factories of the future.", "tokens": [51064, 286, 589, 365, 460, 12779, 71, 293, 17809, 293, 264, 1472, 295, 264, 1469, 293, 1382, 281, 1565, 1214, 439, 295, 264, 665, 589, 321, 600, 1096, 510, 294, 2115, 295, 14293, 2144, 11, 498, 291, 486, 11, 293, 577, 321, 3079, 300, 666, 24813, 295, 264, 2027, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11604515008166828, "compression_ratio": 1.6236162361623616, "no_speech_prob": 0.228164404630661}, {"id": 56, "seek": 47800, "start": 478.0, "end": 486.0, "text": " Amazing. I'm so glad to have you here. <PERSON> is an amazing person on our team, so he's going to have great context as well as <PERSON><PERSON><PERSON> throughout this episode.", "tokens": [50364, 14165, 13, 286, 478, 370, 5404, 281, 362, 291, 510, 13, 29276, 307, 364, 2243, 954, 322, 527, 1469, 11, 370, 415, 311, 516, 281, 362, 869, 4319, 382, 731, 382, 460, 12779, 71, 3710, 341, 3500, 13, 50764], "temperature": 0.0, "avg_logprob": -0.11823641667600537, "compression_ratio": 1.6088560885608856, "no_speech_prob": 0.05689140409231186}, {"id": 57, "seek": 47800, "start": 486.0, "end": 494.0, "text": " Here's a good friend also, a veteran of the live streams. Hey, <PERSON>. Hey, <PERSON><PERSON>, how are you doing? How you been, man? It's great to see you again.", "tokens": [50764, 1692, 311, 257, 665, 1277, 611, 11, 257, 18324, 295, 264, 1621, 15842, 13, 1911, 11, 25550, 13, 1911, 11, 3977, 6209, 11, 577, 366, 291, 884, 30, 1012, 291, 668, 11, 587, 30, 467, 311, 869, 281, 536, 291, 797, 13, 51164], "temperature": 0.0, "avg_logprob": -0.11823641667600537, "compression_ratio": 1.6088560885608856, "no_speech_prob": 0.05689140409231186}, {"id": 58, "seek": 47800, "start": 494.0, "end": 502.0, "text": " Doing good, doing good. Yeah, you too, man. So you have a special role. Why don't you tell me what you do over there at Microsoft?", "tokens": [51164, 18496, 665, 11, 884, 665, 13, 865, 11, 291, 886, 11, 587, 13, 407, 291, 362, 257, 2121, 3090, 13, 1545, 500, 380, 291, 980, 385, 437, 291, 360, 670, 456, 412, 8116, 30, 51564], "temperature": 0.0, "avg_logprob": -0.11823641667600537, "compression_ratio": 1.6088560885608856, "no_speech_prob": 0.05689140409231186}, {"id": 59, "seek": 50200, "start": 502.0, "end": 516.0, "text": " Well, at Microsoft, basically I work with a group called Industrial Solutions Engineering. So basically we go into customers and innovate with them, with partners like NVIDIA, you guys.", "tokens": [50364, 1042, 11, 412, 8116, 11, 1936, 286, 589, 365, 257, 1594, 1219, 32059, 36295, 16215, 13, 407, 1936, 321, 352, 666, 4581, 293, 33444, 365, 552, 11, 365, 4462, 411, 426, 3958, 6914, 11, 291, 1074, 13, 51064], "temperature": 0.0, "avg_logprob": -0.11685314411070288, "compression_ratio": 1.2937062937062938, "no_speech_prob": 0.14807434380054474}, {"id": 60, "seek": 51600, "start": 516.0, "end": 530.0, "text": " And we also have, I also work with a team that's actually in Houston, Texas. They have an actual facility where we build up hardware, like, for reels, and then, like, with PLCs.", "tokens": [50364, 400, 321, 611, 362, 11, 286, 611, 589, 365, 257, 1469, 300, 311, 767, 294, 18717, 11, 7885, 13, 814, 362, 364, 3539, 8973, 689, 321, 1322, 493, 8837, 11, 411, 11, 337, 319, 1625, 11, 293, 550, 11, 411, 11, 365, 6999, 33290, 13, 51064], "temperature": 0.0, "avg_logprob": -0.1699846405343911, "compression_ratio": 1.5727272727272728, "no_speech_prob": 0.7734357118606567}, {"id": 61, "seek": 51600, "start": 530.0, "end": 541.0, "text": " And we then test, like, getting data into the cloud and then doing stuff with it. And yeah, I've been working with <PERSON>, <PERSON><PERSON><PERSON>, and you all, <PERSON><PERSON>, like, for a while.", "tokens": [51064, 400, 321, 550, 1500, 11, 411, 11, 1242, 1412, 666, 264, 4588, 293, 550, 884, 1507, 365, 309, 13, 400, 1338, 11, 286, 600, 668, 1364, 365, 29276, 11, 460, 12779, 71, 11, 293, 291, 439, 11, 3977, 6209, 11, 411, 11, 337, 257, 1339, 13, 51614], "temperature": 0.0, "avg_logprob": -0.1699846405343911, "compression_ratio": 1.5727272727272728, "no_speech_prob": 0.7734357118606567}, {"id": 62, "seek": 54100, "start": 542.0, "end": 550.0, "text": " Especially <PERSON><PERSON><PERSON>, causing chaos wherever we go. So it's great to be here and good to see you.", "tokens": [50414, 8545, 460, 12779, 71, 11, 9853, 14158, 8660, 321, 352, 13, 407, 309, 311, 869, 281, 312, 510, 293, 665, 281, 536, 291, 13, 50814], "temperature": 0.0, "avg_logprob": -0.16136321043356872, "compression_ratio": 1.5745454545454545, "no_speech_prob": 0.25707387924194336}, {"id": 63, "seek": 54100, "start": 550.0, "end": 560.0, "text": " That's awesome. Well, thank you. I mentioned in my promo earlier I've had a lot of fun in rehearsals with this whole team of people. Let's bring in the last two people here. We've got <PERSON><PERSON>. Hey, <PERSON><PERSON>, how are you doing?", "tokens": [50814, 663, 311, 3476, 13, 1042, 11, 1309, 291, 13, 286, 2835, 294, 452, 26750, 3071, 286, 600, 632, 257, 688, 295, 1019, 294, 17052, 1124, 365, 341, 1379, 1469, 295, 561, 13, 961, 311, 1565, 294, 264, 1036, 732, 561, 510, 13, 492, 600, 658, 11789, 84, 13, 1911, 11, 11789, 84, 11, 577, 366, 291, 884, 30, 51314], "temperature": 0.0, "avg_logprob": -0.16136321043356872, "compression_ratio": 1.5745454545454545, "no_speech_prob": 0.25707387924194336}, {"id": 64, "seek": 54100, "start": 560.0, "end": 562.0, "text": " Hello, everyone. Nice to meet you.", "tokens": [51314, 2425, 11, 1518, 13, 5490, 281, 1677, 291, 13, 51414], "temperature": 0.0, "avg_logprob": -0.16136321043356872, "compression_ratio": 1.5745454545454545, "no_speech_prob": 0.25707387924194336}, {"id": 65, "seek": 54100, "start": 562.0, "end": 566.0, "text": " Why don't you tell everybody about your role and your background at Site Machine?", "tokens": [51414, 1545, 500, 380, 291, 980, 2201, 466, 428, 3090, 293, 428, 3678, 412, 34027, 22155, 30, 51614], "temperature": 0.0, "avg_logprob": -0.16136321043356872, "compression_ratio": 1.5745454545454545, "no_speech_prob": 0.25707387924194336}, {"id": 66, "seek": 56600, "start": 566.0, "end": 577.0, "text": " Absolutely. Hi, I'm <PERSON><PERSON>. I'm a director of product marketing at Site Machine. Site Machine is an industrial AI platform focused on delivering manufacturing outcomes at scale.", "tokens": [50364, 7021, 13, 2421, 11, 286, 478, 4327, 84, 591, 336, 9744, 13, 286, 478, 257, 5391, 295, 1674, 6370, 412, 34027, 22155, 13, 34027, 22155, 307, 364, 9987, 7318, 3663, 5178, 322, 14666, 11096, 10070, 412, 4373, 13, 50914], "temperature": 0.0, "avg_logprob": -0.08805729315532901, "compression_ratio": 1.6005917159763314, "no_speech_prob": 0.09048443287611008}, {"id": 67, "seek": 56600, "start": 577.0, "end": 588.0, "text": " Over the years, we've been working very closely with Microsoft and NVIDIA and our kinetic vision. So just really excited to, again, be here with everybody to talk about the compelling value of all of these technologies put together.", "tokens": [50914, 4886, 264, 924, 11, 321, 600, 668, 1364, 588, 8185, 365, 8116, 293, 426, 3958, 6914, 293, 527, 27135, 5201, 13, 407, 445, 534, 2919, 281, 11, 797, 11, 312, 510, 365, 2201, 281, 751, 466, 264, 20050, 2158, 295, 439, 295, 613, 7943, 829, 1214, 13, 51464], "temperature": 0.0, "avg_logprob": -0.08805729315532901, "compression_ratio": 1.6005917159763314, "no_speech_prob": 0.09048443287611008}, {"id": 68, "seek": 56600, "start": 588.0, "end": 593.0, "text": " Awesome. We're very excited to have you here. People are going to really love it. We're going to show off in a few minutes.", "tokens": [51464, 10391, 13, 492, 434, 588, 2919, 281, 362, 291, 510, 13, 3432, 366, 516, 281, 534, 959, 309, 13, 492, 434, 516, 281, 855, 766, 294, 257, 1326, 2077, 13, 51714], "temperature": 0.0, "avg_logprob": -0.08805729315532901, "compression_ratio": 1.6005917159763314, "no_speech_prob": 0.09048443287611008}, {"id": 69, "seek": 59300, "start": 593.0, "end": 598.0, "text": " Speaking of showing off, we've got <PERSON><PERSON> here. How are you doing, <PERSON><PERSON>?", "tokens": [50364, 13069, 295, 4099, 766, 11, 321, 600, 658, 19797, 24118, 510, 13, 1012, 366, 291, 884, 11, 19797, 24118, 30, 50614], "temperature": 0.0, "avg_logprob": -0.19533119201660157, "compression_ratio": 1.6724137931034482, "no_speech_prob": 0.12495650351047516}, {"id": 70, "seek": 59300, "start": 598.0, "end": 622.0, "text": " Hey, I'm doing great. Nice to see you all. Super excited to be here. I run engineering at Site Machine and we work a lot with Microsoft and NVIDIA and all of the folks on the squad and others to bring together Site Machine and Microsoft and NVIDIA, integrating omniverse and bringing true value to our management.", "tokens": [50614, 1911, 11, 286, 478, 884, 869, 13, 5490, 281, 536, 291, 439, 13, 4548, 2919, 281, 312, 510, 13, 286, 1190, 7043, 412, 34027, 22155, 293, 321, 589, 257, 688, 365, 8116, 293, 426, 3958, 6914, 293, 439, 295, 264, 4024, 322, 264, 15310, 293, 2357, 281, 1565, 1214, 34027, 22155, 293, 8116, 293, 426, 3958, 6914, 11, 26889, 36874, 5376, 293, 5062, 2074, 2158, 281, 527, 4592, 13, 51814], "temperature": 0.0, "avg_logprob": -0.19533119201660157, "compression_ratio": 1.6724137931034482, "no_speech_prob": 0.12495650351047516}, {"id": 71, "seek": 62200, "start": 623.0, "end": 633.0, "text": " That's awesome. Well, listen, I can't think of a better crew to tackle this topic today. So I think everyone should buckle up. I already see a ton of comments coming in.", "tokens": [50414, 663, 311, 3476, 13, 1042, 11, 2140, 11, 286, 393, 380, 519, 295, 257, 1101, 7260, 281, 14896, 341, 4829, 965, 13, 407, 286, 519, 1518, 820, 37686, 493, 13, 286, 1217, 536, 257, 2952, 295, 3053, 1348, 294, 13, 50914], "temperature": 0.0, "avg_logprob": -0.12553494841187865, "compression_ratio": 1.5504587155963303, "no_speech_prob": 0.09138855338096619}, {"id": 72, "seek": 62200, "start": 633.0, "end": 643.0, "text": " That's fantastic. We're going to get to some of those in a second, but why don't we set the stage here, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, for what we're going to really dive into here.", "tokens": [50914, 663, 311, 5456, 13, 492, 434, 516, 281, 483, 281, 512, 295, 729, 294, 257, 1150, 11, 457, 983, 500, 380, 321, 992, 264, 3233, 510, 11, 460, 12779, 71, 293, 318, 15612, 11, 337, 437, 321, 434, 516, 281, 534, 9192, 666, 510, 13, 51414], "temperature": 0.0, "avg_logprob": -0.12553494841187865, "compression_ratio": 1.5504587155963303, "no_speech_prob": 0.09138855338096619}, {"id": 73, "seek": 64300, "start": 644.0, "end": 661.0, "text": " Sounds good. So what are we going to see in the next 15 minutes or so? We're going to actually talk about how digital twins are actually shaping and driving the next wave of AI, which is deeply rooted into physical AI.", "tokens": [50414, 14576, 665, 13, 407, 437, 366, 321, 516, 281, 536, 294, 264, 958, 2119, 2077, 420, 370, 30, 492, 434, 516, 281, 767, 751, 466, 577, 4562, 22555, 366, 767, 25945, 293, 4840, 264, 958, 5772, 295, 7318, 11, 597, 307, 8760, 25277, 666, 4001, 7318, 13, 51264], "temperature": 0.0, "avg_logprob": -0.09843266244028129, "compression_ratio": 1.3974358974358974, "no_speech_prob": 0.09534396976232529}, {"id": 74, "seek": 66100, "start": 661.0, "end": 683.0, "text": " And then we're going to actually talk about how Site Machine as an ISV has taken the technology from NVIDIA as well as from Microsoft and bringing that to shop floor and factory operations with the help of kinetic visions where they are actually providing a specific key ingredient in building the digital twin itself", "tokens": [50364, 400, 550, 321, 434, 516, 281, 767, 751, 466, 577, 34027, 22155, 382, 364, 6205, 53, 575, 2726, 264, 2899, 490, 426, 3958, 6914, 382, 731, 382, 490, 8116, 293, 5062, 300, 281, 3945, 4123, 293, 9265, 7705, 365, 264, 854, 295, 27135, 30746, 689, 436, 366, 767, 6530, 257, 2685, 2141, 14751, 294, 2390, 264, 4562, 18397, 2564, 51464], "temperature": 0.0, "avg_logprob": -0.14175475589812747, "compression_ratio": 1.5023696682464456, "no_speech_prob": 0.32997336983680725}, {"id": 75, "seek": 68300, "start": 683.0, "end": 694.0, "text": " and how the 3D assets are actually getting converted into USD and how that is actually driving and powering the whole digital twin physical AI transformation.", "tokens": [50364, 293, 577, 264, 805, 35, 9769, 366, 767, 1242, 16424, 666, 24375, 293, 577, 300, 307, 767, 4840, 293, 1347, 278, 264, 1379, 4562, 18397, 4001, 7318, 9887, 13, 50914], "temperature": 0.0, "avg_logprob": -0.12498212743688512, "compression_ratio": 1.5391705069124424, "no_speech_prob": 0.2138449251651764}, {"id": 76, "seek": 68300, "start": 694.0, "end": 698.0, "text": " So that's what we're going to primarily cover in the next 15 minutes.", "tokens": [50914, 407, 300, 311, 437, 321, 434, 516, 281, 10029, 2060, 294, 264, 958, 2119, 2077, 13, 51114], "temperature": 0.0, "avg_logprob": -0.12498212743688512, "compression_ratio": 1.5391705069124424, "no_speech_prob": 0.2138449251651764}, {"id": 77, "seek": 68300, "start": 698.0, "end": 704.0, "text": " All right. Very cool. And I think with that, I think <PERSON><PERSON>, are you going to kick things off for us here?", "tokens": [51114, 1057, 558, 13, 4372, 1627, 13, 400, 286, 519, 365, 300, 11, 286, 519, 11789, 84, 11, 366, 291, 516, 281, 4437, 721, 766, 337, 505, 510, 30, 51414], "temperature": 0.0, "avg_logprob": -0.12498212743688512, "compression_ratio": 1.5391705069124424, "no_speech_prob": 0.2138449251651764}, {"id": 78, "seek": 70400, "start": 705.0, "end": 706.0, "text": " Yeah, let me...", "tokens": [50414, 865, 11, 718, 385, 485, 50464], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 79, "seek": 70400, "start": 706.0, "end": 707.0, "text": " Oh, go ahead.", "tokens": [50464, 876, 11, 352, 2286, 13, 50514], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 80, "seek": 70400, "start": 708.0, "end": 710.0, "text": " Sorry, are you going to say something?", "tokens": [50564, 4919, 11, 366, 291, 516, 281, 584, 746, 30, 50664], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 81, "seek": 70400, "start": 710.0, "end": 712.0, "text": " Oh, no, sorry. Go ahead, <PERSON><PERSON>.", "tokens": [50664, 876, 11, 572, 11, 2597, 13, 1037, 2286, 11, 11789, 84, 13, 50764], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 82, "seek": 70400, "start": 713.0, "end": 714.0, "text": " Yeah, go for it.", "tokens": [50814, 865, 11, 352, 337, 309, 13, 50864], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 83, "seek": 70400, "start": 714.0, "end": 715.0, "text": " All right.", "tokens": [50864, 1057, 558, 13, 50914], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 84, "seek": 70400, "start": 716.0, "end": 727.0, "text": " I wanted to talk a little bit about the specific manufacturing use cases that we're going to be solving with the solutions, because again, technology to solve real problems is what's very exciting to all of us.", "tokens": [50964, 286, 1415, 281, 751, 257, 707, 857, 466, 264, 2685, 11096, 764, 3331, 300, 321, 434, 516, 281, 312, 12606, 365, 264, 6547, 11, 570, 797, 11, 2899, 281, 5039, 957, 2740, 307, 437, 311, 588, 4670, 281, 439, 295, 505, 13, 51514], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 85, "seek": 70400, "start": 727.0, "end": 731.0, "text": " So give me a moment so I can share my screen.", "tokens": [51514, 407, 976, 385, 257, 1623, 370, 286, 393, 2073, 452, 2568, 13, 51714], "temperature": 0.0, "avg_logprob": -0.15150209835597447, "compression_ratio": 1.54, "no_speech_prob": 0.09818922728300095}, {"id": 86, "seek": 73100, "start": 731.0, "end": 737.0, "text": " Okay. And while she's going to go ahead and sharing her screen, I definitely invite everyone to start posting your questions and comments.", "tokens": [50364, 1033, 13, 400, 1339, 750, 311, 516, 281, 352, 2286, 293, 5414, 720, 2568, 11, 286, 2138, 7980, 1518, 281, 722, 15978, 428, 1651, 293, 3053, 13, 50664], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 87, "seek": 73100, "start": 738.0, "end": 741.0, "text": " We will be hitting those throughout the hour here.", "tokens": [50714, 492, 486, 312, 8850, 729, 3710, 264, 1773, 510, 13, 50864], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 88, "seek": 73100, "start": 742.0, "end": 744.0, "text": " And I think, <PERSON><PERSON>, I see your screen is ready for me to share.", "tokens": [50914, 400, 286, 519, 11, 11789, 84, 11, 286, 536, 428, 2568, 307, 1919, 337, 385, 281, 2073, 13, 51014], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 89, "seek": 73100, "start": 745.0, "end": 746.0, "text": " Yes, ready to share.", "tokens": [51064, 1079, 11, 1919, 281, 2073, 13, 51114], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 90, "seek": 73100, "start": 746.0, "end": 747.0, "text": " All right. Here we go.", "tokens": [51114, 1057, 558, 13, 1692, 321, 352, 13, 51164], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 91, "seek": 73100, "start": 749.0, "end": 753.0, "text": " All right. Like I just mentioned, we are an industrial AI data platform.", "tokens": [51264, 1057, 558, 13, 1743, 286, 445, 2835, 11, 321, 366, 364, 9987, 7318, 1412, 3663, 13, 51464], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 92, "seek": 73100, "start": 754.0, "end": 759.0, "text": " What we wanted to focus on today was what are the critical production challenges on the manufacturing shop floor.", "tokens": [51514, 708, 321, 1415, 281, 1879, 322, 965, 390, 437, 366, 264, 4924, 4265, 4759, 322, 264, 11096, 3945, 4123, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11303317546844482, "compression_ratio": 1.6296296296296295, "no_speech_prob": 0.002488443162292242}, {"id": 93, "seek": 75900, "start": 759.0, "end": 765.0, "text": " We are seeing operations teams constantly under varying conditions trying to achieve a lot of outcomes at once.", "tokens": [50364, 492, 366, 2577, 7705, 5491, 6460, 833, 22984, 4487, 1382, 281, 4584, 257, 688, 295, 10070, 412, 1564, 13, 50664], "temperature": 0.0, "avg_logprob": -0.0994842124707771, "compression_ratio": 1.6881720430107527, "no_speech_prob": 0.0076860967092216015}, {"id": 94, "seek": 75900, "start": 766.0, "end": 774.0, "text": " These outcomes are typically things like line throughput increase, which is really how do I make, produce as much as I can and run as efficiently as possible.", "tokens": [50714, 1981, 10070, 366, 5850, 721, 411, 1622, 44629, 3488, 11, 597, 307, 534, 577, 360, 286, 652, 11, 5258, 382, 709, 382, 286, 393, 293, 1190, 382, 19621, 382, 1944, 13, 51114], "temperature": 0.0, "avg_logprob": -0.0994842124707771, "compression_ratio": 1.6881720430107527, "no_speech_prob": 0.0076860967092216015}, {"id": 95, "seek": 75900, "start": 774.0, "end": 777.0, "text": " Schedule adherence, how do I make sure I'm not behind on production?", "tokens": [51114, 44926, 2271, 30106, 655, 11, 577, 360, 286, 652, 988, 286, 478, 406, 2261, 322, 4265, 30, 51264], "temperature": 0.0, "avg_logprob": -0.0994842124707771, "compression_ratio": 1.6881720430107527, "no_speech_prob": 0.0076860967092216015}, {"id": 96, "seek": 75900, "start": 778.0, "end": 786.0, "text": " How do I maximize my machine efficiency and maximize availability so that every machine is running close to its potential capacity?", "tokens": [51314, 1012, 360, 286, 19874, 452, 3479, 10493, 293, 19874, 17945, 370, 300, 633, 3479, 307, 2614, 1998, 281, 1080, 3995, 6042, 30, 51714], "temperature": 0.0, "avg_logprob": -0.0994842124707771, "compression_ratio": 1.6881720430107527, "no_speech_prob": 0.0076860967092216015}, {"id": 97, "seek": 78600, "start": 787.0, "end": 792.0, "text": " So all of these problems are things that we've been solving for over 10 years already for manufacturers.", "tokens": [50414, 407, 439, 295, 613, 2740, 366, 721, 300, 321, 600, 668, 12606, 337, 670, 1266, 924, 1217, 337, 18455, 13, 50664], "temperature": 0.0, "avg_logprob": -0.06494138529012491, "compression_ratio": 1.653225806451613, "no_speech_prob": 0.0028908541426062584}, {"id": 98, "seek": 78600, "start": 793.0, "end": 796.0, "text": " So that's sort of the landscape of challenges that customers are facing.", "tokens": [50714, 407, 300, 311, 1333, 295, 264, 9661, 295, 4759, 300, 4581, 366, 7170, 13, 50864], "temperature": 0.0, "avg_logprob": -0.06494138529012491, "compression_ratio": 1.653225806451613, "no_speech_prob": 0.0028908541426062584}, {"id": 99, "seek": 78600, "start": 797.0, "end": 809.0, "text": " But with all of the changes in technology, AI and, you know, innovations coming out, what manufacturers are struggling with most are things like user experiences.", "tokens": [50914, 583, 365, 439, 295, 264, 2962, 294, 2899, 11, 7318, 293, 11, 291, 458, 11, 24283, 1348, 484, 11, 437, 18455, 366, 9314, 365, 881, 366, 721, 411, 4195, 5235, 13, 51514], "temperature": 0.0, "avg_logprob": -0.06494138529012491, "compression_ratio": 1.653225806451613, "no_speech_prob": 0.0028908541426062584}, {"id": 100, "seek": 78600, "start": 810.0, "end": 813.0, "text": " How do you make sure that all of these are adopted on the shop floor?", "tokens": [51564, 1012, 360, 291, 652, 988, 300, 439, 295, 613, 366, 12175, 322, 264, 3945, 4123, 30, 51714], "temperature": 0.0, "avg_logprob": -0.06494138529012491, "compression_ratio": 1.653225806451613, "no_speech_prob": 0.0028908541426062584}, {"id": 101, "seek": 81300, "start": 813.0, "end": 817.0, "text": " How do you make sure that everybody in the enterprise has the same view of the line?", "tokens": [50364, 1012, 360, 291, 652, 988, 300, 2201, 294, 264, 14132, 575, 264, 912, 1910, 295, 264, 1622, 30, 50564], "temperature": 0.0, "avg_logprob": -0.0656225788700688, "compression_ratio": 1.701067615658363, "no_speech_prob": 0.002122283913195133}, {"id": 102, "seek": 81300, "start": 818.0, "end": 821.0, "text": " And these are very complex lines and they're very large production lines.", "tokens": [50614, 400, 613, 366, 588, 3997, 3876, 293, 436, 434, 588, 2416, 4265, 3876, 13, 50764], "temperature": 0.0, "avg_logprob": -0.0656225788700688, "compression_ratio": 1.701067615658363, "no_speech_prob": 0.002122283913195133}, {"id": 103, "seek": 81300, "start": 822.0, "end": 826.0, "text": " If you actually go to these plants, you literally can't see a couple of machines away from you.", "tokens": [50814, 759, 291, 767, 352, 281, 613, 5972, 11, 291, 3736, 393, 380, 536, 257, 1916, 295, 8379, 1314, 490, 291, 13, 51014], "temperature": 0.0, "avg_logprob": -0.0656225788700688, "compression_ratio": 1.701067615658363, "no_speech_prob": 0.002122283913195133}, {"id": 104, "seek": 81300, "start": 827.0, "end": 831.0, "text": " You can only see your machine in the next one, but you are impacted by what's happening on the rest of the line.", "tokens": [51064, 509, 393, 787, 536, 428, 3479, 294, 264, 958, 472, 11, 457, 291, 366, 15653, 538, 437, 311, 2737, 322, 264, 1472, 295, 264, 1622, 13, 51264], "temperature": 0.0, "avg_logprob": -0.0656225788700688, "compression_ratio": 1.701067615658363, "no_speech_prob": 0.002122283913195133}, {"id": 105, "seek": 81300, "start": 832.0, "end": 838.0, "text": " So understanding that system level, you know, process is extremely important and very challenging to do today.", "tokens": [51314, 407, 3701, 300, 1185, 1496, 11, 291, 458, 11, 1399, 307, 4664, 1021, 293, 588, 7595, 281, 360, 965, 13, 51614], "temperature": 0.0, "avg_logprob": -0.0656225788700688, "compression_ratio": 1.701067615658363, "no_speech_prob": 0.002122283913195133}, {"id": 106, "seek": 83800, "start": 838.0, "end": 848.0, "text": " So our vision, which is now being realized as we speak and being deployed with our clients is really about the power of 3D immersive visualization,", "tokens": [50364, 407, 527, 5201, 11, 597, 307, 586, 885, 5334, 382, 321, 1710, 293, 885, 17826, 365, 527, 6982, 307, 534, 466, 264, 1347, 295, 805, 35, 35409, 25801, 11, 50864], "temperature": 0.0, "avg_logprob": -0.08735221691345901, "compression_ratio": 1.5714285714285714, "no_speech_prob": 0.0008404432446695864}, {"id": 107, "seek": 83800, "start": 849.0, "end": 853.0, "text": " agentic AI for insights, as well as a real-time data platform.", "tokens": [50914, 9461, 299, 7318, 337, 14310, 11, 382, 731, 382, 257, 957, 12, 3766, 1412, 3663, 13, 51114], "temperature": 0.0, "avg_logprob": -0.08735221691345901, "compression_ratio": 1.5714285714285714, "no_speech_prob": 0.0008404432446695864}, {"id": 108, "seek": 83800, "start": 854.0, "end": 859.0, "text": " So we have found that this combination of technologies really provides the visual elements so you can see what is happening.", "tokens": [51164, 407, 321, 362, 1352, 300, 341, 6562, 295, 7943, 534, 6417, 264, 5056, 4959, 370, 291, 393, 536, 437, 307, 2737, 13, 51414], "temperature": 0.0, "avg_logprob": -0.08735221691345901, "compression_ratio": 1.5714285714285714, "no_speech_prob": 0.0008404432446695864}, {"id": 109, "seek": 83800, "start": 860.0, "end": 863.0, "text": " Everybody has a unified view of the enterprise and the line.", "tokens": [51464, 7646, 575, 257, 26787, 1910, 295, 264, 14132, 293, 264, 1622, 13, 51614], "temperature": 0.0, "avg_logprob": -0.08735221691345901, "compression_ratio": 1.5714285714285714, "no_speech_prob": 0.0008404432446695864}, {"id": 110, "seek": 86300, "start": 863.0, "end": 867.0, "text": " Agentic AI generates recommendations under varying line conditions.", "tokens": [50364, 27174, 299, 7318, 23815, 10434, 833, 22984, 1622, 4487, 13, 50564], "temperature": 0.0, "avg_logprob": -0.08871095995359783, "compression_ratio": 1.7013574660633484, "no_speech_prob": 0.0024265043903142214}, {"id": 111, "seek": 86300, "start": 868.0, "end": 875.0, "text": " So it's adapting to changing scenarios and it's providing the recommendations at the point of consumption against the 3D layer.", "tokens": [50614, 407, 309, 311, 34942, 281, 4473, 15077, 293, 309, 311, 6530, 264, 10434, 412, 264, 935, 295, 12126, 1970, 264, 805, 35, 4583, 13, 50964], "temperature": 0.0, "avg_logprob": -0.08871095995359783, "compression_ratio": 1.7013574660633484, "no_speech_prob": 0.0024265043903142214}, {"id": 112, "seek": 86300, "start": 876.0, "end": 885.0, "text": " So what you can see in this visual and what we'll be digging into and doing a deep dive on with the rest of our amazing team here is really how are these recommendations generated?", "tokens": [51014, 407, 437, 291, 393, 536, 294, 341, 5056, 293, 437, 321, 603, 312, 17343, 666, 293, 884, 257, 2452, 9192, 322, 365, 264, 1472, 295, 527, 2243, 1469, 510, 307, 534, 577, 366, 613, 10434, 10833, 30, 51464], "temperature": 0.0, "avg_logprob": -0.08871095995359783, "compression_ratio": 1.7013574660633484, "no_speech_prob": 0.0024265043903142214}, {"id": 113, "seek": 88500, "start": 886.0, "end": 892.0, "text": " How are these visualizations available and how does all of this work together seamlessly?", "tokens": [50414, 1012, 366, 613, 5056, 14455, 2435, 293, 577, 775, 439, 295, 341, 589, 1214, 38083, 30, 50714], "temperature": 0.0, "avg_logprob": -0.11429727941319562, "compression_ratio": 1.5, "no_speech_prob": 0.021363921463489532}, {"id": 114, "seek": 88500, "start": 893.0, "end": 895.0, "text": " They give a complete unified experience.", "tokens": [50764, 814, 976, 257, 3566, 26787, 1752, 13, 50864], "temperature": 0.0, "avg_logprob": -0.11429727941319562, "compression_ratio": 1.5, "no_speech_prob": 0.021363921463489532}, {"id": 115, "seek": 88500, "start": 896.0, "end": 906.0, "text": " So this is just an example of some of the real-world results we're seeing with our clients and I just wanted to paint the picture for what our manufacturing teams are looking to do.", "tokens": [50914, 407, 341, 307, 445, 364, 1365, 295, 512, 295, 264, 957, 12, 13217, 3542, 321, 434, 2577, 365, 527, 6982, 293, 286, 445, 1415, 281, 4225, 264, 3036, 337, 437, 527, 11096, 5491, 366, 1237, 281, 360, 13, 51414], "temperature": 0.0, "avg_logprob": -0.11429727941319562, "compression_ratio": 1.5, "no_speech_prob": 0.021363921463489532}, {"id": 116, "seek": 90600, "start": 906.0, "end": 916.0, "text": " That's all I have slide-wise so I'm going to stop sharing.", "tokens": [50364, 663, 311, 439, 286, 362, 4137, 12, 3711, 370, 286, 478, 516, 281, 1590, 5414, 13, 50864], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 117, "seek": 90600, "start": 917.0, "end": 918.0, "text": " Very cool.", "tokens": [50914, 4372, 1627, 13, 50964], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 118, "seek": 90600, "start": 919.0, "end": 920.0, "text": " And we have a quick video.", "tokens": [51014, 400, 321, 362, 257, 1702, 960, 13, 51064], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 119, "seek": 90600, "start": 921.0, "end": 923.0, "text": " Did you want me to play that now or is that for a little later?", "tokens": [51114, 2589, 291, 528, 385, 281, 862, 300, 586, 420, 307, 300, 337, 257, 707, 1780, 30, 51214], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 120, "seek": 90600, "start": 924.0, "end": 925.0, "text": " Sure.", "tokens": [51264, 4894, 13, 51314], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 121, "seek": 90600, "start": 925.0, "end": 927.0, "text": " I think, yes, I can speak for a minute as the video plays.", "tokens": [51314, 286, 519, 11, 2086, 11, 286, 393, 1710, 337, 257, 3456, 382, 264, 960, 5749, 13, 51414], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 122, "seek": 90600, "start": 928.0, "end": 929.0, "text": " <PERSON><PERSON>, that would be great.", "tokens": [51464, 3977, 6209, 11, 300, 576, 312, 869, 13, 51514], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 123, "seek": 90600, "start": 929.0, "end": 935.0, "text": " I think it's really important to understand again what the complexities are on the shop floor.", "tokens": [51514, 286, 519, 309, 311, 534, 1021, 281, 1223, 797, 437, 264, 48705, 366, 322, 264, 3945, 4123, 13, 51814], "temperature": 0.0, "avg_logprob": -0.19872052461198234, "compression_ratio": 1.519650655021834, "no_speech_prob": 0.13165557384490967}, {"id": 124, "seek": 93600, "start": 936.0, "end": 944.0, "text": " So when you see these 3D views, you'll really understand and as the video plays, you can see that this is a full view of a bottling line.", "tokens": [50364, 407, 562, 291, 536, 613, 805, 35, 6809, 11, 291, 603, 534, 1223, 293, 382, 264, 960, 5749, 11, 291, 393, 536, 300, 341, 307, 257, 1577, 1910, 295, 257, 2274, 1688, 1622, 13, 50764], "temperature": 0.0, "avg_logprob": -0.06727585969147859, "compression_ratio": 1.7351778656126482, "no_speech_prob": 0.0021473451051861048}, {"id": 125, "seek": 93600, "start": 945.0, "end": 953.0, "text": " Now, this is again, based on the scale, you can't really see how large it actually is, but you can see that in this 3D view, you can see the machine status and speed instantly.", "tokens": [50814, 823, 11, 341, 307, 797, 11, 2361, 322, 264, 4373, 11, 291, 393, 380, 534, 536, 577, 2416, 309, 767, 307, 11, 457, 291, 393, 536, 300, 294, 341, 805, 35, 1910, 11, 291, 393, 536, 264, 3479, 6558, 293, 3073, 13518, 13, 51214], "temperature": 0.0, "avg_logprob": -0.06727585969147859, "compression_ratio": 1.7351778656126482, "no_speech_prob": 0.0021473451051861048}, {"id": 126, "seek": 93600, "start": 954.0, "end": 960.0, "text": " You do not have to talk to operators and radio people or walk across the line or make decisions with incomplete information.", "tokens": [51264, 509, 360, 406, 362, 281, 751, 281, 19077, 293, 6477, 561, 420, 1792, 2108, 264, 1622, 420, 652, 5327, 365, 31709, 1589, 13, 51564], "temperature": 0.0, "avg_logprob": -0.06727585969147859, "compression_ratio": 1.7351778656126482, "no_speech_prob": 0.0021473451051861048}, {"id": 127, "seek": 96000, "start": 961.0, "end": 968.0, "text": " So again, the real focus is on providing those immersive 3D experiences with insights all in one space.", "tokens": [50414, 407, 797, 11, 264, 957, 1879, 307, 322, 6530, 729, 35409, 805, 35, 5235, 365, 14310, 439, 294, 472, 1901, 13, 50764], "temperature": 0.0, "avg_logprob": -0.08144897764379327, "compression_ratio": 1.475, "no_speech_prob": 0.021050328388810158}, {"id": 128, "seek": 96000, "start": 969.0, "end": 974.0, "text": " So the goal of this is to turn everybody on the shop floor team into a data-driven decision maker.", "tokens": [50814, 407, 264, 3387, 295, 341, 307, 281, 1261, 2201, 322, 264, 3945, 4123, 1469, 666, 257, 1412, 12, 25456, 3537, 17127, 13, 51064], "temperature": 0.0, "avg_logprob": -0.08144897764379327, "compression_ratio": 1.475, "no_speech_prob": 0.021050328388810158}, {"id": 129, "seek": 96000, "start": 975.0, "end": 976.0, "text": " Amazing.", "tokens": [51114, 14165, 13, 51164], "temperature": 0.0, "avg_logprob": -0.08144897764379327, "compression_ratio": 1.475, "no_speech_prob": 0.021050328388810158}, {"id": 130, "seek": 96000, "start": 976.0, "end": 977.0, "text": " Very cool.", "tokens": [51164, 4372, 1627, 13, 51214], "temperature": 0.0, "avg_logprob": -0.08144897764379327, "compression_ratio": 1.475, "no_speech_prob": 0.021050328388810158}, {"id": 131, "seek": 96000, "start": 977.0, "end": 979.0, "text": " That's so helpful to have that visualization.", "tokens": [51214, 663, 311, 370, 4961, 281, 362, 300, 25801, 13, 51314], "temperature": 0.0, "avg_logprob": -0.08144897764379327, "compression_ratio": 1.475, "no_speech_prob": 0.021050328388810158}, {"id": 132, "seek": 96000, "start": 980.0, "end": 984.0, "text": " Obviously, there's a lot of opportunity for companies to leverage this kind of stuff.", "tokens": [51364, 7580, 11, 456, 311, 257, 688, 295, 2650, 337, 3431, 281, 13982, 341, 733, 295, 1507, 13, 51564], "temperature": 0.0, "avg_logprob": -0.08144897764379327, "compression_ratio": 1.475, "no_speech_prob": 0.021050328388810158}, {"id": 133, "seek": 98400, "start": 984.0, "end": 989.0, "text": " I guess one thing I always think about when I see something that looks so amazing like this is the work involved.", "tokens": [50364, 286, 2041, 472, 551, 286, 1009, 519, 466, 562, 286, 536, 746, 300, 1542, 370, 2243, 411, 341, 307, 264, 589, 3288, 13, 50614], "temperature": 0.0, "avg_logprob": -0.13117092998087906, "compression_ratio": 1.7201365187713311, "no_speech_prob": 0.23061378300189972}, {"id": 134, "seek": 98400, "start": 990.0, "end": 995.0, "text": " I think sometimes people, companies might look at this and be a little overwhelmed or, oh my gosh, what's involved in that?", "tokens": [50664, 286, 519, 2171, 561, 11, 3431, 1062, 574, 412, 341, 293, 312, 257, 707, 19042, 420, 11, 1954, 452, 6502, 11, 437, 311, 3288, 294, 300, 30, 50914], "temperature": 0.0, "avg_logprob": -0.13117092998087906, "compression_ratio": 1.7201365187713311, "no_speech_prob": 0.23061378300189972}, {"id": 135, "seek": 98400, "start": 996.0, "end": 1001.0, "text": " But I think it's fair to say that people could take a very layered or phased approach to these kinds of scenarios.", "tokens": [50964, 583, 286, 519, 309, 311, 3143, 281, 584, 300, 561, 727, 747, 257, 588, 34666, 420, 903, 1937, 3109, 281, 613, 3685, 295, 15077, 13, 51214], "temperature": 0.0, "avg_logprob": -0.13117092998087906, "compression_ratio": 1.7201365187713311, "no_speech_prob": 0.23061378300189972}, {"id": 136, "seek": 98400, "start": 1002.0, "end": 1005.0, "text": " You don't have to go all in with everything at once.", "tokens": [51264, 509, 500, 380, 362, 281, 352, 439, 294, 365, 1203, 412, 1564, 13, 51414], "temperature": 0.0, "avg_logprob": -0.13117092998087906, "compression_ratio": 1.7201365187713311, "no_speech_prob": 0.23061378300189972}, {"id": 137, "seek": 98400, "start": 1006.0, "end": 1010.0, "text": " I'd be curious to see what you guys all think about suggestions for companies maybe watching that.", "tokens": [51464, 286, 1116, 312, 6369, 281, 536, 437, 291, 1074, 439, 519, 466, 13396, 337, 3431, 1310, 1976, 300, 13, 51664], "temperature": 0.0, "avg_logprob": -0.13117092998087906, "compression_ratio": 1.7201365187713311, "no_speech_prob": 0.23061378300189972}, {"id": 138, "seek": 101000, "start": 1010.0, "end": 1015.0, "text": " What would that layered approach look like or fees for getting started?", "tokens": [50364, 708, 576, 300, 34666, 3109, 574, 411, 420, 13370, 337, 1242, 1409, 30, 50614], "temperature": 0.0, "avg_logprob": -0.1509806717498393, "compression_ratio": 1.5112107623318385, "no_speech_prob": 0.0010305260075256228}, {"id": 139, "seek": 101000, "start": 1017.0, "end": 1025.0, "text": " I think we can answer that real quick, <PERSON><PERSON>, and then as you see what kinetic vision and site machine and the Microsoft team have done here,", "tokens": [50714, 286, 519, 321, 393, 1867, 300, 957, 1702, 11, 3977, 6209, 11, 293, 550, 382, 291, 536, 437, 27135, 5201, 293, 3621, 3479, 293, 264, 8116, 1469, 362, 1096, 510, 11, 51114], "temperature": 0.0, "avg_logprob": -0.1509806717498393, "compression_ratio": 1.5112107623318385, "no_speech_prob": 0.0010305260075256228}, {"id": 140, "seek": 101000, "start": 1026.0, "end": 1027.0, "text": " it'll unpack all of those.", "tokens": [51164, 309, 603, 26699, 439, 295, 729, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1509806717498393, "compression_ratio": 1.5112107623318385, "no_speech_prob": 0.0010305260075256228}, {"id": 141, "seek": 101000, "start": 1028.0, "end": 1033.0, "text": " But I think the key ingredient starts with how the customer thinks about a brownfield operation.", "tokens": [51264, 583, 286, 519, 264, 2141, 14751, 3719, 365, 577, 264, 5474, 7309, 466, 257, 6292, 7610, 6916, 13, 51514], "temperature": 0.0, "avg_logprob": -0.1509806717498393, "compression_ratio": 1.5112107623318385, "no_speech_prob": 0.0010305260075256228}, {"id": 142, "seek": 103300, "start": 1033.0, "end": 1039.0, "text": " So the example that <PERSON> was talking about is an existing operational bottling line.", "tokens": [50364, 407, 264, 1365, 300, 13754, 390, 1417, 466, 307, 364, 6741, 16607, 2274, 1688, 1622, 13, 50664], "temperature": 0.0, "avg_logprob": -0.10309182204209365, "compression_ratio": 1.5957446808510638, "no_speech_prob": 0.017053775489330292}, {"id": 143, "seek": 103300, "start": 1040.0, "end": 1042.0, "text": " They already have systems and processes in place.", "tokens": [50714, 814, 1217, 362, 3652, 293, 7555, 294, 1081, 13, 50814], "temperature": 0.0, "avg_logprob": -0.10309182204209365, "compression_ratio": 1.5957446808510638, "no_speech_prob": 0.017053775489330292}, {"id": 144, "seek": 103300, "start": 1043.0, "end": 1046.0, "text": " Some of them have been actually deployed over several years.", "tokens": [50864, 2188, 295, 552, 362, 668, 767, 17826, 670, 2940, 924, 13, 51014], "temperature": 0.0, "avg_logprob": -0.10309182204209365, "compression_ratio": 1.5957446808510638, "no_speech_prob": 0.017053775489330292}, {"id": 145, "seek": 103300, "start": 1047.0, "end": 1060.0, "text": " So it's part of that whole digital transformation journey where you need to actually deploy more advanced and more latest technologies to harvest that data from the shop floor,", "tokens": [51064, 407, 309, 311, 644, 295, 300, 1379, 4562, 9887, 4671, 689, 291, 643, 281, 767, 7274, 544, 7339, 293, 544, 6792, 7943, 281, 11917, 300, 1412, 490, 264, 3945, 4123, 11, 51714], "temperature": 0.0, "avg_logprob": -0.10309182204209365, "compression_ratio": 1.5957446808510638, "no_speech_prob": 0.017053775489330292}, {"id": 146, "seek": 106000, "start": 1060.0, "end": 1062.0, "text": " which is what site machines systems do.", "tokens": [50364, 597, 307, 437, 3621, 8379, 3652, 360, 13, 50464], "temperature": 0.0, "avg_logprob": -0.11193152717922045, "compression_ratio": 1.8784722222222223, "no_speech_prob": 0.0024263018276542425}, {"id": 147, "seek": 106000, "start": 1063.0, "end": 1068.0, "text": " And then you need to bring that into the cloud, which is what we're going to talk about in a second with the Microsoft architecture.", "tokens": [50514, 400, 550, 291, 643, 281, 1565, 300, 666, 264, 4588, 11, 597, 307, 437, 321, 434, 516, 281, 751, 466, 294, 257, 1150, 365, 264, 8116, 9482, 13, 50764], "temperature": 0.0, "avg_logprob": -0.11193152717922045, "compression_ratio": 1.8784722222222223, "no_speech_prob": 0.0024263018276542425}, {"id": 148, "seek": 106000, "start": 1069.0, "end": 1077.0, "text": " And then you start applying more advanced digital twin technologies with what we were showing earlier with omniverse and kid app streaming, which is what <PERSON><PERSON> is going to talk about.", "tokens": [50814, 400, 550, 291, 722, 9275, 544, 7339, 4562, 18397, 7943, 365, 437, 321, 645, 4099, 3071, 365, 36874, 5376, 293, 1636, 724, 11791, 11, 597, 307, 437, 318, 15612, 307, 516, 281, 751, 466, 13, 51214], "temperature": 0.0, "avg_logprob": -0.11193152717922045, "compression_ratio": 1.8784722222222223, "no_speech_prob": 0.0024263018276542425}, {"id": 149, "seek": 106000, "start": 1078.0, "end": 1088.0, "text": " So there are existing systems in place and then there are add-ons that is required to harness that data sets to really drive the digital transformations and building the digital twins.", "tokens": [51264, 407, 456, 366, 6741, 3652, 294, 1081, 293, 550, 456, 366, 909, 12, 892, 300, 307, 4739, 281, 19700, 300, 1412, 6352, 281, 534, 3332, 264, 4562, 34852, 293, 2390, 264, 4562, 22555, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11193152717922045, "compression_ratio": 1.8784722222222223, "no_speech_prob": 0.0024263018276542425}, {"id": 150, "seek": 108800, "start": 1088.0, "end": 1091.0, "text": " So we'll get into those in a second.", "tokens": [50364, 407, 321, 603, 483, 666, 729, 294, 257, 1150, 13, 50514], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 151, "seek": 108800, "start": 1092.0, "end": 1096.0, "text": " I think with that, we're probably going to hand over to <PERSON> and <PERSON><PERSON> to really unpack that architecture.", "tokens": [50564, 286, 519, 365, 300, 11, 321, 434, 1391, 516, 281, 1011, 670, 281, 25550, 293, 318, 15612, 281, 534, 26699, 300, 9482, 13, 50764], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 152, "seek": 108800, "start": 1097.0, "end": 1098.0, "text": " Amazing. Thank you, <PERSON><PERSON><PERSON>.", "tokens": [50814, 14165, 13, 1044, 291, 11, 460, 12779, 71, 13, 50864], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 153, "seek": 108800, "start": 1100.0, "end": 1101.0, "text": " Okay, so <PERSON>.", "tokens": [50964, 1033, 11, 370, 25550, 13, 51014], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 154, "seek": 108800, "start": 1102.0, "end": 1103.0, "text": " Okay, <PERSON> is fixing his microphone right now.", "tokens": [51064, 1033, 11, 25550, 307, 19442, 702, 10952, 558, 586, 13, 51114], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 155, "seek": 108800, "start": 1104.0, "end": 1106.0, "text": " So we'll let <PERSON><PERSON> take the start here.", "tokens": [51164, 407, 321, 603, 718, 318, 15612, 747, 264, 722, 510, 13, 51264], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 156, "seek": 108800, "start": 1107.0, "end": 1108.0, "text": " Sure.", "tokens": [51314, 4894, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 157, "seek": 108800, "start": 1109.0, "end": 1113.0, "text": " Yeah, so as <PERSON> is fixing his microphone, I think, <PERSON>, are you able to share with me?", "tokens": [51414, 865, 11, 370, 382, 25550, 307, 19442, 702, 10952, 11, 286, 519, 11, 25550, 11, 366, 291, 1075, 281, 2073, 365, 385, 30, 51614], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 158, "seek": 108800, "start": 1114.0, "end": 1115.0, "text": " Oh, you're on man, that's great.", "tokens": [51664, 876, 11, 291, 434, 322, 587, 11, 300, 311, 869, 13, 51714], "temperature": 0.0, "avg_logprob": -0.1749308955284857, "compression_ratio": 1.6639004149377594, "no_speech_prob": 0.01099332980811596}, {"id": 159, "seek": 111500, "start": 1116.0, "end": 1119.0, "text": " The slide that you had on our architecture.", "tokens": [50414, 440, 4137, 300, 291, 632, 322, 527, 9482, 13, 50564], "temperature": 0.0, "avg_logprob": -0.12832456106667992, "compression_ratio": 1.4612068965517242, "no_speech_prob": 0.0015368903987109661}, {"id": 160, "seek": 111500, "start": 1120.0, "end": 1128.0, "text": " Yeah, so <PERSON><PERSON>, I'll just show the initial thing we did back in the night first and then I'll pass it to you. Is that cool?", "tokens": [50614, 865, 11, 370, 318, 15612, 11, 286, 603, 445, 855, 264, 5883, 551, 321, 630, 646, 294, 264, 1818, 700, 293, 550, 286, 603, 1320, 309, 281, 291, 13, 1119, 300, 1627, 30, 51014], "temperature": 0.0, "avg_logprob": -0.12832456106667992, "compression_ratio": 1.4612068965517242, "no_speech_prob": 0.0015368903987109661}, {"id": 161, "seek": 111500, "start": 1129.0, "end": 1130.0, "text": " That's great.", "tokens": [51064, 663, 311, 869, 13, 51114], "temperature": 0.0, "avg_logprob": -0.12832456106667992, "compression_ratio": 1.4612068965517242, "no_speech_prob": 0.0015368903987109661}, {"id": 162, "seek": 111500, "start": 1131.0, "end": 1132.0, "text": " All right.", "tokens": [51164, 1057, 558, 13, 51214], "temperature": 0.0, "avg_logprob": -0.12832456106667992, "compression_ratio": 1.4612068965517242, "no_speech_prob": 0.0015368903987109661}, {"id": 163, "seek": 111500, "start": 1133.0, "end": 1142.0, "text": " So Microsoft and NVIDIA have been working together very closely for a while now to try to come up with a solution that combines the two platforms.", "tokens": [51264, 407, 8116, 293, 426, 3958, 6914, 362, 668, 1364, 1214, 588, 8185, 337, 257, 1339, 586, 281, 853, 281, 808, 493, 365, 257, 3827, 300, 29520, 264, 732, 9473, 13, 51714], "temperature": 0.0, "avg_logprob": -0.12832456106667992, "compression_ratio": 1.4612068965517242, "no_speech_prob": 0.0015368903987109661}, {"id": 164, "seek": 114200, "start": 1143.0, "end": 1146.0, "text": " And a solution that's also scalable, right?", "tokens": [50414, 400, 257, 3827, 300, 311, 611, 38481, 11, 558, 30, 50564], "temperature": 0.0, "avg_logprob": -0.11746368910136976, "compression_ratio": 1.380952380952381, "no_speech_prob": 0.002708528423681855}, {"id": 165, "seek": 114200, "start": 1147.0, "end": 1155.0, "text": " So at Microsoft, we do have this facility in Houston where we build hardware, pull out with PLCs.", "tokens": [50614, 407, 412, 8116, 11, 321, 360, 362, 341, 8973, 294, 18717, 689, 321, 1322, 8837, 11, 2235, 484, 365, 6999, 33290, 13, 51014], "temperature": 0.0, "avg_logprob": -0.11746368910136976, "compression_ratio": 1.380952380952381, "no_speech_prob": 0.002708528423681855}, {"id": 166, "seek": 114200, "start": 1156.0, "end": 1162.0, "text": " What you're looking at right now is a fluid process machine, which has pumps, valves and whatnot.", "tokens": [51064, 708, 291, 434, 1237, 412, 558, 586, 307, 257, 9113, 1399, 3479, 11, 597, 575, 27648, 11, 34950, 293, 25882, 13, 51364], "temperature": 0.0, "avg_logprob": -0.11746368910136976, "compression_ratio": 1.380952380952381, "no_speech_prob": 0.002708528423681855}, {"id": 167, "seek": 114200, "start": 1163.0, "end": 1165.0, "text": " And you can see the omniverse with the 3D context.", "tokens": [51414, 400, 291, 393, 536, 264, 36874, 5376, 365, 264, 805, 35, 4319, 13, 51514], "temperature": 0.0, "avg_logprob": -0.11746368910136976, "compression_ratio": 1.380952380952381, "no_speech_prob": 0.002708528423681855}, {"id": 168, "seek": 116500, "start": 1165.0, "end": 1168.0, "text": " You can see on the left the data from fabric.", "tokens": [50364, 509, 393, 536, 322, 264, 1411, 264, 1412, 490, 7253, 13, 50514], "temperature": 0.0, "avg_logprob": -0.15596753543185204, "compression_ratio": 1.5447470817120623, "no_speech_prob": 0.14763115346431732}, {"id": 169, "seek": 116500, "start": 1169.0, "end": 1171.0, "text": " That's actually an embedded PBI in a single pane of glass.", "tokens": [50564, 663, 311, 767, 364, 16741, 430, 11291, 294, 257, 2167, 32605, 295, 4276, 13, 50664], "temperature": 0.0, "avg_logprob": -0.15596753543185204, "compression_ratio": 1.5447470817120623, "no_speech_prob": 0.14763115346431732}, {"id": 170, "seek": 116500, "start": 1172.0, "end": 1193.0, "text": " And so we created this, demoed it at Ignite with kind of an inspirational architecture for the future, which the whole purpose is to inspire companies like Sight Machine to take a hold of and kind of have like a base template where Microsoft and NVIDIA are like, yep, this is a good approach.", "tokens": [50714, 400, 370, 321, 2942, 341, 11, 10723, 292, 309, 412, 24754, 642, 365, 733, 295, 364, 33554, 9482, 337, 264, 2027, 11, 597, 264, 1379, 4334, 307, 281, 15638, 3431, 411, 318, 397, 22155, 281, 747, 257, 1797, 295, 293, 733, 295, 362, 411, 257, 3096, 12379, 689, 8116, 293, 426, 3958, 6914, 366, 411, 11, 18633, 11, 341, 307, 257, 665, 3109, 13, 51764], "temperature": 0.0, "avg_logprob": -0.15596753543185204, "compression_ratio": 1.5447470817120623, "no_speech_prob": 0.14763115346431732}, {"id": 171, "seek": 119300, "start": 1193.0, "end": 1202.0, "text": " And it gives folks confidence that this is the right way to go when doing like this is an operational use case view.", "tokens": [50364, 400, 309, 2709, 4024, 6687, 300, 341, 307, 264, 558, 636, 281, 352, 562, 884, 411, 341, 307, 364, 16607, 764, 1389, 1910, 13, 50814], "temperature": 0.0, "avg_logprob": -0.17563980183702835, "compression_ratio": 1.3900709219858156, "no_speech_prob": 0.012795149348676205}, {"id": 172, "seek": 119300, "start": 1203.0, "end": 1210.0, "text": " So essentially then, so post Ignite, we posted an open source accelerator repo.", "tokens": [50864, 407, 4476, 550, 11, 370, 2183, 24754, 642, 11, 321, 9437, 364, 1269, 4009, 39889, 49040, 13, 51214], "temperature": 0.0, "avg_logprob": -0.17563980183702835, "compression_ratio": 1.3900709219858156, "no_speech_prob": 0.012795149348676205}, {"id": 173, "seek": 121000, "start": 1211.0, "end": 1216.0, "text": " And then Sight Machine got involved and worked with us and they took it and ran with it.", "tokens": [50414, 400, 550, 318, 397, 22155, 658, 3288, 293, 2732, 365, 505, 293, 436, 1890, 309, 293, 5872, 365, 309, 13, 50664], "temperature": 0.0, "avg_logprob": -0.15751116434733073, "compression_ratio": 1.5621621621621622, "no_speech_prob": 0.5366328954696655}, {"id": 174, "seek": 121000, "start": 1217.0, "end": 1224.0, "text": " And then went crazy cool in reality with the Coca-Cola consolidated bottling line.", "tokens": [50714, 400, 550, 1437, 3219, 1627, 294, 4103, 365, 264, 32719, 12, 42441, 49008, 2274, 1688, 1622, 13, 51064], "temperature": 0.0, "avg_logprob": -0.15751116434733073, "compression_ratio": 1.5621621621621622, "no_speech_prob": 0.5366328954696655}, {"id": 175, "seek": 121000, "start": 1225.0, "end": 1231.0, "text": " And let me pass it to <PERSON><PERSON> right after I go to the architecture.", "tokens": [51114, 400, 718, 385, 1320, 309, 281, 318, 15612, 558, 934, 286, 352, 281, 264, 9482, 13, 51414], "temperature": 0.0, "avg_logprob": -0.15751116434733073, "compression_ratio": 1.5621621621621622, "no_speech_prob": 0.5366328954696655}, {"id": 176, "seek": 121000, "start": 1232.0, "end": 1233.0, "text": " So this is the architecture and <PERSON><PERSON> take it away.", "tokens": [51464, 407, 341, 307, 264, 9482, 293, 318, 15612, 747, 309, 1314, 13, 51514], "temperature": 0.0, "avg_logprob": -0.15751116434733073, "compression_ratio": 1.5621621621621622, "no_speech_prob": 0.5366328954696655}, {"id": 177, "seek": 123300, "start": 1234.0, "end": 1235.0, "text": " Yeah, so thanks, <PERSON>.", "tokens": [50414, 865, 11, 370, 3231, 11, 25550, 13, 50464], "temperature": 0.0, "avg_logprob": -0.1421795572553362, "compression_ratio": 1.5971223021582734, "no_speech_prob": 0.07193299382925034}, {"id": 178, "seek": 123300, "start": 1236.0, "end": 1244.0, "text": " So what we're showing here is a design pattern, if you will, or architectural design pattern for creating an operational digital twin.", "tokens": [50514, 407, 437, 321, 434, 4099, 510, 307, 257, 1715, 5102, 11, 498, 291, 486, 11, 420, 26621, 1715, 5102, 337, 4084, 364, 16607, 4562, 18397, 13, 50914], "temperature": 0.0, "avg_logprob": -0.1421795572553362, "compression_ratio": 1.5971223021582734, "no_speech_prob": 0.07193299382925034}, {"id": 179, "seek": 123300, "start": 1245.0, "end": 1246.0, "text": " Now, this is an exemplar.", "tokens": [50964, 823, 11, 341, 307, 364, 24112, 289, 13, 51014], "temperature": 0.0, "avg_logprob": -0.1421795572553362, "compression_ratio": 1.5971223021582734, "no_speech_prob": 0.07193299382925034}, {"id": 180, "seek": 123300, "start": 1247.0, "end": 1253.0, "text": " It's not meant to be like the end off, but it gives you an idea of what to consider as you're trying to build out a capability.", "tokens": [51064, 467, 311, 406, 4140, 281, 312, 411, 264, 917, 766, 11, 457, 309, 2709, 291, 364, 1558, 295, 437, 281, 1949, 382, 291, 434, 1382, 281, 1322, 484, 257, 13759, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1421795572553362, "compression_ratio": 1.5971223021582734, "no_speech_prob": 0.07193299382925034}, {"id": 181, "seek": 123300, "start": 1254.0, "end": 1261.0, "text": " And I know in chat there's questions about like, how is this effective in terms of auto realistic renderings for users and so forth.", "tokens": [51414, 400, 286, 458, 294, 5081, 456, 311, 1651, 466, 411, 11, 577, 307, 341, 4942, 294, 2115, 295, 8399, 12465, 15529, 1109, 337, 5022, 293, 370, 5220, 13, 51764], "temperature": 0.0, "avg_logprob": -0.1421795572553362, "compression_ratio": 1.5971223021582734, "no_speech_prob": 0.07193299382925034}, {"id": 182, "seek": 126100, "start": 1261.0, "end": 1262.0, "text": " We'll get to that.", "tokens": [50364, 492, 603, 483, 281, 300, 13, 50414], "temperature": 0.0, "avg_logprob": -0.0796513251208384, "compression_ratio": 1.7042801556420233, "no_speech_prob": 0.0019392993999645114}, {"id": 183, "seek": 126100, "start": 1263.0, "end": 1267.0, "text": " Let me walk you through this and then we can start answering some of those concerns too.", "tokens": [50464, 961, 385, 1792, 291, 807, 341, 293, 550, 321, 393, 722, 13430, 512, 295, 729, 7389, 886, 13, 50664], "temperature": 0.0, "avg_logprob": -0.0796513251208384, "compression_ratio": 1.7042801556420233, "no_speech_prob": 0.0019392993999645114}, {"id": 184, "seek": 126100, "start": 1268.0, "end": 1275.0, "text": " Fundamentally, when we start thinking about these kinds of systems, where we start is usually in the data, right?", "tokens": [50714, 13493, 2466, 379, 11, 562, 321, 722, 1953, 466, 613, 3685, 295, 3652, 11, 689, 321, 722, 307, 2673, 294, 264, 1412, 11, 558, 30, 51064], "temperature": 0.0, "avg_logprob": -0.0796513251208384, "compression_ratio": 1.7042801556420233, "no_speech_prob": 0.0019392993999645114}, {"id": 185, "seek": 126100, "start": 1276.0, "end": 1281.0, "text": " And so that's on the left most side of this where you have edge computing, pulling in data.", "tokens": [51114, 400, 370, 300, 311, 322, 264, 1411, 881, 1252, 295, 341, 689, 291, 362, 4691, 15866, 11, 8407, 294, 1412, 13, 51364], "temperature": 0.0, "avg_logprob": -0.0796513251208384, "compression_ratio": 1.7042801556420233, "no_speech_prob": 0.0019392993999645114}, {"id": 186, "seek": 126100, "start": 1282.0, "end": 1289.0, "text": " You're going to take that data and you're going to have to do some kind of processing and staging of it into an environment.", "tokens": [51414, 509, 434, 516, 281, 747, 300, 1412, 293, 291, 434, 516, 281, 362, 281, 360, 512, 733, 295, 9007, 293, 41085, 295, 309, 666, 364, 2823, 13, 51764], "temperature": 0.0, "avg_logprob": -0.0796513251208384, "compression_ratio": 1.7042801556420233, "no_speech_prob": 0.0019392993999645114}, {"id": 187, "seek": 128900, "start": 1289.0, "end": 1293.0, "text": " In this case, we're using things like Azure IoT operations.", "tokens": [50364, 682, 341, 1389, 11, 321, 434, 1228, 721, 411, 11969, 30112, 7705, 13, 50564], "temperature": 0.0, "avg_logprob": -0.13984109259940483, "compression_ratio": 1.46, "no_speech_prob": 0.0033291801810264587}, {"id": 188, "seek": 128900, "start": 1294.0, "end": 1297.0, "text": " We're using Arc enabled Kubernetes to do that kind of stage.", "tokens": [50614, 492, 434, 1228, 21727, 15172, 23145, 281, 360, 300, 733, 295, 3233, 13, 50764], "temperature": 0.0, "avg_logprob": -0.13984109259940483, "compression_ratio": 1.46, "no_speech_prob": 0.0033291801810264587}, {"id": 189, "seek": 128900, "start": 1298.0, "end": 1302.0, "text": " Additionally, you'll have some level of 3D scene creation, and that has to also get managed.", "tokens": [50814, 19927, 11, 291, 603, 362, 512, 1496, 295, 805, 35, 4145, 8016, 11, 293, 300, 575, 281, 611, 483, 6453, 13, 51014], "temperature": 0.0, "avg_logprob": -0.13984109259940483, "compression_ratio": 1.46, "no_speech_prob": 0.0033291801810264587}, {"id": 190, "seek": 128900, "start": 1303.0, "end": 1307.0, "text": " So in our case here, we started putting that together with Azure Blob Storage.", "tokens": [51064, 407, 294, 527, 1389, 510, 11, 321, 1409, 3372, 300, 1214, 365, 11969, 9865, 65, 36308, 13, 51264], "temperature": 0.0, "avg_logprob": -0.13984109259940483, "compression_ratio": 1.46, "no_speech_prob": 0.0033291801810264587}, {"id": 191, "seek": 130700, "start": 1308.0, "end": 1312.0, "text": " These two pieces of information need to get correlated.", "tokens": [50414, 1981, 732, 3755, 295, 1589, 643, 281, 483, 38574, 13, 50614], "temperature": 0.0, "avg_logprob": -0.12847985777744028, "compression_ratio": 1.5260869565217392, "no_speech_prob": 0.030172813683748245}, {"id": 192, "seek": 130700, "start": 1313.0, "end": 1318.0, "text": " And that's where we're correlating it with Omniverse Kit and with Microsoft Fabric and Power BI.", "tokens": [50664, 400, 300, 311, 689, 321, 434, 13983, 990, 309, 365, 9757, 77, 5376, 23037, 293, 365, 8116, 17440, 1341, 293, 7086, 23524, 13, 50914], "temperature": 0.0, "avg_logprob": -0.12847985777744028, "compression_ratio": 1.5260869565217392, "no_speech_prob": 0.030172813683748245}, {"id": 193, "seek": 130700, "start": 1319.0, "end": 1327.0, "text": " So Fabric, Real-Time Intelligence and Azure Functions will take that data that we just received, convert it into more actionable data,", "tokens": [50964, 407, 17440, 1341, 11, 8467, 12, 22233, 27274, 293, 11969, 11166, 3916, 486, 747, 300, 1412, 300, 321, 445, 4613, 11, 7620, 309, 666, 544, 45098, 1412, 11, 51364], "temperature": 0.0, "avg_logprob": -0.12847985777744028, "compression_ratio": 1.5260869565217392, "no_speech_prob": 0.030172813683748245}, {"id": 194, "seek": 130700, "start": 1328.0, "end": 1331.0, "text": " convert it from, you know, bronze data to silver and gold data.", "tokens": [51414, 7620, 309, 490, 11, 291, 458, 11, 25454, 1412, 281, 8753, 293, 3821, 1412, 13, 51564], "temperature": 0.0, "avg_logprob": -0.12847985777744028, "compression_ratio": 1.5260869565217392, "no_speech_prob": 0.030172813683748245}, {"id": 195, "seek": 133100, "start": 1331.0, "end": 1341.0, "text": " And then the Power BI elements in block two will start overlaying that data into a user interface for a user.", "tokens": [50364, 400, 550, 264, 7086, 23524, 4959, 294, 3461, 732, 486, 722, 31741, 278, 300, 1412, 666, 257, 4195, 9226, 337, 257, 4195, 13, 50864], "temperature": 0.0, "avg_logprob": -0.11249184608459473, "compression_ratio": 1.594890510948905, "no_speech_prob": 0.025667980313301086}, {"id": 196, "seek": 133100, "start": 1342.0, "end": 1346.0, "text": " That's getting combined with the Omniverse Kit app streaming, the photo realistic rendering.", "tokens": [50914, 663, 311, 1242, 9354, 365, 264, 9757, 77, 5376, 23037, 724, 11791, 11, 264, 5052, 12465, 22407, 13, 51114], "temperature": 0.0, "avg_logprob": -0.11249184608459473, "compression_ratio": 1.594890510948905, "no_speech_prob": 0.025667980313301086}, {"id": 197, "seek": 133100, "start": 1347.0, "end": 1350.0, "text": " Now, the big question that most people will have is, well, okay, you've got these two streams.", "tokens": [51164, 823, 11, 264, 955, 1168, 300, 881, 561, 486, 362, 307, 11, 731, 11, 1392, 11, 291, 600, 658, 613, 732, 15842, 13, 51314], "temperature": 0.0, "avg_logprob": -0.11249184608459473, "compression_ratio": 1.594890510948905, "no_speech_prob": 0.025667980313301086}, {"id": 198, "seek": 133100, "start": 1351.0, "end": 1356.0, "text": " You've got this 3D enrichment or 3D data and you've got this IoT data, but how do you connect that?", "tokens": [51364, 509, 600, 658, 341, 805, 35, 49900, 420, 805, 35, 1412, 293, 291, 600, 658, 341, 30112, 1412, 11, 457, 577, 360, 291, 1745, 300, 30, 51614], "temperature": 0.0, "avg_logprob": -0.11249184608459473, "compression_ratio": 1.594890510948905, "no_speech_prob": 0.025667980313301086}, {"id": 199, "seek": 133100, "start": 1357.0, "end": 1359.0, "text": " And that's where it's kind of powerful.", "tokens": [51664, 400, 300, 311, 689, 309, 311, 733, 295, 4005, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11249184608459473, "compression_ratio": 1.594890510948905, "no_speech_prob": 0.025667980313301086}, {"id": 200, "seek": 135900, "start": 1359.0, "end": 1361.0, "text": " We put in enrichment into the USD.", "tokens": [50364, 492, 829, 294, 49900, 666, 264, 24375, 13, 50464], "temperature": 0.0, "avg_logprob": -0.10627214191029373, "compression_ratio": 1.6680497925311204, "no_speech_prob": 0.0012923197355121374}, {"id": 201, "seek": 135900, "start": 1362.0, "end": 1369.0, "text": " So the USD files that we create in Azure Blob Storage with the 3D data, we enrich them with ID information and other metadata.", "tokens": [50514, 407, 264, 24375, 7098, 300, 321, 1884, 294, 11969, 9865, 65, 36308, 365, 264, 805, 35, 1412, 11, 321, 18849, 552, 365, 7348, 1589, 293, 661, 26603, 13, 50864], "temperature": 0.0, "avg_logprob": -0.10627214191029373, "compression_ratio": 1.6680497925311204, "no_speech_prob": 0.0012923197355121374}, {"id": 202, "seek": 135900, "start": 1370.0, "end": 1372.0, "text": " I sometimes refer to that as syntactical sugar.", "tokens": [50914, 286, 2171, 2864, 281, 300, 382, 23980, 578, 804, 5076, 13, 51014], "temperature": 0.0, "avg_logprob": -0.10627214191029373, "compression_ratio": 1.6680497925311204, "no_speech_prob": 0.0012923197355121374}, {"id": 203, "seek": 135900, "start": 1373.0, "end": 1377.0, "text": " And that's what's brought in into Omniverse Kit and provides the front end.", "tokens": [51064, 400, 300, 311, 437, 311, 3038, 294, 666, 9757, 77, 5376, 23037, 293, 6417, 264, 1868, 917, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10627214191029373, "compression_ratio": 1.6680497925311204, "no_speech_prob": 0.0012923197355121374}, {"id": 204, "seek": 135900, "start": 1378.0, "end": 1384.0, "text": " And the front end is able to then map between the data sources coming in from Fabric to the data source in Omniverse", "tokens": [51314, 400, 264, 1868, 917, 307, 1075, 281, 550, 4471, 1296, 264, 1412, 7139, 1348, 294, 490, 17440, 1341, 281, 264, 1412, 4009, 294, 9757, 77, 5376, 51614], "temperature": 0.0, "avg_logprob": -0.10627214191029373, "compression_ratio": 1.6680497925311204, "no_speech_prob": 0.0012923197355121374}, {"id": 205, "seek": 138400, "start": 1384.0, "end": 1389.0, "text": " and give a user an immersive 3D environment plus the dashboarding effect.", "tokens": [50364, 293, 976, 257, 4195, 364, 35409, 805, 35, 2823, 1804, 264, 18342, 278, 1802, 13, 50614], "temperature": 0.0, "avg_logprob": -0.10789808360013095, "compression_ratio": 1.4594594594594594, "no_speech_prob": 0.0029096542857587337}, {"id": 206, "seek": 138400, "start": 1390.0, "end": 1392.0, "text": " So why, right?", "tokens": [50664, 407, 983, 11, 558, 30, 50764], "temperature": 0.0, "avg_logprob": -0.10789808360013095, "compression_ratio": 1.4594594594594594, "no_speech_prob": 0.0029096542857587337}, {"id": 207, "seek": 138400, "start": 1393.0, "end": 1394.0, "text": " Like, okay, this is great.", "tokens": [50814, 1743, 11, 1392, 11, 341, 307, 869, 13, 50864], "temperature": 0.0, "avg_logprob": -0.10789808360013095, "compression_ratio": 1.4594594594594594, "no_speech_prob": 0.0029096542857587337}, {"id": 208, "seek": 138400, "start": 1395.0, "end": 1400.0, "text": " And one of the questions is usually, so you can do in 3D, but how does this really help?", "tokens": [50914, 400, 472, 295, 264, 1651, 307, 2673, 11, 370, 291, 393, 360, 294, 805, 35, 11, 457, 577, 775, 341, 534, 854, 30, 51164], "temperature": 0.0, "avg_logprob": -0.10789808360013095, "compression_ratio": 1.4594594594594594, "no_speech_prob": 0.0029096542857587337}, {"id": 209, "seek": 138400, "start": 1401.0, "end": 1408.0, "text": " Now, if we can imagine in the world, as we're progressing into decision making, we want engineers, users and developers", "tokens": [51214, 823, 11, 498, 321, 393, 3811, 294, 264, 1002, 11, 382, 321, 434, 36305, 666, 3537, 1455, 11, 321, 528, 11955, 11, 5022, 293, 8849, 51564], "temperature": 0.0, "avg_logprob": -0.10789808360013095, "compression_ratio": 1.4594594594594594, "no_speech_prob": 0.0029096542857587337}, {"id": 210, "seek": 140800, "start": 1409.0, "end": 1418.0, "text": " to be able to quickly contextualize the space that they're in and be able to solve problems in that space.", "tokens": [50414, 281, 312, 1075, 281, 2661, 35526, 1125, 264, 1901, 300, 436, 434, 294, 293, 312, 1075, 281, 5039, 2740, 294, 300, 1901, 13, 50864], "temperature": 0.0, "avg_logprob": -0.0745951881011327, "compression_ratio": 1.6814159292035398, "no_speech_prob": 0.05932322517037392}, {"id": 211, "seek": 140800, "start": 1419.0, "end": 1428.0, "text": " Fundamentally, if you've got an area in a factory or you have a stoppage or a blockage and you want to understand where that is,", "tokens": [50914, 13493, 2466, 379, 11, 498, 291, 600, 658, 364, 1859, 294, 257, 9265, 420, 291, 362, 257, 1590, 15161, 420, 257, 3461, 609, 293, 291, 528, 281, 1223, 689, 300, 307, 11, 51364], "temperature": 0.0, "avg_logprob": -0.0745951881011327, "compression_ratio": 1.6814159292035398, "no_speech_prob": 0.05932322517037392}, {"id": 212, "seek": 140800, "start": 1429.0, "end": 1430.0, "text": " what's the context around it?", "tokens": [51414, 437, 311, 264, 4319, 926, 309, 30, 51464], "temperature": 0.0, "avg_logprob": -0.0745951881011327, "compression_ratio": 1.6814159292035398, "no_speech_prob": 0.05932322517037392}, {"id": 213, "seek": 140800, "start": 1431.0, "end": 1436.0, "text": " Maybe you're looking at understanding, okay, what's the ingress path to get to that location to service something?", "tokens": [51514, 2704, 291, 434, 1237, 412, 3701, 11, 1392, 11, 437, 311, 264, 3957, 735, 3100, 281, 483, 281, 300, 4914, 281, 2643, 746, 30, 51764], "temperature": 0.0, "avg_logprob": -0.0745951881011327, "compression_ratio": 1.6814159292035398, "no_speech_prob": 0.05932322517037392}, {"id": 214, "seek": 143600, "start": 1436.0, "end": 1438.0, "text": " Or what do I need to change?", "tokens": [50364, 1610, 437, 360, 286, 643, 281, 1319, 30, 50464], "temperature": 0.0, "avg_logprob": -0.11856000082833426, "compression_ratio": 1.595667870036101, "no_speech_prob": 0.003333951346576214}, {"id": 215, "seek": 143600, "start": 1439.0, "end": 1440.0, "text": " You need an immersive 3D environment.", "tokens": [50514, 509, 643, 364, 35409, 805, 35, 2823, 13, 50564], "temperature": 0.0, "avg_logprob": -0.11856000082833426, "compression_ratio": 1.595667870036101, "no_speech_prob": 0.003333951346576214}, {"id": 216, "seek": 143600, "start": 1441.0, "end": 1447.0, "text": " The photo realism part of it helps you in understanding both what you might do as a human operator,", "tokens": [50614, 440, 5052, 38484, 644, 295, 309, 3665, 291, 294, 3701, 1293, 437, 291, 1062, 360, 382, 257, 1952, 12973, 11, 50914], "temperature": 0.0, "avg_logprob": -0.11856000082833426, "compression_ratio": 1.595667870036101, "no_speech_prob": 0.003333951346576214}, {"id": 217, "seek": 143600, "start": 1448.0, "end": 1454.0, "text": " but also what you might do downstream when you're doing things like computer vision or other AI genetic kind of workflows", "tokens": [50964, 457, 611, 437, 291, 1062, 360, 30621, 562, 291, 434, 884, 721, 411, 3820, 5201, 420, 661, 7318, 12462, 733, 295, 43461, 51264], "temperature": 0.0, "avg_logprob": -0.11856000082833426, "compression_ratio": 1.595667870036101, "no_speech_prob": 0.003333951346576214}, {"id": 218, "seek": 143600, "start": 1455.0, "end": 1458.0, "text": " for simulation purposes, for training, what I've been so far.", "tokens": [51314, 337, 16575, 9932, 11, 337, 3097, 11, 437, 286, 600, 668, 370, 1400, 13, 51464], "temperature": 0.0, "avg_logprob": -0.11856000082833426, "compression_ratio": 1.595667870036101, "no_speech_prob": 0.003333951346576214}, {"id": 219, "seek": 143600, "start": 1459.0, "end": 1464.0, "text": " So this is kind of like the starting point in which you can then have downstream use cases.", "tokens": [51514, 407, 341, 307, 733, 295, 411, 264, 2891, 935, 294, 597, 291, 393, 550, 362, 30621, 764, 3331, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11856000082833426, "compression_ratio": 1.595667870036101, "no_speech_prob": 0.003333951346576214}, {"id": 220, "seek": 146400, "start": 1464.0, "end": 1473.0, "text": " And the starting point then becomes the same single pane of glass that you would use for both operations and for simulation environments", "tokens": [50364, 400, 264, 2891, 935, 550, 3643, 264, 912, 2167, 32605, 295, 4276, 300, 291, 576, 764, 337, 1293, 7705, 293, 337, 16575, 12388, 50814], "temperature": 0.0, "avg_logprob": -0.1286954023899176, "compression_ratio": 1.5436893203883495, "no_speech_prob": 0.0013643222628161311}, {"id": 221, "seek": 146400, "start": 1474.0, "end": 1475.0, "text": " and what if analysis and so forth.", "tokens": [50864, 293, 437, 498, 5215, 293, 370, 5220, 13, 50914], "temperature": 0.0, "avg_logprob": -0.1286954023899176, "compression_ratio": 1.5436893203883495, "no_speech_prob": 0.0013643222628161311}, {"id": 222, "seek": 146400, "start": 1476.0, "end": 1483.0, "text": " With that, I'd love to hand it over to <PERSON> and he can talk through how we created the digital twin.", "tokens": [50964, 2022, 300, 11, 286, 1116, 959, 281, 1011, 309, 670, 281, 17809, 293, 415, 393, 751, 807, 577, 321, 2942, 264, 4562, 18397, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1286954023899176, "compression_ratio": 1.5436893203883495, "no_speech_prob": 0.0013643222628161311}, {"id": 223, "seek": 146400, "start": 1487.0, "end": 1488.0, "text": " It's great. Thank you, <PERSON>. It's awesome.", "tokens": [51514, 467, 311, 869, 13, 1044, 291, 11, 29276, 13, 467, 311, 3476, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1286954023899176, "compression_ratio": 1.5436893203883495, "no_speech_prob": 0.0013643222628161311}, {"id": 224, "seek": 148800, "start": 1488.0, "end": 1493.0, "text": " <PERSON>, looks like you have the floor now. Are you ready?", "tokens": [50364, 17809, 11, 1542, 411, 291, 362, 264, 4123, 586, 13, 2014, 291, 1919, 30, 50614], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 225, "seek": 148800, "start": 1494.0, "end": 1495.0, "text": " I'm ready.", "tokens": [50664, 286, 478, 1919, 13, 50714], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 226, "seek": 148800, "start": 1496.0, "end": 1497.0, "text": " No pressure. No pressure.", "tokens": [50764, 883, 3321, 13, 883, 3321, 13, 50814], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 227, "seek": 148800, "start": 1498.0, "end": 1499.0, "text": " No, so let's see here.", "tokens": [50864, 883, 11, 370, 718, 311, 536, 510, 13, 50914], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 228, "seek": 148800, "start": 1500.0, "end": 1501.0, "text": " <PERSON><PERSON>, I've got a couple of things. There you go.", "tokens": [50964, 3977, 6209, 11, 286, 600, 658, 257, 1916, 295, 721, 13, 821, 291, 352, 13, 51014], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 229, "seek": 148800, "start": 1502.0, "end": 1503.0, "text": " All right, you're reading my mind here.", "tokens": [51064, 1057, 558, 11, 291, 434, 3760, 452, 1575, 510, 13, 51114], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 230, "seek": 148800, "start": 1504.0, "end": 1505.0, "text": " So let's start with this.", "tokens": [51164, 407, 718, 311, 722, 365, 341, 13, 51214], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 231, "seek": 148800, "start": 1506.0, "end": 1516.0, "text": " This is really just a basic representation of what we do to create the beautiful 3D asset that is inside", "tokens": [51264, 639, 307, 534, 445, 257, 3875, 10290, 295, 437, 321, 360, 281, 1884, 264, 2238, 805, 35, 11999, 300, 307, 1854, 51764], "temperature": 0.0, "avg_logprob": -0.15527578002040826, "compression_ratio": 1.54337899543379, "no_speech_prob": 0.014766423963010311}, {"id": 232, "seek": 151600, "start": 1516.0, "end": 1519.0, "text": " of factory operate or inside of site machine.", "tokens": [50364, 295, 9265, 9651, 420, 1854, 295, 3621, 3479, 13, 50514], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 233, "seek": 151600, "start": 1520.0, "end": 1523.0, "text": " And all I do is just, you know, we have a lot of people on this call.", "tokens": [50564, 400, 439, 286, 360, 307, 445, 11, 291, 458, 11, 321, 362, 257, 688, 295, 561, 322, 341, 818, 13, 50714], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 234, "seek": 151600, "start": 1524.0, "end": 1525.0, "text": " They're going to be at varying levels of knowledge about all this.", "tokens": [50764, 814, 434, 516, 281, 312, 412, 22984, 4358, 295, 3601, 466, 439, 341, 13, 50814], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 235, "seek": 151600, "start": 1526.0, "end": 1529.0, "text": " I saw some great questions about, you know, how do you create these assets?", "tokens": [50864, 286, 1866, 512, 869, 1651, 466, 11, 291, 458, 11, 577, 360, 291, 1884, 613, 9769, 30, 51014], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 236, "seek": 151600, "start": 1530.0, "end": 1531.0, "text": " What are the steps?", "tokens": [51064, 708, 366, 264, 4439, 30, 51114], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 237, "seek": 151600, "start": 1532.0, "end": 1533.0, "text": " And I'm going to take some time to go through that.", "tokens": [51164, 400, 286, 478, 516, 281, 747, 512, 565, 281, 352, 807, 300, 13, 51214], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 238, "seek": 151600, "start": 1534.0, "end": 1541.0, "text": " But, you know, <PERSON><PERSON>, you asked one very interesting thing about, hey, how does a company take like a layered approach to this?", "tokens": [51264, 583, 11, 291, 458, 11, 3977, 6209, 11, 291, 2351, 472, 588, 1880, 551, 466, 11, 4177, 11, 577, 775, 257, 2237, 747, 411, 257, 34666, 3109, 281, 341, 30, 51614], "temperature": 0.0, "avg_logprob": -0.09226572129034227, "compression_ratio": 1.6715328467153285, "no_speech_prob": 0.001390725839883089}, {"id": 239, "seek": 154100, "start": 1541.0, "end": 1549.0, "text": " And because we are, you know, integrating these solutions, we meet our customers where they're at.", "tokens": [50364, 400, 570, 321, 366, 11, 291, 458, 11, 26889, 613, 6547, 11, 321, 1677, 527, 4581, 689, 436, 434, 412, 13, 50764], "temperature": 0.0, "avg_logprob": -0.0961634116836741, "compression_ratio": 1.5534883720930233, "no_speech_prob": 0.0019744480960071087}, {"id": 240, "seek": 154100, "start": 1550.0, "end": 1551.0, "text": " And that's really important.", "tokens": [50814, 400, 300, 311, 534, 1021, 13, 50864], "temperature": 0.0, "avg_logprob": -0.0961634116836741, "compression_ratio": 1.5534883720930233, "no_speech_prob": 0.0019744480960071087}, {"id": 241, "seek": 154100, "start": 1552.0, "end": 1557.0, "text": " And so they may not be ready for a full blown site machine digital twin.", "tokens": [50914, 400, 370, 436, 815, 406, 312, 1919, 337, 257, 1577, 16479, 3621, 3479, 4562, 18397, 13, 51164], "temperature": 0.0, "avg_logprob": -0.0961634116836741, "compression_ratio": 1.5534883720930233, "no_speech_prob": 0.0019744480960071087}, {"id": 242, "seek": 154100, "start": 1558.0, "end": 1559.0, "text": " They may need something a little more basic than that.", "tokens": [51214, 814, 815, 643, 746, 257, 707, 544, 3875, 813, 300, 13, 51264], "temperature": 0.0, "avg_logprob": -0.0961634116836741, "compression_ratio": 1.5534883720930233, "no_speech_prob": 0.0019744480960071087}, {"id": 243, "seek": 154100, "start": 1560.0, "end": 1563.0, "text": " So we developed this really simple process called the choir activate optimize.", "tokens": [51314, 407, 321, 4743, 341, 534, 2199, 1399, 1219, 264, 31244, 13615, 19719, 13, 51464], "temperature": 0.0, "avg_logprob": -0.0961634116836741, "compression_ratio": 1.5534883720930233, "no_speech_prob": 0.0019744480960071087}, {"id": 244, "seek": 156300, "start": 1563.0, "end": 1572.0, "text": " And then, and then at the end, you know, collaboration through an amazing platform like omniverse really brings all of your stakeholders together.", "tokens": [50364, 400, 550, 11, 293, 550, 412, 264, 917, 11, 291, 458, 11, 9363, 807, 364, 2243, 3663, 411, 36874, 5376, 534, 5607, 439, 295, 428, 17779, 1214, 13, 50814], "temperature": 0.0, "avg_logprob": -0.08934253195057744, "compression_ratio": 1.626984126984127, "no_speech_prob": 0.11408969014883041}, {"id": 245, "seek": 156300, "start": 1573.0, "end": 1577.0, "text": " So from the choir standpoint, we're just, we're just talking about scan data, get your data.", "tokens": [50864, 407, 490, 264, 31244, 15827, 11, 321, 434, 445, 11, 321, 434, 445, 1417, 466, 11049, 1412, 11, 483, 428, 1412, 13, 51064], "temperature": 0.0, "avg_logprob": -0.08934253195057744, "compression_ratio": 1.626984126984127, "no_speech_prob": 0.11408969014883041}, {"id": 246, "seek": 156300, "start": 1578.0, "end": 1579.0, "text": " Most companies do not have a hold of their data.", "tokens": [51114, 4534, 3431, 360, 406, 362, 257, 1797, 295, 641, 1412, 13, 51164], "temperature": 0.0, "avg_logprob": -0.08934253195057744, "compression_ratio": 1.626984126984127, "no_speech_prob": 0.11408969014883041}, {"id": 247, "seek": 156300, "start": 1580.0, "end": 1587.0, "text": " Activate it using real simple software and then optimize it using data analysis that site machine provides or simulation.", "tokens": [51214, 28550, 473, 309, 1228, 957, 2199, 4722, 293, 550, 19719, 309, 1228, 1412, 5215, 300, 3621, 3479, 6417, 420, 16575, 13, 51564], "temperature": 0.0, "avg_logprob": -0.08934253195057744, "compression_ratio": 1.626984126984127, "no_speech_prob": 0.11408969014883041}, {"id": 248, "seek": 158700, "start": 1588.0, "end": 1598.0, "text": " And then in the end, when you have all of this fantastic compute available, you can immerse yourself and really create a connected experience with all of your users.", "tokens": [50414, 400, 550, 294, 264, 917, 11, 562, 291, 362, 439, 295, 341, 5456, 14722, 2435, 11, 291, 393, 5578, 405, 1803, 293, 534, 1884, 257, 4582, 1752, 365, 439, 295, 428, 5022, 13, 50914], "temperature": 0.0, "avg_logprob": -0.08408846338111234, "compression_ratio": 1.5982142857142858, "no_speech_prob": 0.05892021209001541}, {"id": 249, "seek": 158700, "start": 1599.0, "end": 1606.0, "text": " And so I also saw a question about, you know, how do you know when to use like a really high fidelity 3D model versus really basic representation?", "tokens": [50964, 400, 370, 286, 611, 1866, 257, 1168, 466, 11, 291, 458, 11, 577, 360, 291, 458, 562, 281, 764, 411, 257, 534, 1090, 46404, 805, 35, 2316, 5717, 534, 3875, 10290, 30, 51314], "temperature": 0.0, "avg_logprob": -0.08408846338111234, "compression_ratio": 1.5982142857142858, "no_speech_prob": 0.05892021209001541}, {"id": 250, "seek": 158700, "start": 1609.0, "end": 1611.0, "text": " You know, I always err towards immersiveness.", "tokens": [51464, 509, 458, 11, 286, 1009, 45267, 3030, 16787, 8477, 13, 51564], "temperature": 0.0, "avg_logprob": -0.08408846338111234, "compression_ratio": 1.5982142857142858, "no_speech_prob": 0.05892021209001541}, {"id": 251, "seek": 161100, "start": 1612.0, "end": 1617.0, "text": " If you can do immersiveness without friction, then the human, we are humans.", "tokens": [50414, 759, 291, 393, 360, 16787, 8477, 1553, 17710, 11, 550, 264, 1952, 11, 321, 366, 6255, 13, 50664], "temperature": 0.0, "avg_logprob": -0.08107626733701091, "compression_ratio": 1.803088803088803, "no_speech_prob": 0.06193647161126137}, {"id": 252, "seek": 161100, "start": 1618.0, "end": 1619.0, "text": " The human experience is going to be better.", "tokens": [50714, 440, 1952, 1752, 307, 516, 281, 312, 1101, 13, 50764], "temperature": 0.0, "avg_logprob": -0.08107626733701091, "compression_ratio": 1.803088803088803, "no_speech_prob": 0.06193647161126137}, {"id": 253, "seek": 161100, "start": 1620.0, "end": 1621.0, "text": " You're going to be inside the digital twin.", "tokens": [50814, 509, 434, 516, 281, 312, 1854, 264, 4562, 18397, 13, 50864], "temperature": 0.0, "avg_logprob": -0.08107626733701091, "compression_ratio": 1.803088803088803, "no_speech_prob": 0.06193647161126137}, {"id": 254, "seek": 161100, "start": 1622.0, "end": 1626.0, "text": " So the more that you, where you get into issues is where you have, you know, lots of data.", "tokens": [50914, 407, 264, 544, 300, 291, 11, 689, 291, 483, 666, 2663, 307, 689, 291, 362, 11, 291, 458, 11, 3195, 295, 1412, 13, 51114], "temperature": 0.0, "avg_logprob": -0.08107626733701091, "compression_ratio": 1.803088803088803, "no_speech_prob": 0.06193647161126137}, {"id": 255, "seek": 161100, "start": 1627.0, "end": 1632.0, "text": " It's complex or you, you know, can't display it very well or there's a lot of friction and understanding it.", "tokens": [51164, 467, 311, 3997, 420, 291, 11, 291, 458, 11, 393, 380, 4674, 309, 588, 731, 420, 456, 311, 257, 688, 295, 17710, 293, 3701, 309, 13, 51414], "temperature": 0.0, "avg_logprob": -0.08107626733701091, "compression_ratio": 1.803088803088803, "no_speech_prob": 0.06193647161126137}, {"id": 256, "seek": 161100, "start": 1633.0, "end": 1637.0, "text": " But if you can without friction err towards immersiveness, you're always going to be in a better spot.", "tokens": [51464, 583, 498, 291, 393, 1553, 17710, 45267, 3030, 16787, 8477, 11, 291, 434, 1009, 516, 281, 312, 294, 257, 1101, 4008, 13, 51664], "temperature": 0.0, "avg_logprob": -0.08107626733701091, "compression_ratio": 1.803088803088803, "no_speech_prob": 0.06193647161126137}, {"id": 257, "seek": 163700, "start": 1637.0, "end": 1642.0, "text": " Sometimes you need a little bit of visionary leadership there to, you know, kind of push an organization that direction.", "tokens": [50364, 4803, 291, 643, 257, 707, 857, 295, 49442, 5848, 456, 281, 11, 291, 458, 11, 733, 295, 2944, 364, 4475, 300, 3513, 13, 50614], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 258, "seek": 163700, "start": 1643.0, "end": 1644.0, "text": " But that's where we always tend towards.", "tokens": [50664, 583, 300, 311, 689, 321, 1009, 3928, 3030, 13, 50714], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 259, "seek": 163700, "start": 1645.0, "end": 1650.0, "text": " So, you know, I have a really simple video that makes this stuff look simple and feel simple.", "tokens": [50764, 407, 11, 291, 458, 11, 286, 362, 257, 534, 2199, 960, 300, 1669, 341, 1507, 574, 2199, 293, 841, 2199, 13, 51014], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 260, "seek": 163700, "start": 1651.0, "end": 1652.0, "text": " It's, I haven't labeled this video one.", "tokens": [51064, 467, 311, 11, 286, 2378, 380, 21335, 341, 960, 472, 13, 51114], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 261, "seek": 163700, "start": 1653.0, "end": 1654.0, "text": " I don't know if you can flip over there real quick.", "tokens": [51164, 286, 500, 380, 458, 498, 291, 393, 7929, 670, 456, 957, 1702, 13, 51214], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 262, "seek": 163700, "start": 1656.0, "end": 1657.0, "text": " Let me see. I'm looking at it. I see.", "tokens": [51314, 961, 385, 536, 13, 286, 478, 1237, 412, 309, 13, 286, 536, 13, 51364], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 263, "seek": 163700, "start": 1658.0, "end": 1659.0, "text": " Oh, yeah, I do see it. Let's see.", "tokens": [51414, 876, 11, 1338, 11, 286, 360, 536, 309, 13, 961, 311, 536, 13, 51464], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 264, "seek": 163700, "start": 1660.0, "end": 1661.0, "text": " I got three. I got all kinds of content.", "tokens": [51514, 286, 658, 1045, 13, 286, 658, 439, 3685, 295, 2701, 13, 51564], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 265, "seek": 163700, "start": 1661.0, "end": 1664.0, "text": " Even small delays can snowball into big disruptions.", "tokens": [51564, 2754, 1359, 28610, 393, 46143, 666, 955, 14124, 626, 13, 51714], "temperature": 0.0, "avg_logprob": -0.12184565597110325, "compression_ratio": 1.6986754966887416, "no_speech_prob": 0.015174143947660923}, {"id": 266, "seek": 166400, "start": 1664.0, "end": 1669.0, "text": " That's where digital twins come in with our acquire, activate, optimize process.", "tokens": [50364, 663, 311, 689, 4562, 22555, 808, 294, 365, 527, 20001, 11, 13615, 11, 19719, 1399, 13, 50614], "temperature": 0.0, "avg_logprob": -0.0949169567653111, "compression_ratio": 1.4195402298850575, "no_speech_prob": 0.030787378549575806}, {"id": 267, "seek": 166400, "start": 1670.0, "end": 1672.0, "text": " It's fast, easy and low risk.", "tokens": [50664, 467, 311, 2370, 11, 1858, 293, 2295, 3148, 13, 50764], "temperature": 0.0, "avg_logprob": -0.0949169567653111, "compression_ratio": 1.4195402298850575, "no_speech_prob": 0.030787378549575806}, {"id": 268, "seek": 166400, "start": 1673.0, "end": 1681.0, "text": " We start by scanning your facility in stunning detail up to 10 times faster than traditional methods with zero disruption to operations.", "tokens": [50814, 492, 722, 538, 27019, 428, 8973, 294, 18550, 2607, 493, 281, 1266, 1413, 4663, 813, 5164, 7150, 365, 4018, 28751, 281, 7705, 13, 51214], "temperature": 0.0, "avg_logprob": -0.0949169567653111, "compression_ratio": 1.4195402298850575, "no_speech_prob": 0.030787378549575806}, {"id": 269, "seek": 168100, "start": 1682.0, "end": 1695.0, "text": " That scan becomes a powerful 3D digital twin, enabling virtual design, remote tours and supporting system updates, layouts, training and safety planning.", "tokens": [50414, 663, 11049, 3643, 257, 4005, 805, 35, 4562, 18397, 11, 23148, 6374, 1715, 11, 8607, 22911, 293, 7231, 1185, 9205, 11, 46100, 11, 3097, 293, 4514, 5038, 13, 51064], "temperature": 0.0, "avg_logprob": -0.09049855617054722, "compression_ratio": 1.343915343915344, "no_speech_prob": 0.42590662837028503}, {"id": 270, "seek": 168100, "start": 1696.0, "end": 1702.0, "text": " In just 60 days, we uncover eight to 10 times first year ROI with payback in less than three months.", "tokens": [51114, 682, 445, 4060, 1708, 11, 321, 21694, 3180, 281, 1266, 1413, 700, 1064, 49808, 365, 1689, 3207, 294, 1570, 813, 1045, 2493, 13, 51414], "temperature": 0.0, "avg_logprob": -0.09049855617054722, "compression_ratio": 1.343915343915344, "no_speech_prob": 0.42590662837028503}, {"id": 271, "seek": 170200, "start": 1703.0, "end": 1711.0, "text": " AI delivers smart insights and actions to drive fast solutions and improve overall operation effectiveness.", "tokens": [50414, 7318, 24860, 4069, 14310, 293, 5909, 281, 3332, 2370, 6547, 293, 3470, 4787, 6916, 21208, 13, 50814], "temperature": 0.0, "avg_logprob": -0.07915889805760877, "compression_ratio": 1.5062240663900415, "no_speech_prob": 0.22908322513103485}, {"id": 272, "seek": 170200, "start": 1712.0, "end": 1714.0, "text": " Dashboards track performance in real time.", "tokens": [50864, 23453, 17228, 2837, 3389, 294, 957, 565, 13, 50964], "temperature": 0.0, "avg_logprob": -0.07915889805760877, "compression_ratio": 1.5062240663900415, "no_speech_prob": 0.22908322513103485}, {"id": 273, "seek": 170200, "start": 1715.0, "end": 1724.0, "text": " AI flags a labeler jam, triggers a fix and recommends a second forklift and dual labeler to boost material movement and throughput.", "tokens": [51014, 7318, 23265, 257, 2715, 6185, 7872, 11, 22827, 257, 3191, 293, 34556, 257, 1150, 337, 7837, 2008, 293, 11848, 2715, 6185, 281, 9194, 2527, 3963, 293, 44629, 13, 51464], "temperature": 0.0, "avg_logprob": -0.07915889805760877, "compression_ratio": 1.5062240663900415, "no_speech_prob": 0.22908322513103485}, {"id": 274, "seek": 170200, "start": 1725.0, "end": 1726.0, "text": " Have a problem or want to try a change?", "tokens": [51514, 3560, 257, 1154, 420, 528, 281, 853, 257, 1319, 30, 51564], "temperature": 0.0, "avg_logprob": -0.07915889805760877, "compression_ratio": 1.5062240663900415, "no_speech_prob": 0.22908322513103485}, {"id": 275, "seek": 170200, "start": 1727.0, "end": 1728.0, "text": " Simulate it first.", "tokens": [51614, 3998, 5256, 309, 700, 13, 51664], "temperature": 0.0, "avg_logprob": -0.07915889805760877, "compression_ratio": 1.5062240663900415, "no_speech_prob": 0.22908322513103485}, {"id": 276, "seek": 170200, "start": 1729.0, "end": 1730.0, "text": " No risk, no downtime.", "tokens": [51714, 883, 3148, 11, 572, 49648, 13, 51764], "temperature": 0.0, "avg_logprob": -0.07915889805760877, "compression_ratio": 1.5062240663900415, "no_speech_prob": 0.22908322513103485}, {"id": 277, "seek": 173000, "start": 1730.0, "end": 1733.0, "text": " Your digital twin drives smarter decisions.", "tokens": [50364, 2260, 4562, 18397, 11754, 20294, 5327, 13, 50514], "temperature": 0.0, "avg_logprob": -0.1699160823115596, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.000905281980521977}, {"id": 278, "seek": 173000, "start": 1734.0, "end": 1742.0, "text": " Once it works in one facility, it scales easily, making digital twins the perfect solution to transform your operations.", "tokens": [50564, 3443, 309, 1985, 294, 472, 8973, 11, 309, 17408, 3612, 11, 1455, 4562, 22555, 264, 2176, 3827, 281, 4088, 428, 7705, 13, 50964], "temperature": 0.0, "avg_logprob": -0.1699160823115596, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.000905281980521977}, {"id": 279, "seek": 173000, "start": 1744.0, "end": 1749.0, "text": " Okay, <PERSON><PERSON>, that was marketing glitz from our marketing department.", "tokens": [51064, 1033, 11, 3977, 6209, 11, 300, 390, 6370, 1563, 6862, 490, 527, 6370, 5882, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1699160823115596, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.000905281980521977}, {"id": 280, "seek": 173000, "start": 1749.0, "end": 1750.0, "text": " Makes it look simple.", "tokens": [51314, 25245, 309, 574, 2199, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1699160823115596, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.000905281980521977}, {"id": 281, "seek": 173000, "start": 1751.0, "end": 1754.0, "text": " Well, I gotta tell you, there was a couple of pieces of information that were super compelling.", "tokens": [51414, 1042, 11, 286, 3428, 980, 291, 11, 456, 390, 257, 1916, 295, 3755, 295, 1589, 300, 645, 1687, 20050, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1699160823115596, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.000905281980521977}, {"id": 282, "seek": 175400, "start": 1755.0, "end": 1759.0, "text": " The ROI of three months, that's pretty amazing.", "tokens": [50414, 440, 49808, 295, 1045, 2493, 11, 300, 311, 1238, 2243, 13, 50614], "temperature": 0.0, "avg_logprob": -0.19684672591709854, "compression_ratio": 1.5466101694915255, "no_speech_prob": 0.06921909749507904}, {"id": 283, "seek": 175400, "start": 1760.0, "end": 1764.0, "text": " And also the granularity of detail, down to five millimeters.", "tokens": [50664, 400, 611, 264, 39962, 507, 295, 2607, 11, 760, 281, 1732, 24388, 13, 50864], "temperature": 0.0, "avg_logprob": -0.19684672591709854, "compression_ratio": 1.5466101694915255, "no_speech_prob": 0.06921909749507904}, {"id": 284, "seek": 175400, "start": 1765.0, "end": 1766.0, "text": " It's pretty wild.", "tokens": [50914, 467, 311, 1238, 4868, 13, 50964], "temperature": 0.0, "avg_logprob": -0.19684672591709854, "compression_ratio": 1.5466101694915255, "no_speech_prob": 0.06921909749507904}, {"id": 285, "seek": 175400, "start": 1767.0, "end": 1770.0, "text": " Yeah, so can you queue up, there's something called Video 2 in there?", "tokens": [51014, 865, 11, 370, 393, 291, 18639, 493, 11, 456, 311, 746, 1219, 9777, 568, 294, 456, 30, 51164], "temperature": 0.0, "avg_logprob": -0.19684672591709854, "compression_ratio": 1.5466101694915255, "no_speech_prob": 0.06921909749507904}, {"id": 286, "seek": 175400, "start": 1771.0, "end": 1775.0, "text": " So this is going to be a little more like, what does it look like when you do this stuff?", "tokens": [51214, 407, 341, 307, 516, 281, 312, 257, 707, 544, 411, 11, 437, 775, 309, 574, 411, 562, 291, 360, 341, 1507, 30, 51414], "temperature": 0.0, "avg_logprob": -0.19684672591709854, "compression_ratio": 1.5466101694915255, "no_speech_prob": 0.06921909749507904}, {"id": 287, "seek": 175400, "start": 1776.0, "end": 1780.0, "text": " Like, who's doing what? When are they doing it? What programs are they using?", "tokens": [51464, 1743, 11, 567, 311, 884, 437, 30, 1133, 366, 436, 884, 309, 30, 708, 4268, 366, 436, 1228, 30, 51664], "temperature": 0.0, "avg_logprob": -0.19684672591709854, "compression_ratio": 1.5466101694915255, "no_speech_prob": 0.06921909749507904}, {"id": 288, "seek": 178000, "start": 1780.0, "end": 1785.0, "text": " So just queue that up, that'll be another minute, and then I'll talk a little more after that.", "tokens": [50364, 407, 445, 18639, 300, 493, 11, 300, 603, 312, 1071, 3456, 11, 293, 550, 286, 603, 751, 257, 707, 544, 934, 300, 13, 50614], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 289, "seek": 178000, "start": 1786.0, "end": 1787.0, "text": " Okay, here we go.", "tokens": [50664, 1033, 11, 510, 321, 352, 13, 50714], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 290, "seek": 178000, "start": 1788.0, "end": 1791.0, "text": " And there's no audio on this.", "tokens": [50764, 400, 456, 311, 572, 6278, 322, 341, 13, 50914], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 291, "seek": 178000, "start": 1792.0, "end": 1799.0, "text": " So really all we're showing is just a case study here of taking this acquire, activate, optimize process to a real facility.", "tokens": [50964, 407, 534, 439, 321, 434, 4099, 307, 445, 257, 1389, 2979, 510, 295, 1940, 341, 20001, 11, 13615, 11, 19719, 1399, 281, 257, 957, 8973, 13, 51314], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 292, "seek": 178000, "start": 1800.0, "end": 1801.0, "text": " This is a distribution center.", "tokens": [51364, 639, 307, 257, 7316, 3056, 13, 51414], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 293, "seek": 178000, "start": 1802.0, "end": 1803.0, "text": " So <PERSON>'s out there scanning it.", "tokens": [51464, 407, 10765, 311, 484, 456, 27019, 309, 13, 51514], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 294, "seek": 178000, "start": 1804.0, "end": 1806.0, "text": " He looks a lot like that little iconography we made of him.", "tokens": [51564, 634, 1542, 257, 688, 411, 300, 707, 6528, 5820, 321, 1027, 295, 796, 13, 51664], "temperature": 0.0, "avg_logprob": -0.14969253540039062, "compression_ratio": 1.568, "no_speech_prob": 0.0020319989416748285}, {"id": 295, "seek": 180600, "start": 1807.0, "end": 1813.0, "text": " We use real, we take that data, grab that 3D data, and then we use some other NVIDIA partners.", "tokens": [50414, 492, 764, 957, 11, 321, 747, 300, 1412, 11, 4444, 300, 805, 35, 1412, 11, 293, 550, 321, 764, 512, 661, 426, 3958, 6914, 4462, 13, 50714], "temperature": 0.0, "avg_logprob": -0.1287344079268606, "compression_ratio": 1.5104602510460252, "no_speech_prob": 0.005370276514440775}, {"id": 296, "seek": 180600, "start": 1814.0, "end": 1821.0, "text": " This is Preview 3D on this particular project to really quickly get measurements, get panos.", "tokens": [50764, 639, 307, 6001, 1759, 805, 35, 322, 341, 1729, 1716, 281, 534, 2661, 483, 15383, 11, 483, 2462, 329, 13, 51114], "temperature": 0.0, "avg_logprob": -0.1287344079268606, "compression_ratio": 1.5104602510460252, "no_speech_prob": 0.005370276514440775}, {"id": 297, "seek": 180600, "start": 1822.0, "end": 1825.0, "text": " This is a simulation tool that we happen to use called FlexSim.", "tokens": [51164, 639, 307, 257, 16575, 2290, 300, 321, 1051, 281, 764, 1219, 29208, 39392, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1287344079268606, "compression_ratio": 1.5104602510460252, "no_speech_prob": 0.005370276514440775}, {"id": 298, "seek": 180600, "start": 1827.0, "end": 1832.0, "text": " And just, you know, once again, an alternate process, not related to what we're doing with site machine here,", "tokens": [51414, 400, 445, 11, 291, 458, 11, 1564, 797, 11, 364, 18873, 1399, 11, 406, 4077, 281, 437, 321, 434, 884, 365, 3621, 3479, 510, 11, 51664], "temperature": 0.0, "avg_logprob": -0.1287344079268606, "compression_ratio": 1.5104602510460252, "no_speech_prob": 0.005370276514440775}, {"id": 299, "seek": 183200, "start": 1832.0, "end": 1835.0, "text": " but another method to optimize the site.", "tokens": [50364, 457, 1071, 3170, 281, 19719, 264, 3621, 13, 50514], "temperature": 0.0, "avg_logprob": -0.10307037129121668, "compression_ratio": 1.657258064516129, "no_speech_prob": 0.001743727014400065}, {"id": 300, "seek": 183200, "start": 1836.0, "end": 1840.0, "text": " Taking those, we're actually doing virtual camera simulation here just to make sure,", "tokens": [50564, 17837, 729, 11, 321, 434, 767, 884, 6374, 2799, 16575, 510, 445, 281, 652, 988, 11, 50764], "temperature": 0.0, "avg_logprob": -0.10307037129121668, "compression_ratio": 1.657258064516129, "no_speech_prob": 0.001743727014400065}, {"id": 301, "seek": 183200, "start": 1841.0, "end": 1846.0, "text": " even stuff that's as simple as get your camera set up in the right place before you install them, that can be all done virtually.", "tokens": [50814, 754, 1507, 300, 311, 382, 2199, 382, 483, 428, 2799, 992, 493, 294, 264, 558, 1081, 949, 291, 3625, 552, 11, 300, 393, 312, 439, 1096, 14103, 13, 51064], "temperature": 0.0, "avg_logprob": -0.10307037129121668, "compression_ratio": 1.657258064516129, "no_speech_prob": 0.001743727014400065}, {"id": 302, "seek": 183200, "start": 1847.0, "end": 1850.0, "text": " And it saves a lot of time and it saves big mess ups.", "tokens": [51114, 400, 309, 19155, 257, 688, 295, 565, 293, 309, 19155, 955, 2082, 15497, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10307037129121668, "compression_ratio": 1.657258064516129, "no_speech_prob": 0.001743727014400065}, {"id": 303, "seek": 183200, "start": 1851.0, "end": 1856.0, "text": " So just a couple of visuals of a recent case study, that's a real project we did for a real customer.", "tokens": [51314, 407, 445, 257, 1916, 295, 26035, 295, 257, 5162, 1389, 2979, 11, 300, 311, 257, 957, 1716, 321, 630, 337, 257, 957, 5474, 13, 51564], "temperature": 0.0, "avg_logprob": -0.10307037129121668, "compression_ratio": 1.657258064516129, "no_speech_prob": 0.001743727014400065}, {"id": 304, "seek": 185600, "start": 1856.0, "end": 1861.0, "text": " And we did deliver, that was like a three month payback.", "tokens": [50364, 400, 321, 630, 4239, 11, 300, 390, 411, 257, 1045, 1618, 1689, 3207, 13, 50614], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 305, "seek": 185600, "start": 1862.0, "end": 1865.0, "text": " And there's some big decisions being made off of what we found digitally.", "tokens": [50664, 400, 456, 311, 512, 955, 5327, 885, 1027, 766, 295, 437, 321, 1352, 36938, 13, 50814], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 306, "seek": 185600, "start": 1866.0, "end": 1868.0, "text": " The biggest thing is not disrupting the operation.", "tokens": [50864, 440, 3880, 551, 307, 406, 14124, 278, 264, 6916, 13, 50964], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 307, "seek": 185600, "start": 1869.0, "end": 1870.0, "text": " That's kind of the biggest thing.", "tokens": [51014, 663, 311, 733, 295, 264, 3880, 551, 13, 51064], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 308, "seek": 185600, "start": 1871.0, "end": 1873.0, "text": " And then just queue up my PowerPoint if you wouldn't mind.", "tokens": [51114, 400, 550, 445, 18639, 493, 452, 25584, 498, 291, 2759, 380, 1575, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 309, "seek": 185600, "start": 1874.0, "end": 1875.0, "text": " Okay, let me move this one first.", "tokens": [51264, 1033, 11, 718, 385, 1286, 341, 472, 700, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 310, "seek": 185600, "start": 1877.0, "end": 1879.0, "text": " And then let me see, your PowerPoint is right here.", "tokens": [51414, 400, 550, 718, 385, 536, 11, 428, 25584, 307, 558, 510, 13, 51514], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 311, "seek": 185600, "start": 1880.0, "end": 1885.0, "text": " Yeah, I win the contest for most media presentations.", "tokens": [51564, 865, 11, 286, 1942, 264, 10287, 337, 881, 3021, 18964, 13, 51814], "temperature": 0.0, "avg_logprob": -0.1578387864139102, "compression_ratio": 1.6363636363636365, "no_speech_prob": 0.07119270414113998}, {"id": 312, "seek": 188600, "start": 1886.0, "end": 1887.0, "text": " I love it.", "tokens": [50364, 286, 959, 309, 13, 50414], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 313, "seek": 188600, "start": 1890.0, "end": 1891.0, "text": " Okay, we saw that, we saw that.", "tokens": [50564, 1033, 11, 321, 1866, 300, 11, 321, 1866, 300, 13, 50614], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 314, "seek": 188600, "start": 1892.0, "end": 1893.0, "text": " Okay, so how do we do this?", "tokens": [50664, 1033, 11, 370, 577, 360, 321, 360, 341, 30, 50714], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 315, "seek": 188600, "start": 1894.0, "end": 1898.0, "text": " First of all, do not underestimate, you need some great people to do this work.", "tokens": [50764, 2386, 295, 439, 11, 360, 406, 35826, 11, 291, 643, 512, 869, 561, 281, 360, 341, 589, 13, 50964], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 316, "seek": 188600, "start": 1899.0, "end": 1904.0, "text": " Kinetic Vision has a lot of amazing people, but we're talking about between site machine and Kinetic Vision.", "tokens": [51014, 27950, 3532, 25170, 575, 257, 688, 295, 2243, 561, 11, 457, 321, 434, 1417, 466, 1296, 3621, 3479, 293, 27950, 3532, 25170, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 317, "seek": 188600, "start": 1905.0, "end": 1911.0, "text": " We have data scientists, we have mechanical engineers, we've got machine learning engineers, we've got software engineers, technical artists.", "tokens": [51314, 492, 362, 1412, 7708, 11, 321, 362, 12070, 11955, 11, 321, 600, 658, 3479, 2539, 11955, 11, 321, 600, 658, 4722, 11955, 11, 6191, 6910, 13, 51614], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 318, "seek": 188600, "start": 1912.0, "end": 1913.0, "text": " It's a diverse team.", "tokens": [51664, 467, 311, 257, 9521, 1469, 13, 51714], "temperature": 0.0, "avg_logprob": -0.10711784873689924, "compression_ratio": 1.7295081967213115, "no_speech_prob": 0.0024729708675295115}, {"id": 319, "seek": 191300, "start": 1913.0, "end": 1923.0, "text": " So when you're making decisions about building even just the 3D asset, it's really helpful to have subject matter expertise alongside your tech artists when you're building that asset.", "tokens": [50364, 407, 562, 291, 434, 1455, 5327, 466, 2390, 754, 445, 264, 805, 35, 11999, 11, 309, 311, 534, 4961, 281, 362, 3983, 1871, 11769, 12385, 428, 7553, 6910, 562, 291, 434, 2390, 300, 11999, 13, 50864], "temperature": 0.0, "avg_logprob": -0.08426895508399376, "compression_ratio": 1.6045627376425855, "no_speech_prob": 0.0013479269109666348}, {"id": 320, "seek": 191300, "start": 1924.0, "end": 1927.0, "text": " So just a little bit about, you know, do not forget about the people.", "tokens": [50914, 407, 445, 257, 707, 857, 466, 11, 291, 458, 11, 360, 406, 2870, 466, 264, 561, 13, 51064], "temperature": 0.0, "avg_logprob": -0.08426895508399376, "compression_ratio": 1.6045627376425855, "no_speech_prob": 0.0013479269109666348}, {"id": 321, "seek": 191300, "start": 1928.0, "end": 1933.0, "text": " AI is amazing, but we, at least for probably the next five years, we're still going to need people.", "tokens": [51114, 7318, 307, 2243, 11, 457, 321, 11, 412, 1935, 337, 1391, 264, 958, 1732, 924, 11, 321, 434, 920, 516, 281, 643, 561, 13, 51364], "temperature": 0.0, "avg_logprob": -0.08426895508399376, "compression_ratio": 1.6045627376425855, "no_speech_prob": 0.0013479269109666348}, {"id": 322, "seek": 191300, "start": 1935.0, "end": 1940.0, "text": " Okay, little nuts and bolts on like, what do you use to go do this?", "tokens": [51464, 1033, 11, 707, 10483, 293, 18127, 322, 411, 11, 437, 360, 291, 764, 281, 352, 360, 341, 30, 51714], "temperature": 0.0, "avg_logprob": -0.08426895508399376, "compression_ratio": 1.6045627376425855, "no_speech_prob": 0.0013479269109666348}, {"id": 323, "seek": 194000, "start": 1941.0, "end": 1944.0, "text": " We're, what we're doing, I'll look at the end here really quickly.", "tokens": [50414, 492, 434, 11, 437, 321, 434, 884, 11, 286, 603, 574, 412, 264, 917, 510, 534, 2661, 13, 50564], "temperature": 0.0, "avg_logprob": -0.11452148982456752, "compression_ratio": 1.6804979253112033, "no_speech_prob": 0.0035068877041339874}, {"id": 324, "seek": 194000, "start": 1945.0, "end": 1956.0, "text": " We're, you know, we're publishing a kit app, you know, kit app USD that's streaming within an omniverse, like visual context, and that's part of the site machine application.", "tokens": [50614, 492, 434, 11, 291, 458, 11, 321, 434, 17832, 257, 8260, 724, 11, 291, 458, 11, 8260, 724, 24375, 300, 311, 11791, 1951, 364, 36874, 5376, 11, 411, 5056, 4319, 11, 293, 300, 311, 644, 295, 264, 3621, 3479, 3861, 13, 51164], "temperature": 0.0, "avg_logprob": -0.11452148982456752, "compression_ratio": 1.6804979253112033, "no_speech_prob": 0.0035068877041339874}, {"id": 325, "seek": 194000, "start": 1957.0, "end": 1965.0, "text": " And so what we're doing is we're delivering a USD asset that can deliver that high fidelity, fully realistic, interactive view.", "tokens": [51214, 400, 370, 437, 321, 434, 884, 307, 321, 434, 14666, 257, 24375, 11999, 300, 393, 4239, 300, 1090, 46404, 11, 4498, 12465, 11, 15141, 1910, 13, 51614], "temperature": 0.0, "avg_logprob": -0.11452148982456752, "compression_ratio": 1.6804979253112033, "no_speech_prob": 0.0035068877041339874}, {"id": 326, "seek": 194000, "start": 1966.0, "end": 1967.0, "text": " What we start with is this 3D scan.", "tokens": [51664, 708, 321, 722, 365, 307, 341, 805, 35, 11049, 13, 51714], "temperature": 0.0, "avg_logprob": -0.11452148982456752, "compression_ratio": 1.6804979253112033, "no_speech_prob": 0.0035068877041339874}, {"id": 327, "seek": 196700, "start": 1967.0, "end": 1969.0, "text": " There's a lot of choices here, everybody.", "tokens": [50364, 821, 311, 257, 688, 295, 7994, 510, 11, 2201, 13, 50464], "temperature": 0.0, "avg_logprob": -0.11951034380042035, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.01682318188250065}, {"id": 328, "seek": 196700, "start": 1970.0, "end": 1972.0, "text": " We happen to use Naviz, Faro and Leica.", "tokens": [50514, 492, 1051, 281, 764, 9219, 590, 11, 9067, 78, 293, 1456, 2262, 13, 50614], "temperature": 0.0, "avg_logprob": -0.11951034380042035, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.01682318188250065}, {"id": 329, "seek": 196700, "start": 1973.0, "end": 1976.0, "text": " Naviz scanners are very fast, down to five millimeter accuracy.", "tokens": [50664, 9219, 590, 795, 25792, 366, 588, 2370, 11, 760, 281, 1732, 17942, 14170, 13, 50814], "temperature": 0.0, "avg_logprob": -0.11951034380042035, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.01682318188250065}, {"id": 330, "seek": 196700, "start": 1977.0, "end": 1979.0, "text": " You're doing a lidar scan, capturing millions of points as you go.", "tokens": [50864, 509, 434, 884, 257, 10252, 289, 11049, 11, 23384, 6803, 295, 2793, 382, 291, 352, 13, 50964], "temperature": 0.0, "avg_logprob": -0.11951034380042035, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.01682318188250065}, {"id": 331, "seek": 196700, "start": 1980.0, "end": 1983.0, "text": " Faro and Leica are more terrestrial scans.", "tokens": [51014, 9067, 78, 293, 1456, 2262, 366, 544, 1796, 34539, 35116, 13, 51164], "temperature": 0.0, "avg_logprob": -0.11951034380042035, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.01682318188250065}, {"id": 332, "seek": 196700, "start": 1984.0, "end": 1990.0, "text": " And so you're setting your, you know, you're setting your tripods and you're capturing data, but they're much more accurate and you get a lot of higher fidelity.", "tokens": [51214, 400, 370, 291, 434, 3287, 428, 11, 291, 458, 11, 291, 434, 3287, 428, 4931, 19768, 293, 291, 434, 23384, 1412, 11, 457, 436, 434, 709, 544, 8559, 293, 291, 483, 257, 688, 295, 2946, 46404, 13, 51514], "temperature": 0.0, "avg_logprob": -0.11951034380042035, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.01682318188250065}, {"id": 333, "seek": 199000, "start": 1990.0, "end": 1994.0, "text": " So we typically use Naviz for scanning a full site.", "tokens": [50364, 407, 321, 5850, 764, 9219, 590, 337, 27019, 257, 1577, 3621, 13, 50564], "temperature": 0.0, "avg_logprob": -0.0963589911367379, "compression_ratio": 1.6865671641791045, "no_speech_prob": 0.06686127185821533}, {"id": 334, "seek": 199000, "start": 1995.0, "end": 2007.0, "text": " And then if we have particular machines where you really need really accurate user interface details and accurate, like mechanical details, we'll go in and re scan with like a Faro or Leica to get those high details.", "tokens": [50614, 400, 550, 498, 321, 362, 1729, 8379, 689, 291, 534, 643, 534, 8559, 4195, 9226, 4365, 293, 8559, 11, 411, 12070, 4365, 11, 321, 603, 352, 294, 293, 319, 11049, 365, 411, 257, 9067, 78, 420, 1456, 2262, 281, 483, 729, 1090, 4365, 13, 51214], "temperature": 0.0, "avg_logprob": -0.0963589911367379, "compression_ratio": 1.6865671641791045, "no_speech_prob": 0.06686127185821533}, {"id": 335, "seek": 199000, "start": 2008.0, "end": 2014.0, "text": " From a reality capture perspective, we're taking those scans and we're activating them with the real simple off the shelf software that we integrate.", "tokens": [51264, 3358, 257, 4103, 7983, 4585, 11, 321, 434, 1940, 729, 35116, 293, 321, 434, 42481, 552, 365, 264, 957, 2199, 766, 264, 15222, 4722, 300, 321, 13365, 13, 51564], "temperature": 0.0, "avg_logprob": -0.0963589911367379, "compression_ratio": 1.6865671641791045, "no_speech_prob": 0.06686127185821533}, {"id": 336, "seek": 199000, "start": 2015.0, "end": 2017.0, "text": " You have a few choices there too.", "tokens": [51614, 509, 362, 257, 1326, 7994, 456, 886, 13, 51714], "temperature": 0.0, "avg_logprob": -0.0963589911367379, "compression_ratio": 1.6865671641791045, "no_speech_prob": 0.06686127185821533}, {"id": 337, "seek": 201700, "start": 2017.0, "end": 2019.0, "text": " There's a big ecosystem out there.", "tokens": [50364, 821, 311, 257, 955, 11311, 484, 456, 13, 50464], "temperature": 0.0, "avg_logprob": -0.12155405365594543, "compression_ratio": 1.6270491803278688, "no_speech_prob": 0.0016346558695659041}, {"id": 338, "seek": 201700, "start": 2021.0, "end": 2024.0, "text": " We use preview 3D here at Kinetic Vision.", "tokens": [50564, 492, 764, 14281, 805, 35, 510, 412, 27950, 3532, 25170, 13, 50714], "temperature": 0.0, "avg_logprob": -0.12155405365594543, "compression_ratio": 1.6270491803278688, "no_speech_prob": 0.0016346558695659041}, {"id": 339, "seek": 201700, "start": 2025.0, "end": 2029.0, "text": " And let's see, we also use Reality Cloud Studio.", "tokens": [50764, 400, 718, 311, 536, 11, 321, 611, 764, 33822, 8061, 13500, 13, 50964], "temperature": 0.0, "avg_logprob": -0.12155405365594543, "compression_ratio": 1.6270491803278688, "no_speech_prob": 0.0016346558695659041}, {"id": 340, "seek": 201700, "start": 2030.0, "end": 2032.0, "text": " So these are two great programs.", "tokens": [51014, 407, 613, 366, 732, 869, 4268, 13, 51114], "temperature": 0.0, "avg_logprob": -0.12155405365594543, "compression_ratio": 1.6270491803278688, "no_speech_prob": 0.0016346558695659041}, {"id": 341, "seek": 201700, "start": 2033.0, "end": 2042.0, "text": " There's a handful of them out there, but what's nice, we work with mostly big companies and a lot of them prefer to procure software instead of using something open source, something that's supported.", "tokens": [51164, 821, 311, 257, 16458, 295, 552, 484, 456, 11, 457, 437, 311, 1481, 11, 321, 589, 365, 5240, 955, 3431, 293, 257, 688, 295, 552, 4382, 281, 26846, 4722, 2602, 295, 1228, 746, 1269, 4009, 11, 746, 300, 311, 8104, 13, 51614], "temperature": 0.0, "avg_logprob": -0.12155405365594543, "compression_ratio": 1.6270491803278688, "no_speech_prob": 0.0016346558695659041}, {"id": 342, "seek": 201700, "start": 2043.0, "end": 2044.0, "text": " But there's open source options also.", "tokens": [51664, 583, 456, 311, 1269, 4009, 3956, 611, 13, 51714], "temperature": 0.0, "avg_logprob": -0.12155405365594543, "compression_ratio": 1.6270491803278688, "no_speech_prob": 0.0016346558695659041}, {"id": 343, "seek": 204400, "start": 2045.0, "end": 2055.0, "text": " Then, you know, once we get that reality capture data, which includes really high resolution, panographic images, sometimes we're creating a mesh that's got materials applied to it.", "tokens": [50414, 1396, 11, 291, 458, 11, 1564, 321, 483, 300, 4103, 7983, 1412, 11, 597, 5974, 534, 1090, 8669, 11, 2462, 12295, 5267, 11, 2171, 321, 434, 4084, 257, 17407, 300, 311, 658, 5319, 6456, 281, 309, 13, 50914], "temperature": 0.0, "avg_logprob": -0.10206889091654027, "compression_ratio": 1.5228215767634854, "no_speech_prob": 0.003271730849519372}, {"id": 344, "seek": 204400, "start": 2056.0, "end": 2060.0, "text": " We then pull that into a 3D digital content creation package.", "tokens": [50964, 492, 550, 2235, 300, 666, 257, 805, 35, 4562, 2701, 8016, 7372, 13, 51164], "temperature": 0.0, "avg_logprob": -0.10206889091654027, "compression_ratio": 1.5228215767634854, "no_speech_prob": 0.003271730849519372}, {"id": 345, "seek": 204400, "start": 2061.0, "end": 2064.0, "text": " Pick your package, 3D Studio Max, Blender, Maya.", "tokens": [51214, 14129, 428, 7372, 11, 805, 35, 13500, 7402, 11, 2177, 3216, 11, 21695, 13, 51364], "temperature": 0.0, "avg_logprob": -0.10206889091654027, "compression_ratio": 1.5228215767634854, "no_speech_prob": 0.003271730849519372}, {"id": 346, "seek": 204400, "start": 2065.0, "end": 2066.0, "text": " There's a lot of great choices there.", "tokens": [51414, 821, 311, 257, 688, 295, 869, 7994, 456, 13, 51464], "temperature": 0.0, "avg_logprob": -0.10206889091654027, "compression_ratio": 1.5228215767634854, "no_speech_prob": 0.003271730849519372}, {"id": 347, "seek": 204400, "start": 2067.0, "end": 2069.0, "text": " We happen to use all three of these.", "tokens": [51514, 492, 1051, 281, 764, 439, 1045, 295, 613, 13, 51614], "temperature": 0.0, "avg_logprob": -0.10206889091654027, "compression_ratio": 1.5228215767634854, "no_speech_prob": 0.003271730849519372}, {"id": 348, "seek": 206900, "start": 2069.0, "end": 2072.0, "text": " And then you're following traditional artist workflows.", "tokens": [50364, 400, 550, 291, 434, 3480, 5164, 5748, 43461, 13, 50514], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 349, "seek": 206900, "start": 2073.0, "end": 2074.0, "text": " You're either doing direct modeling.", "tokens": [50564, 509, 434, 2139, 884, 2047, 15983, 13, 50614], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 350, "seek": 206900, "start": 2075.0, "end": 2077.0, "text": " You're using a model library and maybe bringing a model in.", "tokens": [50664, 509, 434, 1228, 257, 2316, 6405, 293, 1310, 5062, 257, 2316, 294, 13, 50764], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 351, "seek": 206900, "start": 2078.0, "end": 2081.0, "text": " You're referencing those scans for the geometry sizes.", "tokens": [50814, 509, 434, 40582, 729, 35116, 337, 264, 18426, 11602, 13, 50964], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 352, "seek": 206900, "start": 2082.0, "end": 2084.0, "text": " You're perhaps re-topologizing some geometry.", "tokens": [51014, 509, 434, 4317, 319, 12, 19337, 1132, 3319, 512, 18426, 13, 51114], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 353, "seek": 206900, "start": 2085.0, "end": 2087.0, "text": " And then you're building your assets in 3D Studio Max.", "tokens": [51164, 400, 550, 291, 434, 2390, 428, 9769, 294, 805, 35, 13500, 7402, 13, 51264], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 354, "seek": 206900, "start": 2088.0, "end": 2089.0, "text": " I'll put a footnote here.", "tokens": [51314, 286, 603, 829, 257, 2671, 22178, 510, 13, 51364], "temperature": 0.0, "avg_logprob": -0.09890138691869275, "compression_ratio": 1.6135265700483092, "no_speech_prob": 0.006219005677849054}, {"id": 355, "seek": 208900, "start": 2090.0, "end": 2098.0, "text": " There's a lot of exciting, exciting technology around the generative creation of these assets.", "tokens": [50414, 821, 311, 257, 688, 295, 4670, 11, 4670, 2899, 926, 264, 1337, 1166, 8016, 295, 613, 9769, 13, 50814], "temperature": 0.0, "avg_logprob": -0.08595567682515019, "compression_ratio": 1.6265060240963856, "no_speech_prob": 0.19620494544506073}, {"id": 356, "seek": 208900, "start": 2099.0, "end": 2105.0, "text": " NVIDIA's got some great open source libraries out there that they're publishing with their research teams.", "tokens": [50864, 426, 3958, 6914, 311, 658, 512, 869, 1269, 4009, 15148, 484, 456, 300, 436, 434, 17832, 365, 641, 2132, 5491, 13, 51164], "temperature": 0.0, "avg_logprob": -0.08595567682515019, "compression_ratio": 1.6265060240963856, "no_speech_prob": 0.19620494544506073}, {"id": 357, "seek": 208900, "start": 2106.0, "end": 2107.0, "text": " And, you know, they're worth checking out.", "tokens": [51214, 400, 11, 291, 458, 11, 436, 434, 3163, 8568, 484, 13, 51264], "temperature": 0.0, "avg_logprob": -0.08595567682515019, "compression_ratio": 1.6265060240963856, "no_speech_prob": 0.19620494544506073}, {"id": 358, "seek": 208900, "start": 2108.0, "end": 2118.0, "text": " We're not fully using them in these workflows yet, but there's going to be a whole slew of software packages and workflows available around using generative AI.", "tokens": [51314, 492, 434, 406, 4498, 1228, 552, 294, 613, 43461, 1939, 11, 457, 456, 311, 516, 281, 312, 257, 1379, 2426, 86, 295, 4722, 17401, 293, 43461, 2435, 926, 1228, 1337, 1166, 7318, 13, 51814], "temperature": 0.0, "avg_logprob": -0.08595567682515019, "compression_ratio": 1.6265060240963856, "no_speech_prob": 0.19620494544506073}, {"id": 359, "seek": 211900, "start": 2119.0, "end": 2120.0, "text": " So, you know, we're going to be using them for 3D.", "tokens": [50364, 407, 11, 291, 458, 11, 321, 434, 516, 281, 312, 1228, 552, 337, 805, 35, 13, 50414], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 360, "seek": 211900, "start": 2121.0, "end": 2124.0, "text": " And then, you know, once we have that 3D Studio Max or Blender asset,", "tokens": [50464, 400, 550, 11, 291, 458, 11, 1564, 321, 362, 300, 805, 35, 13500, 7402, 420, 2177, 3216, 11999, 11, 50614], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 361, "seek": 211900, "start": 2125.0, "end": 2129.0, "text": " in order to access it programmatically within Sight Machine,", "tokens": [50664, 294, 1668, 281, 2105, 309, 37648, 5030, 1951, 318, 397, 22155, 11, 50864], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 362, "seek": 211900, "start": 2130.0, "end": 2132.0, "text": " we're, you know, grouping portions of that USB file.", "tokens": [50914, 321, 434, 11, 291, 458, 11, 40149, 25070, 295, 300, 10109, 3991, 13, 51014], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 363, "seek": 211900, "start": 2133.0, "end": 2135.0, "text": " We're making them available so they're triggered by a sensor.", "tokens": [51064, 492, 434, 1455, 552, 2435, 370, 436, 434, 21710, 538, 257, 10200, 13, 51164], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 364, "seek": 211900, "start": 2136.0, "end": 2137.0, "text": " We're setting camera views.", "tokens": [51214, 492, 434, 3287, 2799, 6809, 13, 51264], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 365, "seek": 211900, "start": 2138.0, "end": 2142.0, "text": " And so what we're doing that in is just a little application that we built called Data Vision.", "tokens": [51314, 400, 370, 437, 321, 434, 884, 300, 294, 307, 445, 257, 707, 3861, 300, 321, 3094, 1219, 11888, 25170, 13, 51514], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 366, "seek": 211900, "start": 2143.0, "end": 2144.0, "text": " It's built on the Omniverse SDK.", "tokens": [51564, 467, 311, 3094, 322, 264, 9757, 77, 5376, 37135, 13, 51614], "temperature": 0.0, "avg_logprob": -0.22480972169891117, "compression_ratio": 1.6258992805755397, "no_speech_prob": 0.0028209013398736715}, {"id": 367, "seek": 214400, "start": 2144.0, "end": 2150.0, "text": " And it's really using those great resources from the Omniverse SDK to build 3D application.", "tokens": [50364, 400, 309, 311, 534, 1228, 729, 869, 3593, 490, 264, 9757, 77, 5376, 37135, 281, 1322, 805, 35, 3861, 13, 50664], "temperature": 0.0, "avg_logprob": -0.07831478118896484, "compression_ratio": 1.5387755102040817, "no_speech_prob": 0.007690812461078167}, {"id": 368, "seek": 214400, "start": 2151.0, "end": 2155.0, "text": " This allows us to layer in some extra data that Sight Machine needs to hook up to their platform.", "tokens": [50714, 639, 4045, 505, 281, 4583, 294, 512, 2857, 1412, 300, 318, 397, 22155, 2203, 281, 6328, 493, 281, 641, 3663, 13, 50914], "temperature": 0.0, "avg_logprob": -0.07831478118896484, "compression_ratio": 1.5387755102040817, "no_speech_prob": 0.007690812461078167}, {"id": 369, "seek": 214400, "start": 2156.0, "end": 2158.0, "text": " So, and that's most of it.", "tokens": [50964, 407, 11, 293, 300, 311, 881, 295, 309, 13, 51064], "temperature": 0.0, "avg_logprob": -0.07831478118896484, "compression_ratio": 1.5387755102040817, "no_speech_prob": 0.007690812461078167}, {"id": 370, "seek": 214400, "start": 2159.0, "end": 2161.0, "text": " I'm sure there'll be some questions, but just to cap it off,", "tokens": [51114, 286, 478, 988, 456, 603, 312, 512, 1651, 11, 457, 445, 281, 1410, 309, 766, 11, 51214], "temperature": 0.0, "avg_logprob": -0.07831478118896484, "compression_ratio": 1.5387755102040817, "no_speech_prob": 0.007690812461078167}, {"id": 371, "seek": 214400, "start": 2162.0, "end": 2168.0, "text": " this is just one of an asset from one of our pieces, one of our recent projects with Sight Machine,", "tokens": [51264, 341, 307, 445, 472, 295, 364, 11999, 490, 472, 295, 527, 3755, 11, 472, 295, 527, 5162, 4455, 365, 318, 397, 22155, 11, 51564], "temperature": 0.0, "avg_logprob": -0.07831478118896484, "compression_ratio": 1.5387755102040817, "no_speech_prob": 0.007690812461078167}, {"id": 372, "seek": 216800, "start": 2168.0, "end": 2175.0, "text": " just showing these steps, going from 3D Point Cloud to Reality Capture Asset to Assimilation Asset", "tokens": [50364, 445, 4099, 613, 4439, 11, 516, 490, 805, 35, 12387, 8061, 281, 33822, 9480, 540, 1018, 3854, 281, 40376, 16067, 1018, 3854, 50714], "temperature": 0.0, "avg_logprob": -0.1644353012540447, "compression_ratio": 1.6159169550173011, "no_speech_prob": 0.04127166047692299}, {"id": 373, "seek": 216800, "start": 2176.0, "end": 2182.0, "text": " to a really beautiful photorealistic asset with animation done through the Omniverse SDK.", "tokens": [50764, 281, 257, 534, 2238, 2409, 418, 304, 3142, 11999, 365, 9603, 1096, 807, 264, 9757, 77, 5376, 37135, 13, 51064], "temperature": 0.0, "avg_logprob": -0.1644353012540447, "compression_ratio": 1.6159169550173011, "no_speech_prob": 0.04127166047692299}, {"id": 374, "seek": 216800, "start": 2183.0, "end": 2185.0, "text": " And that's what I got for you, people.", "tokens": [51114, 400, 300, 311, 437, 286, 658, 337, 291, 11, 561, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1644353012540447, "compression_ratio": 1.6159169550173011, "no_speech_prob": 0.04127166047692299}, {"id": 375, "seek": 216800, "start": 2186.0, "end": 2189.0, "text": " That was really cool. Show me. I didn't see that before. That was really amazing.", "tokens": [51264, 663, 390, 534, 1627, 13, 6895, 385, 13, 286, 994, 380, 536, 300, 949, 13, 663, 390, 534, 2243, 13, 51414], "temperature": 0.0, "avg_logprob": -0.1644353012540447, "compression_ratio": 1.6159169550173011, "no_speech_prob": 0.04127166047692299}, {"id": 376, "seek": 216800, "start": 2190.0, "end": 2193.0, "text": " I know. <PERSON><PERSON><PERSON>, you're always, yeah. <PERSON><PERSON><PERSON> is like, he's like, you never show me anything.", "tokens": [51464, 286, 458, 13, 460, 12779, 71, 11, 291, 434, 1009, 11, 1338, 13, 460, 12779, 71, 307, 411, 11, 415, 311, 411, 11, 291, 1128, 855, 385, 1340, 13, 51614], "temperature": 0.0, "avg_logprob": -0.1644353012540447, "compression_ratio": 1.6159169550173011, "no_speech_prob": 0.04127166047692299}, {"id": 377, "seek": 216800, "start": 2194.0, "end": 2196.0, "text": " Like, I just got to see the latest stuff. So, yeah, there you go.", "tokens": [51664, 1743, 11, 286, 445, 658, 281, 536, 264, 6792, 1507, 13, 407, 11, 1338, 11, 456, 291, 352, 13, 51764], "temperature": 0.0, "avg_logprob": -0.1644353012540447, "compression_ratio": 1.6159169550173011, "no_speech_prob": 0.04127166047692299}, {"id": 378, "seek": 219600, "start": 2197.0, "end": 2203.0, "text": " I love it. That is so wild. I think, yeah, it's a lot of impressed people watching in the chat as well.", "tokens": [50414, 286, 959, 309, 13, 663, 307, 370, 4868, 13, 286, 519, 11, 1338, 11, 309, 311, 257, 688, 295, 11679, 561, 1976, 294, 264, 5081, 382, 731, 13, 50714], "temperature": 0.0, "avg_logprob": -0.16693787207970254, "compression_ratio": 1.5649350649350648, "no_speech_prob": 0.004392748232930899}, {"id": 379, "seek": 219600, "start": 2204.0, "end": 2209.0, "text": " Very cool. Let me see. And I think I'm going to leave this on for me for one second.", "tokens": [50764, 4372, 1627, 13, 961, 385, 536, 13, 400, 286, 519, 286, 478, 516, 281, 1856, 341, 322, 337, 385, 337, 472, 1150, 13, 51014], "temperature": 0.0, "avg_logprob": -0.16693787207970254, "compression_ratio": 1.5649350649350648, "no_speech_prob": 0.004392748232930899}, {"id": 380, "seek": 219600, "start": 2210.0, "end": 2215.0, "text": " And there we go. Okay, cool. That's wild. So, anyone who's just joining us, thank you.", "tokens": [51064, 400, 456, 321, 352, 13, 1033, 11, 1627, 13, 663, 311, 4868, 13, 407, 11, 2878, 567, 311, 445, 5549, 505, 11, 1309, 291, 13, 51314], "temperature": 0.0, "avg_logprob": -0.16693787207970254, "compression_ratio": 1.5649350649350648, "no_speech_prob": 0.004392748232930899}, {"id": 381, "seek": 219600, "start": 2216.0, "end": 2218.0, "text": " Welcome to the stream. We're talking with <PERSON><PERSON> Machine, Microsoft Kinetic Vision,", "tokens": [51364, 4027, 281, 264, 4309, 13, 492, 434, 1417, 365, 318, 397, 22155, 11, 8116, 27950, 3532, 25170, 11, 51464], "temperature": 0.0, "avg_logprob": -0.16693787207970254, "compression_ratio": 1.5649350649350648, "no_speech_prob": 0.004392748232930899}, {"id": 382, "seek": 219600, "start": 2219.0, "end": 2225.0, "text": " and of course, folks from the NVIDIA team about how Agentec AI and digital twins are transforming manufacturing operations.", "tokens": [51514, 293, 295, 1164, 11, 4024, 490, 264, 426, 3958, 6914, 1469, 466, 577, 2725, 1576, 66, 7318, 293, 4562, 22555, 366, 27210, 11096, 7705, 13, 51814], "temperature": 0.0, "avg_logprob": -0.16693787207970254, "compression_ratio": 1.5649350649350648, "no_speech_prob": 0.004392748232930899}, {"id": 383, "seek": 222600, "start": 2226.0, "end": 2229.0, "text": " Be sure to stay active in the chat. We see a lot of questions and comments coming through.", "tokens": [50364, 879, 988, 281, 1754, 4967, 294, 264, 5081, 13, 492, 536, 257, 688, 295, 1651, 293, 3053, 1348, 807, 13, 50514], "temperature": 0.0, "avg_logprob": -0.10822886070318982, "compression_ratio": 1.5543478260869565, "no_speech_prob": 0.0016088496195152402}, {"id": 384, "seek": 222600, "start": 2230.0, "end": 2236.0, "text": " We'll try to address those. But that was fantastic. Thank you so much for carrying us through that really nice journey, <PERSON>.", "tokens": [50564, 492, 603, 853, 281, 2985, 729, 13, 583, 300, 390, 5456, 13, 1044, 291, 370, 709, 337, 9792, 505, 807, 300, 534, 1481, 4671, 11, 17809, 13, 50864], "temperature": 0.0, "avg_logprob": -0.10822886070318982, "compression_ratio": 1.5543478260869565, "no_speech_prob": 0.0016088496195152402}, {"id": 385, "seek": 222600, "start": 2238.0, "end": 2239.0, "text": " You are welcome.", "tokens": [50964, 509, 366, 2928, 13, 51014], "temperature": 0.0, "avg_logprob": -0.10822886070318982, "compression_ratio": 1.5543478260869565, "no_speech_prob": 0.0016088496195152402}, {"id": 386, "seek": 222600, "start": 2240.0, "end": 2245.0, "text": " Okay. All right. So, of course, now that we've talked about scanning and creating USD,", "tokens": [51064, 1033, 13, 1057, 558, 13, 407, 11, 295, 1164, 11, 586, 300, 321, 600, 2825, 466, 27019, 293, 4084, 24375, 11, 51314], "temperature": 0.0, "avg_logprob": -0.10822886070318982, "compression_ratio": 1.5543478260869565, "no_speech_prob": 0.0016088496195152402}, {"id": 387, "seek": 222600, "start": 2246.0, "end": 2251.0, "text": " I think we have our friend, <PERSON><PERSON><PERSON> here, who's going to bring us into, to bring us home here, so to speak.", "tokens": [51364, 286, 519, 321, 362, 527, 1277, 11, 12323, 24118, 510, 11, 567, 311, 516, 281, 1565, 505, 666, 11, 281, 1565, 505, 1280, 510, 11, 370, 281, 1710, 13, 51614], "temperature": 0.0, "avg_logprob": -0.10822886070318982, "compression_ratio": 1.5543478260869565, "no_speech_prob": 0.0016088496195152402}, {"id": 388, "seek": 225100, "start": 2252.0, "end": 2256.0, "text": " All right. Can you share my screen?", "tokens": [50414, 1057, 558, 13, 1664, 291, 2073, 452, 2568, 30, 50614], "temperature": 0.0, "avg_logprob": -0.16994516666118914, "compression_ratio": 1.427807486631016, "no_speech_prob": 0.0036892532370984554}, {"id": 389, "seek": 225100, "start": 2257.0, "end": 2260.0, "text": " Yes, let me see if I can. Yeah, I think I got it right here.", "tokens": [50664, 1079, 11, 718, 385, 536, 498, 286, 393, 13, 865, 11, 286, 519, 286, 658, 309, 558, 510, 13, 50814], "temperature": 0.0, "avg_logprob": -0.16994516666118914, "compression_ratio": 1.427807486631016, "no_speech_prob": 0.0036892532370984554}, {"id": 390, "seek": 225100, "start": 2264.0, "end": 2265.0, "text": " Okay, we can see it.", "tokens": [51014, 1033, 11, 321, 393, 536, 309, 13, 51064], "temperature": 0.0, "avg_logprob": -0.16994516666118914, "compression_ratio": 1.427807486631016, "no_speech_prob": 0.0036892532370984554}, {"id": 391, "seek": 225100, "start": 2266.0, "end": 2267.0, "text": " Awesome. Awesome.", "tokens": [51114, 10391, 13, 10391, 13, 51164], "temperature": 0.0, "avg_logprob": -0.16994516666118914, "compression_ratio": 1.427807486631016, "no_speech_prob": 0.0036892532370984554}, {"id": 392, "seek": 225100, "start": 2268.0, "end": 2278.0, "text": " Thanks, everyone. Super exciting. So you guys saw how <PERSON><PERSON> and <PERSON> presented the reference architecture that was done at Ignite.", "tokens": [51214, 2561, 11, 1518, 13, 4548, 4670, 13, 407, 291, 1074, 1866, 577, 1160, 15612, 293, 8510, 8212, 264, 6408, 9482, 300, 390, 1096, 412, 24754, 642, 13, 51714], "temperature": 0.0, "avg_logprob": -0.16994516666118914, "compression_ratio": 1.427807486631016, "no_speech_prob": 0.0036892532370984554}, {"id": 393, "seek": 227800, "start": 2278.0, "end": 2287.0, "text": " Super cool, right? <PERSON><PERSON><PERSON> presented the use case that we are talking to customers about and how we are showing value with this ecosystem.", "tokens": [50364, 4548, 1627, 11, 558, 30, 1160, 1573, 8212, 264, 764, 1389, 300, 321, 366, 1417, 281, 4581, 466, 293, 577, 321, 366, 4099, 2158, 365, 341, 11311, 13, 50814], "temperature": 0.0, "avg_logprob": -0.12212233543395996, "compression_ratio": 1.4751381215469612, "no_speech_prob": 0.007081232964992523}, {"id": 394, "seek": 227800, "start": 2288.0, "end": 2298.0, "text": " And then <PERSON> presented how they take all these scans on the factory and convert them into meaningful USDs that we can then use.", "tokens": [50864, 400, 550, 17809, 8212, 577, 436, 747, 439, 613, 35116, 322, 264, 9265, 293, 7620, 552, 666, 10995, 24375, 82, 300, 321, 393, 550, 764, 13, 51364], "temperature": 0.0, "avg_logprob": -0.12212233543395996, "compression_ratio": 1.4751381215469612, "no_speech_prob": 0.007081232964992523}, {"id": 395, "seek": 229800, "start": 2298.0, "end": 2311.0, "text": " I'm going to just put it together to show you how Sight Machine took all these pieces together and built an architecture that shows value to our customers with all of these pieces put together.", "tokens": [50364, 286, 478, 516, 281, 445, 829, 309, 1214, 281, 855, 291, 577, 318, 397, 22155, 1890, 439, 613, 3755, 1214, 293, 3094, 364, 9482, 300, 3110, 2158, 281, 527, 4581, 365, 439, 295, 613, 3755, 829, 1214, 13, 51014], "temperature": 0.0, "avg_logprob": -0.08360710877638597, "compression_ratio": 1.6758241758241759, "no_speech_prob": 0.04887007176876068}, {"id": 396, "seek": 229800, "start": 2312.0, "end": 2320.0, "text": " So here's the technical architecture diagram. It's a flavor of the reference architecture that you saw earlier.", "tokens": [51064, 407, 510, 311, 264, 6191, 9482, 10686, 13, 467, 311, 257, 6813, 295, 264, 6408, 9482, 300, 291, 1866, 3071, 13, 51464], "temperature": 0.0, "avg_logprob": -0.08360710877638597, "compression_ratio": 1.6758241758241759, "no_speech_prob": 0.04887007176876068}, {"id": 397, "seek": 232000, "start": 2320.0, "end": 2330.0, "text": " I'm going to highlight some of the changes we did or how we added on our technologies to make this even more compelling for our job customers.", "tokens": [50364, 286, 478, 516, 281, 5078, 512, 295, 264, 2962, 321, 630, 420, 577, 321, 3869, 322, 527, 7943, 281, 652, 341, 754, 544, 20050, 337, 527, 1691, 4581, 13, 50864], "temperature": 0.0, "avg_logprob": -0.12234324839577745, "compression_ratio": 1.482233502538071, "no_speech_prob": 0.024752210825681686}, {"id": 398, "seek": 232000, "start": 2331.0, "end": 2341.0, "text": " So first off to recap Sight Machine is a AI manufacturing data platform. We take data from all these data sources that you see on the left hand side.", "tokens": [50914, 407, 700, 766, 281, 20928, 318, 397, 22155, 307, 257, 7318, 11096, 1412, 3663, 13, 492, 747, 1412, 490, 439, 613, 1412, 7139, 300, 291, 536, 322, 264, 1411, 1011, 1252, 13, 51414], "temperature": 0.0, "avg_logprob": -0.12234324839577745, "compression_ratio": 1.482233502538071, "no_speech_prob": 0.024752210825681686}, {"id": 399, "seek": 234100, "start": 2341.0, "end": 2352.0, "text": " We standardize them, convert them into standard data models, thereby enabling things like analytics, general AI, digital twins and so on.", "tokens": [50364, 492, 3832, 1125, 552, 11, 7620, 552, 666, 3832, 1412, 5245, 11, 28281, 23148, 721, 411, 15370, 11, 2674, 7318, 11, 4562, 22555, 293, 370, 322, 13, 50914], "temperature": 0.0, "avg_logprob": -0.19047801559035843, "compression_ratio": 1.5707964601769913, "no_speech_prob": 0.03118167258799076}, {"id": 400, "seek": 234100, "start": 2353.0, "end": 2368.0, "text": " So here you can see the first step going through the steps here is our factory connect application that Sight Machines application that runs on IoT operations as <PERSON><PERSON> was mentioning in the Unable Kubernetes cluster.", "tokens": [50964, 407, 510, 291, 393, 536, 264, 700, 1823, 516, 807, 264, 4439, 510, 307, 527, 9265, 1745, 3861, 300, 318, 397, 12089, 1652, 3861, 300, 6676, 322, 30112, 7705, 382, 1160, 15612, 390, 18315, 294, 264, 1156, 712, 23145, 13630, 13, 51714], "temperature": 0.0, "avg_logprob": -0.19047801559035843, "compression_ratio": 1.5707964601769913, "no_speech_prob": 0.03118167258799076}, {"id": 401, "seek": 236800, "start": 2368.0, "end": 2372.0, "text": " This gets all the data and passes it on to the next step.", "tokens": [50364, 639, 2170, 439, 264, 1412, 293, 11335, 309, 322, 281, 264, 958, 1823, 13, 50564], "temperature": 0.0, "avg_logprob": -0.08766744754932544, "compression_ratio": 1.6008403361344539, "no_speech_prob": 0.004267227835953236}, {"id": 402, "seek": 236800, "start": 2373.0, "end": 2389.0, "text": " We also have this data powering the factory operate and factory build platforms which are Sight Machines proprietary platforms to process and model the data for use in the kit application as well as for further analysis and AI.", "tokens": [50614, 492, 611, 362, 341, 1412, 1347, 278, 264, 9265, 9651, 293, 9265, 1322, 9473, 597, 366, 318, 397, 12089, 1652, 38992, 9473, 281, 1399, 293, 2316, 264, 1412, 337, 764, 294, 264, 8260, 3861, 382, 731, 382, 337, 3052, 5215, 293, 7318, 13, 51414], "temperature": 0.0, "avg_logprob": -0.08766744754932544, "compression_ratio": 1.6008403361344539, "no_speech_prob": 0.004267227835953236}, {"id": 403, "seek": 236800, "start": 2390.0, "end": 2397.0, "text": " All of this is running on the Microsoft Azure ecosystem to deliver a scalable unified solution.", "tokens": [51464, 1057, 295, 341, 307, 2614, 322, 264, 8116, 11969, 11311, 281, 4239, 257, 38481, 26787, 3827, 13, 51814], "temperature": 0.0, "avg_logprob": -0.08766744754932544, "compression_ratio": 1.6008403361344539, "no_speech_prob": 0.004267227835953236}, {"id": 404, "seek": 239800, "start": 2398.0, "end": 2405.0, "text": " So let's look at each component of it by drilling down into each and see each aspect in more detail.", "tokens": [50364, 407, 718, 311, 574, 412, 1184, 6542, 295, 309, 538, 26290, 760, 666, 1184, 293, 536, 1184, 4171, 294, 544, 2607, 13, 50714], "temperature": 0.0, "avg_logprob": -0.1603663839944979, "compression_ratio": 1.5825242718446602, "no_speech_prob": 0.001053534564562142}, {"id": 405, "seek": 239800, "start": 2406.0, "end": 2415.0, "text": " So first off the data ingestion piece, right? So factory connect ingest data. The first step is to ingest data from the edge.", "tokens": [50764, 407, 700, 766, 264, 1412, 3957, 31342, 2522, 11, 558, 30, 407, 9265, 1745, 3957, 377, 1412, 13, 440, 700, 1823, 307, 281, 3957, 377, 1412, 490, 264, 4691, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1603663839944979, "compression_ratio": 1.5825242718446602, "no_speech_prob": 0.001053534564562142}, {"id": 406, "seek": 239800, "start": 2416.0, "end": 2421.0, "text": " We are able to ingest data from a variety of manufacturing data sources like PLCs, historians, etc.", "tokens": [51264, 492, 366, 1075, 281, 3957, 377, 1412, 490, 257, 5673, 295, 11096, 1412, 7139, 411, 6999, 33290, 11, 26442, 11, 5183, 13, 51514], "temperature": 0.0, "avg_logprob": -0.1603663839944979, "compression_ratio": 1.5825242718446602, "no_speech_prob": 0.001053534564562142}, {"id": 407, "seek": 242100, "start": 2421.0, "end": 2431.0, "text": " Factory connect problems as we mentioned in the Arc-enabled Kubernetes cluster, which offers an extensible, composable view to represent the line.", "tokens": [50364, 36868, 1745, 2740, 382, 321, 2835, 294, 264, 21727, 12, 268, 8909, 23145, 13630, 11, 597, 7736, 364, 1279, 30633, 11, 10199, 712, 1910, 281, 2906, 264, 1622, 13, 50864], "temperature": 0.0, "avg_logprob": -0.1714402241493339, "compression_ratio": 1.4736842105263157, "no_speech_prob": 0.0024264161475002766}, {"id": 408, "seek": 242100, "start": 2432.0, "end": 2434.0, "text": " So that's the first step.", "tokens": [50914, 407, 300, 311, 264, 700, 1823, 13, 51014], "temperature": 0.0, "avg_logprob": -0.1714402241493339, "compression_ratio": 1.4736842105263157, "no_speech_prob": 0.0024264161475002766}, {"id": 409, "seek": 242100, "start": 2435.0, "end": 2443.0, "text": " The second step is we use IoT operations here that reduces the complexity to build the end-to-end solution.", "tokens": [51064, 440, 1150, 1823, 307, 321, 764, 30112, 7705, 510, 300, 18081, 264, 14024, 281, 1322, 264, 917, 12, 1353, 12, 521, 3827, 13, 51464], "temperature": 0.0, "avg_logprob": -0.1714402241493339, "compression_ratio": 1.4736842105263157, "no_speech_prob": 0.0024264161475002766}, {"id": 410, "seek": 244300, "start": 2443.0, "end": 2450.0, "text": " IoT operations enables us to connect the cloud and edge using bi-directional messaging.", "tokens": [50364, 30112, 7705, 17077, 505, 281, 1745, 264, 4588, 293, 4691, 1228, 3228, 12, 18267, 41048, 21812, 13, 50714], "temperature": 0.0, "avg_logprob": -0.10907926844127143, "compression_ratio": 1.4202127659574468, "no_speech_prob": 0.013487369753420353}, {"id": 411, "seek": 244300, "start": 2451.0, "end": 2461.0, "text": " On the second piece here, we have the 3D scans of the factory. This is what <PERSON> is talking about. These are created. You saw all the details. I'm not going to go into it again.", "tokens": [50764, 1282, 264, 1150, 2522, 510, 11, 321, 362, 264, 805, 35, 35116, 295, 264, 9265, 13, 639, 307, 437, 17809, 307, 1417, 466, 13, 1981, 366, 2942, 13, 509, 1866, 439, 264, 4365, 13, 286, 478, 406, 516, 281, 352, 666, 309, 797, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10907926844127143, "compression_ratio": 1.4202127659574468, "no_speech_prob": 0.013487369753420353}, {"id": 412, "seek": 246100, "start": 2462.0, "end": 2476.0, "text": " These scans are segmented into Assemblies, Machines, Components. All those are in the USD format, which is then loaded into Azure Blob Storage for consumption by us and by NVIDIA on viewers.", "tokens": [50414, 1981, 35116, 366, 9469, 292, 666, 1018, 15750, 24119, 11, 12089, 1652, 11, 6620, 40496, 13, 1057, 729, 366, 294, 264, 24375, 7877, 11, 597, 307, 550, 13210, 666, 11969, 9865, 65, 36308, 337, 12126, 538, 505, 293, 538, 426, 3958, 6914, 322, 8499, 13, 51114], "temperature": 0.0, "avg_logprob": -0.19426347207332004, "compression_ratio": 1.4264705882352942, "no_speech_prob": 0.0841546282172203}, {"id": 413, "seek": 246100, "start": 2477.0, "end": 2484.0, "text": " With this data and leveraging Azure AI services, Site Machine is able to provide effective insights.", "tokens": [51164, 2022, 341, 1412, 293, 32666, 11969, 7318, 3328, 11, 34027, 22155, 307, 1075, 281, 2893, 4942, 14310, 13, 51514], "temperature": 0.0, "avg_logprob": -0.19426347207332004, "compression_ratio": 1.4264705882352942, "no_speech_prob": 0.0841546282172203}, {"id": 414, "seek": 248400, "start": 2485.0, "end": 2498.0, "text": " Next up is the scalable cloud platform. On this cloud, once we have the data transferred from the edge to the cloud, Site Machine is manufacturing data platform.", "tokens": [50414, 3087, 493, 307, 264, 38481, 4588, 3663, 13, 1282, 341, 4588, 11, 1564, 321, 362, 264, 1412, 15809, 490, 264, 4691, 281, 264, 4588, 11, 34027, 22155, 307, 11096, 1412, 3663, 13, 51064], "temperature": 0.0, "avg_logprob": -0.14063065739001257, "compression_ratio": 1.5056179775280898, "no_speech_prob": 0.008662032894790173}, {"id": 415, "seek": 248400, "start": 2499.0, "end": 2506.0, "text": " This powers all the data to factory operate. All this runs seamlessly in Azure cloud and Microsoft Fabric.", "tokens": [51114, 639, 8674, 439, 264, 1412, 281, 9265, 9651, 13, 1057, 341, 6676, 38083, 294, 11969, 4588, 293, 8116, 17440, 1341, 13, 51464], "temperature": 0.0, "avg_logprob": -0.14063065739001257, "compression_ratio": 1.5056179775280898, "no_speech_prob": 0.008662032894790173}, {"id": 416, "seek": 250600, "start": 2507.0, "end": 2515.0, "text": " IoT operations sends data to the cloud via Fabric Azure Event Hubs, where I even streams in Fabric.", "tokens": [50414, 30112, 7705, 14790, 1412, 281, 264, 4588, 5766, 17440, 1341, 11969, 13222, 389, 5432, 11, 689, 286, 754, 15842, 294, 17440, 1341, 13, 50814], "temperature": 0.0, "avg_logprob": -0.11054275074943168, "compression_ratio": 1.4715909090909092, "no_speech_prob": 0.015175306238234043}, {"id": 417, "seek": 250600, "start": 2516.0, "end": 2525.0, "text": " This is where Site Machine is able to process the data and create those standardized data models that I talked about that represent the entire line end-to-end.", "tokens": [50864, 639, 307, 689, 34027, 22155, 307, 1075, 281, 1399, 264, 1412, 293, 1884, 729, 31677, 1412, 5245, 300, 286, 2825, 466, 300, 2906, 264, 2302, 1622, 917, 12, 1353, 12, 521, 13, 51314], "temperature": 0.0, "avg_logprob": -0.11054275074943168, "compression_ratio": 1.4715909090909092, "no_speech_prob": 0.015175306238234043}, {"id": 418, "seek": 252500, "start": 2526.0, "end": 2538.0, "text": " With this data and then leveraging AI services from Azure, Site Machine is able to provide effective insights like agentic recommendations, which we will look at shortly.", "tokens": [50414, 2022, 341, 1412, 293, 550, 32666, 7318, 3328, 490, 11969, 11, 34027, 22155, 307, 1075, 281, 2893, 4942, 14310, 411, 9461, 299, 10434, 11, 597, 321, 486, 574, 412, 13392, 13, 51014], "temperature": 0.0, "avg_logprob": -0.12420948573521205, "compression_ratio": 1.2592592592592593, "no_speech_prob": 0.005537364166229963}, {"id": 419, "seek": 253800, "start": 2539.0, "end": 2550.0, "text": " The third piece here is the omniverse kit extensions. We are leveraging NVIDIA omniverse kit app streaming, obviously.", "tokens": [50414, 440, 2636, 2522, 510, 307, 264, 36874, 5376, 8260, 25129, 13, 492, 366, 32666, 426, 3958, 6914, 36874, 5376, 8260, 724, 11791, 11, 2745, 13, 50964], "temperature": 0.0, "avg_logprob": -0.12093466419284626, "compression_ratio": 1.5260115606936415, "no_speech_prob": 0.0023222246672958136}, {"id": 420, "seek": 253800, "start": 2551.0, "end": 2560.0, "text": " Now that we have data from Site Machine's data platform, we built a couple of kit extensions to integrate with the kit app streaming application.", "tokens": [51014, 823, 300, 321, 362, 1412, 490, 34027, 22155, 311, 1412, 3663, 11, 321, 3094, 257, 1916, 295, 8260, 25129, 281, 13365, 365, 264, 8260, 724, 11791, 3861, 13, 51464], "temperature": 0.0, "avg_logprob": -0.12093466419284626, "compression_ratio": 1.5260115606936415, "no_speech_prob": 0.0023222246672958136}, {"id": 421, "seek": 256000, "start": 2560.0, "end": 2571.0, "text": " The first one as <PERSON><PERSON> was alluding to takes real-time data from the edge as well as the model data from our factory build application to annotate the USD", "tokens": [50364, 440, 700, 472, 382, 1160, 15612, 390, 439, 33703, 281, 2516, 957, 12, 3766, 1412, 490, 264, 4691, 382, 731, 382, 264, 2316, 1412, 490, 527, 9265, 1322, 3861, 281, 25339, 473, 264, 24375, 50914], "temperature": 0.0, "avg_logprob": -0.13669285407433143, "compression_ratio": 1.484375, "no_speech_prob": 0.07544337213039398}, {"id": 422, "seek": 256000, "start": 2572.0, "end": 2580.0, "text": " so that we can get contextualized data for these twins that <PERSON> is so beautifully generated. Example things like filler speed.", "tokens": [50964, 370, 300, 321, 393, 483, 35526, 1602, 1412, 337, 613, 22555, 300, 17809, 307, 370, 16525, 10833, 13, 24755, 781, 721, 411, 34676, 3073, 13, 51364], "temperature": 0.0, "avg_logprob": -0.13669285407433143, "compression_ratio": 1.484375, "no_speech_prob": 0.07544337213039398}, {"id": 423, "seek": 258000, "start": 2581.0, "end": 2591.0, "text": " That gives you the context of each machine, each asset on the line layered on with meaningful data from the Site Machine application.", "tokens": [50414, 663, 2709, 291, 264, 4319, 295, 1184, 3479, 11, 1184, 11999, 322, 264, 1622, 34666, 322, 365, 10995, 1412, 490, 264, 34027, 22155, 3861, 13, 50914], "temperature": 0.0, "avg_logprob": -0.09101798025409827, "compression_ratio": 1.6623376623376624, "no_speech_prob": 0.05088311806321144}, {"id": 424, "seek": 258000, "start": 2592.0, "end": 2599.0, "text": " The second piece is the operate extension. That's the one that handles rendering of this contextual data.", "tokens": [50964, 440, 1150, 2522, 307, 264, 9651, 10320, 13, 663, 311, 264, 472, 300, 18722, 22407, 295, 341, 35526, 1412, 13, 51314], "temperature": 0.0, "avg_logprob": -0.09101798025409827, "compression_ratio": 1.6623376623376624, "no_speech_prob": 0.05088311806321144}, {"id": 425, "seek": 258000, "start": 2600.0, "end": 2608.0, "text": " Example, creating a billboard on top of the machine to show you the name of the filler, the attributes, how it's running, its status, and so on.", "tokens": [51364, 24755, 781, 11, 4084, 257, 2961, 3787, 322, 1192, 295, 264, 3479, 281, 855, 291, 264, 1315, 295, 264, 34676, 11, 264, 17212, 11, 577, 309, 311, 2614, 11, 1080, 6558, 11, 293, 370, 322, 13, 51764], "temperature": 0.0, "avg_logprob": -0.09101798025409827, "compression_ratio": 1.6623376623376624, "no_speech_prob": 0.05088311806321144}, {"id": 426, "seek": 260800, "start": 2609.0, "end": 2618.0, "text": " It also handles things like zooming into a particular asset, events on the UI, showing where a fault is.", "tokens": [50414, 467, 611, 18722, 721, 411, 48226, 666, 257, 1729, 11999, 11, 3931, 322, 264, 15682, 11, 4099, 689, 257, 7441, 307, 13, 50864], "temperature": 0.0, "avg_logprob": -0.1499769566422802, "compression_ratio": 1.4720496894409938, "no_speech_prob": 0.002854862017557025}, {"id": 427, "seek": 260800, "start": 2619.0, "end": 2628.0, "text": " It responds to data changes, like if the filler speed changes, you'll immediately see the change in omniverse, in our UI, and so on.", "tokens": [50914, 467, 27331, 281, 1412, 2962, 11, 411, 498, 264, 34676, 3073, 2962, 11, 291, 603, 4258, 536, 264, 1319, 294, 36874, 5376, 11, 294, 527, 15682, 11, 293, 370, 322, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1499769566422802, "compression_ratio": 1.4720496894409938, "no_speech_prob": 0.002854862017557025}, {"id": 428, "seek": 262800, "start": 2628.0, "end": 2638.0, "text": " We see that in a demo or a short piece. It automatically syncs up. Everything is instantly available on the UI and in omniverse.", "tokens": [50364, 492, 536, 300, 294, 257, 10723, 420, 257, 2099, 2522, 13, 467, 6772, 5451, 14368, 493, 13, 5471, 307, 13518, 2435, 322, 264, 15682, 293, 294, 36874, 5376, 13, 50864], "temperature": 0.0, "avg_logprob": -0.17161324217512802, "compression_ratio": 1.5520833333333333, "no_speech_prob": 0.008507423102855682}, {"id": 429, "seek": 262800, "start": 2639.0, "end": 2646.0, "text": " Events in the UI are in React and it's communicated to the kit application.", "tokens": [50914, 45314, 294, 264, 15682, 366, 294, 30644, 293, 309, 311, 34989, 281, 264, 8260, 3861, 13, 51264], "temperature": 0.0, "avg_logprob": -0.17161324217512802, "compression_ratio": 1.5520833333333333, "no_speech_prob": 0.008507423102855682}, {"id": 430, "seek": 262800, "start": 2647.0, "end": 2654.0, "text": " Now let's look at how the UI piece works. The last piece here is the seamless UI integration.", "tokens": [51314, 823, 718, 311, 574, 412, 577, 264, 15682, 2522, 1985, 13, 440, 1036, 2522, 510, 307, 264, 28677, 15682, 10980, 13, 51664], "temperature": 0.0, "avg_logprob": -0.17161324217512802, "compression_ratio": 1.5520833333333333, "no_speech_prob": 0.008507423102855682}, {"id": 431, "seek": 265400, "start": 2654.0, "end": 2664.0, "text": " On the front end, Factory Operate is a React application. We embed the omniverse viewport into Operate using NVIDIA provided packages.", "tokens": [50364, 1282, 264, 1868, 917, 11, 36868, 12480, 473, 307, 257, 30644, 3861, 13, 492, 12240, 264, 36874, 5376, 1910, 2707, 666, 12480, 473, 1228, 426, 3958, 6914, 5649, 17401, 13, 50864], "temperature": 0.0, "avg_logprob": -0.15920982360839844, "compression_ratio": 1.360248447204969, "no_speech_prob": 0.003746622009202838}, {"id": 432, "seek": 265400, "start": 2665.0, "end": 2671.0, "text": " Every user then connects to a stream from the kit app to show this viewer in the UI.", "tokens": [50914, 2048, 4195, 550, 16967, 281, 257, 4309, 490, 264, 8260, 724, 281, 855, 341, 16767, 294, 264, 15682, 13, 51214], "temperature": 0.0, "avg_logprob": -0.15920982360839844, "compression_ratio": 1.360248447204969, "no_speech_prob": 0.003746622009202838}, {"id": 433, "seek": 267100, "start": 2671.0, "end": 2684.0, "text": " In order to improve efficiency, we've implemented things like stream management to pre-created cash streams, creating the stream pools, showing instant availability in the UI.", "tokens": [50364, 682, 1668, 281, 3470, 10493, 11, 321, 600, 12270, 721, 411, 4309, 4592, 281, 659, 12, 66, 26559, 6388, 15842, 11, 4084, 264, 4309, 28688, 11, 4099, 9836, 17945, 294, 264, 15682, 13, 51014], "temperature": 0.0, "avg_logprob": -0.25933900372735386, "compression_ratio": 1.4166666666666667, "no_speech_prob": 0.016664281487464905}, {"id": 434, "seek": 267100, "start": 2685.0, "end": 2690.0, "text": " Events in the UI are passed to Kit App via WebRTC and Y-Saver.", "tokens": [51064, 45314, 294, 264, 15682, 366, 4678, 281, 23037, 3132, 5766, 9573, 49, 18238, 293, 398, 12, 50, 20655, 13, 51314], "temperature": 0.0, "avg_logprob": -0.25933900372735386, "compression_ratio": 1.4166666666666667, "no_speech_prob": 0.016664281487464905}, {"id": 435, "seek": 269000, "start": 2690.0, "end": 2704.0, "text": " Anything that happens in the Kit application, you can have events reflecting in the UI and things the user interacting or data streaming from the UI can interact real time with the omniverse application.", "tokens": [50364, 11998, 300, 2314, 294, 264, 23037, 3861, 11, 291, 393, 362, 3931, 23543, 294, 264, 15682, 293, 721, 264, 4195, 18017, 420, 1412, 11791, 490, 264, 15682, 393, 4648, 957, 565, 365, 264, 36874, 5376, 3861, 13, 51064], "temperature": 0.0, "avg_logprob": -0.09891967075627024, "compression_ratio": 1.5037037037037038, "no_speech_prob": 0.006237586494535208}, {"id": 436, "seek": 270400, "start": 2705.0, "end": 2719.0, "text": " The whole stream is contextualized to the View and React labels and other annotations in the stream provide a seamless view of the line with recommendations from agent.ai layered on top of it.", "tokens": [50414, 440, 1379, 4309, 307, 35526, 1602, 281, 264, 13909, 293, 30644, 16949, 293, 661, 25339, 763, 294, 264, 4309, 2893, 257, 28677, 1910, 295, 264, 1622, 365, 10434, 490, 9461, 13, 1301, 34666, 322, 1192, 295, 309, 13, 51114], "temperature": 0.0, "avg_logprob": -0.23983797113946143, "compression_ratio": 1.4295774647887325, "no_speech_prob": 0.0433819405734539}, {"id": 437, "seek": 270400, "start": 2722.0, "end": 2724.0, "text": " Very nice.", "tokens": [51264, 4372, 1481, 13, 51364], "temperature": 0.0, "avg_logprob": -0.23983797113946143, "compression_ratio": 1.4295774647887325, "no_speech_prob": 0.0433819405734539}, {"id": 438, "seek": 272400, "start": 2724.0, "end": 2734.0, "text": " Okay, you got it. Okay, cool. That was fantastic. We got a lot of great comments as you were showing your presentation there.", "tokens": [50364, 1033, 11, 291, 658, 309, 13, 1033, 11, 1627, 13, 663, 390, 5456, 13, 492, 658, 257, 688, 295, 869, 3053, 382, 291, 645, 4099, 428, 5860, 456, 13, 50864], "temperature": 0.0, "avg_logprob": -0.2467728665000514, "compression_ratio": 1.5606060606060606, "no_speech_prob": 0.20373494923114777}, {"id": 439, "seek": 272400, "start": 2735.0, "end": 2741.0, "text": " Good questions too. Is there anything you guys want to adjust before we start tackling some of these questions?", "tokens": [50914, 2205, 1651, 886, 13, 1119, 456, 1340, 291, 1074, 528, 281, 4369, 949, 321, 722, 34415, 512, 295, 613, 1651, 30, 51214], "temperature": 0.0, "avg_logprob": -0.2467728665000514, "compression_ratio": 1.5606060606060606, "no_speech_prob": 0.20373494923114777}, {"id": 440, "seek": 272400, "start": 2742.0, "end": 2749.0, "text": " I want to show up and then we can jump into questions. Give me one sec.", "tokens": [51264, 286, 528, 281, 855, 493, 293, 550, 321, 393, 3012, 666, 1651, 13, 5303, 385, 472, 907, 13, 51614], "temperature": 0.0, "avg_logprob": -0.2467728665000514, "compression_ratio": 1.5606060606060606, "no_speech_prob": 0.20373494923114777}, {"id": 441, "seek": 274900, "start": 2750.0, "end": 2757.0, "text": " I see omniverse ambassador <PERSON> from BMW is watching today.", "tokens": [50414, 286, 536, 36874, 5376, 25445, 2619, 27582, 490, 21355, 307, 1976, 965, 13, 50764], "temperature": 0.0, "avg_logprob": -0.21935350466997194, "compression_ratio": 1.4931506849315068, "no_speech_prob": 0.01980896107852459}, {"id": 442, "seek": 274900, "start": 2758.0, "end": 2768.0, "text": " We're showing great interest in connecting with members of our panel here, which is great. BMW of course is doing amazing things in the digital twin world with their factories and robotics.", "tokens": [50814, 492, 434, 4099, 869, 1179, 294, 11015, 365, 2679, 295, 527, 4831, 510, 11, 597, 307, 869, 13, 21355, 295, 1164, 307, 884, 2243, 721, 294, 264, 4562, 18397, 1002, 365, 641, 24813, 293, 34145, 13, 51314], "temperature": 0.0, "avg_logprob": -0.21935350466997194, "compression_ratio": 1.4931506849315068, "no_speech_prob": 0.01980896107852459}, {"id": 443, "seek": 274900, "start": 2769.0, "end": 2772.0, "text": " Okay, so do you guys see it right here, right?", "tokens": [51364, 1033, 11, 370, 360, 291, 1074, 536, 309, 558, 510, 11, 558, 30, 51514], "temperature": 0.0, "avg_logprob": -0.21935350466997194, "compression_ratio": 1.4931506849315068, "no_speech_prob": 0.01980896107852459}, {"id": 444, "seek": 274900, "start": 2774.0, "end": 2776.0, "text": " Yes, that is correct.", "tokens": [51614, 1079, 11, 300, 307, 3006, 13, 51714], "temperature": 0.0, "avg_logprob": -0.21935350466997194, "compression_ratio": 1.4931506849315068, "no_speech_prob": 0.01980896107852459}, {"id": 445, "seek": 277600, "start": 2777.0, "end": 2782.0, "text": " Okay, awesome. So just show a quick demo, putting it all together.", "tokens": [50414, 1033, 11, 3476, 13, 407, 445, 855, 257, 1702, 10723, 11, 3372, 309, 439, 1214, 13, 50664], "temperature": 0.0, "avg_logprob": -0.23105295080887644, "compression_ratio": 1.4834123222748816, "no_speech_prob": 0.004268627613782883}, {"id": 446, "seek": 277600, "start": 2783.0, "end": 2794.0, "text": " <PERSON><PERSON> showed this briefly in the video. This is a live version of our factory operate application running with omniverse get up streaming embedded in our React application.", "tokens": [50714, 761, 9708, 4712, 341, 10515, 294, 264, 960, 13, 639, 307, 257, 1621, 3037, 295, 527, 9265, 9651, 3861, 2614, 365, 36874, 5376, 483, 493, 11791, 16741, 294, 527, 30644, 3861, 13, 51264], "temperature": 0.0, "avg_logprob": -0.23105295080887644, "compression_ratio": 1.4834123222748816, "no_speech_prob": 0.004268627613782883}, {"id": 447, "seek": 277600, "start": 2795.0, "end": 2799.0, "text": " As you can see, this is an entire view of the line. It's a modeling line.", "tokens": [51314, 1018, 291, 393, 536, 11, 341, 307, 364, 2302, 1910, 295, 264, 1622, 13, 467, 311, 257, 15983, 1622, 13, 51514], "temperature": 0.0, "avg_logprob": -0.23105295080887644, "compression_ratio": 1.4834123222748816, "no_speech_prob": 0.004268627613782883}, {"id": 448, "seek": 279900, "start": 2799.0, "end": 2811.0, "text": " On the left hand side, you can see a bunch of, you know, metrics for the line. What's the machine efficiency, what's the filler speed, different fillers, production volume, what flavor is going through the line and so on.", "tokens": [50364, 1282, 264, 1411, 1011, 1252, 11, 291, 393, 536, 257, 3840, 295, 11, 291, 458, 11, 16367, 337, 264, 1622, 13, 708, 311, 264, 3479, 10493, 11, 437, 311, 264, 34676, 3073, 11, 819, 2836, 433, 11, 4265, 5523, 11, 437, 6813, 307, 516, 807, 264, 1622, 293, 370, 322, 13, 50964], "temperature": 0.0, "avg_logprob": -0.15715551376342773, "compression_ratio": 1.75, "no_speech_prob": 0.13344164192676544}, {"id": 449, "seek": 279900, "start": 2812.0, "end": 2824.0, "text": " And at the bottom of the screen, you can see all the assets and their individual attributes like the filler one, it seems like has a fault, filler two is running well, and so on and so forth.", "tokens": [51014, 400, 412, 264, 2767, 295, 264, 2568, 11, 291, 393, 536, 439, 264, 9769, 293, 641, 2609, 17212, 411, 264, 34676, 472, 11, 309, 2544, 411, 575, 257, 7441, 11, 34676, 732, 307, 2614, 731, 11, 293, 370, 322, 293, 370, 5220, 13, 51614], "temperature": 0.0, "avg_logprob": -0.15715551376342773, "compression_ratio": 1.75, "no_speech_prob": 0.13344164192676544}, {"id": 450, "seek": 282400, "start": 2824.0, "end": 2837.0, "text": " And you can see the same data being leveraged and shown in the omniverse get up streaming as well as billboards, as I mentioned, using the web RTC cons that we talked about.", "tokens": [50364, 400, 291, 393, 536, 264, 912, 1412, 885, 12451, 2980, 293, 4898, 294, 264, 36874, 5376, 483, 493, 11791, 382, 731, 382, 2961, 17228, 11, 382, 286, 2835, 11, 1228, 264, 3670, 497, 18238, 1014, 300, 321, 2825, 466, 13, 51014], "temperature": 0.0, "avg_logprob": -0.10977586523278968, "compression_ratio": 1.4639175257731958, "no_speech_prob": 0.018149584531784058}, {"id": 451, "seek": 282400, "start": 2838.0, "end": 2844.0, "text": " Now, if I want to look in detail, I can see, okay, this filler one has a fault. So let me draw down into that.", "tokens": [51064, 823, 11, 498, 286, 528, 281, 574, 294, 2607, 11, 286, 393, 536, 11, 1392, 11, 341, 34676, 472, 575, 257, 7441, 13, 407, 718, 385, 2642, 760, 666, 300, 13, 51364], "temperature": 0.0, "avg_logprob": -0.10977586523278968, "compression_ratio": 1.4639175257731958, "no_speech_prob": 0.018149584531784058}, {"id": 452, "seek": 284400, "start": 2844.0, "end": 2856.0, "text": " Now, and you can see we zoom in to the asset. And not only do we zoom in, we are able to highlight specific areas of the machine where the fault actually occurred.", "tokens": [50364, 823, 11, 293, 291, 393, 536, 321, 8863, 294, 281, 264, 11999, 13, 400, 406, 787, 360, 321, 8863, 294, 11, 321, 366, 1075, 281, 5078, 2685, 3179, 295, 264, 3479, 689, 264, 7441, 767, 11068, 13, 50964], "temperature": 0.0, "avg_logprob": -0.12601120848404734, "compression_ratio": 1.5583756345177664, "no_speech_prob": 0.2623760402202606}, {"id": 453, "seek": 284400, "start": 2857.0, "end": 2866.0, "text": " So it looks like a conveyor jam. It reflects the red section is this is the place where the operator needs to focus on to fix the issue, right.", "tokens": [51014, 407, 309, 1542, 411, 257, 18053, 2454, 7872, 13, 467, 18926, 264, 2182, 3541, 307, 341, 307, 264, 1081, 689, 264, 12973, 2203, 281, 1879, 322, 281, 3191, 264, 2734, 11, 558, 13, 51464], "temperature": 0.0, "avg_logprob": -0.12601120848404734, "compression_ratio": 1.5583756345177664, "no_speech_prob": 0.2623760402202606}, {"id": 454, "seek": 286600, "start": 2866.0, "end": 2880.0, "text": " So this is super powerful. This is where we talked about how kinetic vision is helping us build the USD segmented into components so that we can leverage our data and pinpoint exact locations.", "tokens": [50364, 407, 341, 307, 1687, 4005, 13, 639, 307, 689, 321, 2825, 466, 577, 27135, 5201, 307, 4315, 505, 1322, 264, 24375, 9469, 292, 666, 6677, 370, 300, 321, 393, 13982, 527, 1412, 293, 40837, 1900, 9253, 13, 51064], "temperature": 0.0, "avg_logprob": -0.1509035773899244, "compression_ratio": 1.5885167464114833, "no_speech_prob": 0.04351642355322838}, {"id": 455, "seek": 286600, "start": 2881.0, "end": 2890.0, "text": " So not only do we give calls this we also give exact locations and recommendations for operators to immediately fix this on the shop floor.", "tokens": [51114, 407, 406, 787, 360, 321, 976, 5498, 341, 321, 611, 976, 1900, 9253, 293, 10434, 337, 19077, 281, 4258, 3191, 341, 322, 264, 3945, 4123, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1509035773899244, "compression_ratio": 1.5885167464114833, "no_speech_prob": 0.04351642355322838}, {"id": 456, "seek": 289000, "start": 2890.0, "end": 2901.0, "text": " Let me zoom back out a bit. The other thing you can see here with this purple screen is it says line is running well but not at peak efficiency. I'm going to take ways to optimize.", "tokens": [50364, 961, 385, 8863, 646, 484, 257, 857, 13, 440, 661, 551, 291, 393, 536, 510, 365, 341, 9656, 2568, 307, 309, 1619, 1622, 307, 2614, 731, 457, 406, 412, 10651, 10493, 13, 286, 478, 516, 281, 747, 2098, 281, 19719, 13, 50914], "temperature": 0.0, "avg_logprob": -0.1601314906832538, "compression_ratio": 1.502262443438914, "no_speech_prob": 0.006133468355983496}, {"id": 457, "seek": 289000, "start": 2902.0, "end": 2913.0, "text": " And this is where a genetic AI recommendation engine comes into play, right. So not only are you seeing like current statuses and basic ways to fix it.", "tokens": [50964, 400, 341, 307, 689, 257, 12462, 7318, 11879, 2848, 1487, 666, 862, 11, 558, 13, 407, 406, 787, 366, 291, 2577, 411, 2190, 6558, 279, 293, 3875, 2098, 281, 3191, 309, 13, 51514], "temperature": 0.0, "avg_logprob": -0.1601314906832538, "compression_ratio": 1.502262443438914, "no_speech_prob": 0.006133468355983496}, {"id": 458, "seek": 291300, "start": 2913.0, "end": 2932.0, "text": " You're also seeing things like hey, the filler speed should be increased from 630 to 700. That's what our agent to get I recommend just the optimal speed for the filler or something to do with the tackle to update their settings to accommodate the next set of packaging or a wrapper.", "tokens": [50364, 509, 434, 611, 2577, 721, 411, 4177, 11, 264, 34676, 3073, 820, 312, 6505, 490, 1386, 3446, 281, 15204, 13, 663, 311, 437, 527, 9461, 281, 483, 286, 2748, 445, 264, 16252, 3073, 337, 264, 34676, 420, 746, 281, 360, 365, 264, 14896, 281, 5623, 641, 6257, 281, 21410, 264, 958, 992, 295, 16836, 420, 257, 46906, 13, 51314], "temperature": 0.0, "avg_logprob": -0.2506245490043394, "compression_ratio": 1.4894736842105263, "no_speech_prob": 0.2224753051996231}, {"id": 459, "seek": 293200, "start": 2932.0, "end": 2942.0, "text": " What is there to replace and similarly, you can now say hey, go to the rapper. Let me see what's going on. Hey, prepare to wrap replace the rap material.", "tokens": [50364, 708, 307, 456, 281, 7406, 293, 14138, 11, 291, 393, 586, 584, 4177, 11, 352, 281, 264, 26457, 13, 961, 385, 536, 437, 311, 516, 322, 13, 1911, 11, 5940, 281, 7019, 7406, 264, 5099, 2527, 13, 50864], "temperature": 0.0, "avg_logprob": -0.16705030010592553, "compression_ratio": 1.6682242990654206, "no_speech_prob": 0.25809088349342346}, {"id": 460, "seek": 293200, "start": 2943.0, "end": 2955.0, "text": " AI has detected that you might run out of the rap soon enough so that's the time for your operator to go on set up the rap get it put it in place so that you don't have an issue you don't have stoppages.", "tokens": [50914, 7318, 575, 21896, 300, 291, 1062, 1190, 484, 295, 264, 5099, 2321, 1547, 370, 300, 311, 264, 565, 337, 428, 12973, 281, 352, 322, 992, 493, 264, 5099, 483, 309, 829, 309, 294, 1081, 370, 300, 291, 500, 380, 362, 364, 2734, 291, 500, 380, 362, 1590, 79, 1660, 13, 51514], "temperature": 0.0, "avg_logprob": -0.16705030010592553, "compression_ratio": 1.6682242990654206, "no_speech_prob": 0.25809088349342346}, {"id": 461, "seek": 295500, "start": 2955.0, "end": 2972.0, "text": " You don't have down times. So very, very powerful leveraging of our data and leveraging of all the 3D assets that <PERSON> showed the army was kept up streaming platform bringing it all together to show immense value for our customers.", "tokens": [50364, 509, 500, 380, 362, 760, 1413, 13, 407, 588, 11, 588, 4005, 32666, 295, 527, 1412, 293, 32666, 295, 439, 264, 805, 35, 9769, 300, 17809, 4712, 264, 7267, 390, 4305, 493, 11791, 3663, 5062, 309, 439, 1214, 281, 855, 22920, 2158, 337, 527, 4581, 13, 51214], "temperature": 0.0, "avg_logprob": -0.281366019413389, "compression_ratio": 1.4913294797687862, "no_speech_prob": 0.05941159278154373}, {"id": 462, "seek": 295500, "start": 2973.0, "end": 2975.0, "text": " I will stop right there.", "tokens": [51264, 286, 486, 1590, 558, 456, 13, 51364], "temperature": 0.0, "avg_logprob": -0.281366019413389, "compression_ratio": 1.4913294797687862, "no_speech_prob": 0.05941159278154373}, {"id": 463, "seek": 297500, "start": 2975.0, "end": 2985.0, "text": " Wow, amazing. I don't think anybody wants you to stop where everyone was blown away. You're going live and everyone can obviously you notice the time and date up up on the right. It was not faked.", "tokens": [50364, 3153, 11, 2243, 13, 286, 500, 380, 519, 4472, 2738, 291, 281, 1590, 689, 1518, 390, 16479, 1314, 13, 509, 434, 516, 1621, 293, 1518, 393, 2745, 291, 3449, 264, 565, 293, 4002, 493, 493, 322, 264, 558, 13, 467, 390, 406, 283, 7301, 13, 50864], "temperature": 0.0, "avg_logprob": -0.26948027517281326, "compression_ratio": 1.6431226765799256, "no_speech_prob": 0.26537278294563293}, {"id": 464, "seek": 297500, "start": 2986.0, "end": 2999.0, "text": " Real time. That's pretty amazing. That's a great question. The comments in the chat. Do you any any more or less thoughts before we start tackling some of these questions. I know we've been discussing something in the background which to tackle.", "tokens": [50914, 8467, 565, 13, 663, 311, 1238, 2243, 13, 663, 311, 257, 869, 1168, 13, 440, 3053, 294, 264, 5081, 13, 1144, 291, 604, 604, 544, 420, 1570, 4598, 949, 321, 722, 34415, 512, 295, 613, 1651, 13, 286, 458, 321, 600, 668, 10850, 746, 294, 264, 3678, 597, 281, 14896, 13, 51564], "temperature": 0.0, "avg_logprob": -0.26948027517281326, "compression_ratio": 1.6431226765799256, "no_speech_prob": 0.26537278294563293}, {"id": 465, "seek": 299900, "start": 3000.0, "end": 3012.0, "text": " Okay, alright, so let's go. We have we've got nine minutes, so which means we got we got a hustle. So let's try to let's try to keep our answers as concise as possible and we can refer people to our discord.", "tokens": [50414, 1033, 11, 5845, 11, 370, 718, 311, 352, 13, 492, 362, 321, 600, 658, 4949, 2077, 11, 370, 597, 1355, 321, 658, 321, 658, 257, 34639, 13, 407, 718, 311, 853, 281, 718, 311, 853, 281, 1066, 527, 6338, 382, 44882, 382, 1944, 293, 321, 393, 2864, 561, 281, 527, 32989, 13, 51014], "temperature": 0.0, "avg_logprob": -0.19041535706646676, "compression_ratio": 1.7279151943462898, "no_speech_prob": 0.12450166046619415}, {"id": 466, "seek": 299900, "start": 3013.0, "end": 3026.0, "text": " <PERSON> is going to set up a thread on our discord server specifically for this live stream. So whether you're watching this live or the replay, you can go to that thread and you could continue to ask questions, conversations and hopefully with other viewers will also go there too.", "tokens": [51064, 42814, 307, 516, 281, 992, 493, 257, 7207, 322, 527, 32989, 7154, 4682, 337, 341, 1621, 4309, 13, 407, 1968, 291, 434, 1976, 341, 1621, 420, 264, 23836, 11, 291, 393, 352, 281, 300, 7207, 293, 291, 727, 2354, 281, 1029, 1651, 11, 7315, 293, 4696, 365, 661, 8499, 486, 611, 352, 456, 886, 13, 51714], "temperature": 0.0, "avg_logprob": -0.19041535706646676, "compression_ratio": 1.7279151943462898, "no_speech_prob": 0.12450166046619415}, {"id": 467, "seek": 302600, "start": 3026.0, "end": 3042.0, "text": " So you can chat with them on it. Alright, so let's do the little lightning round of questions here. I think earlier on actually we had a good question. I think for a first site machine so sure if you want to take this one about how do you approach a client engagement and when client stakeholders have different priorities.", "tokens": [50364, 407, 291, 393, 5081, 365, 552, 322, 309, 13, 2798, 11, 370, 718, 311, 360, 264, 707, 16589, 3098, 295, 1651, 510, 13, 286, 519, 3071, 322, 767, 321, 632, 257, 665, 1168, 13, 286, 519, 337, 257, 700, 3621, 3479, 370, 988, 498, 291, 528, 281, 747, 341, 472, 466, 577, 360, 291, 3109, 257, 6423, 8742, 293, 562, 6423, 17779, 362, 819, 15503, 13, 51164], "temperature": 0.0, "avg_logprob": -0.1776616784590709, "compression_ratio": 1.6090909090909091, "no_speech_prob": 0.01200565230101347}, {"id": 468, "seek": 302600, "start": 3043.0, "end": 3045.0, "text": " Who do you prioritize and why.", "tokens": [51214, 2102, 360, 291, 25164, 293, 983, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1776616784590709, "compression_ratio": 1.6090909090909091, "no_speech_prob": 0.01200565230101347}, {"id": 469, "seek": 304500, "start": 3046.0, "end": 3065.0, "text": " Yeah, so the answer to that is we have to prioritize everybody. He actually worked with both it and we provide a complete solution without the agreement and agreement across these functions and now now we're also seeing a separate a function which really collaborates with it and", "tokens": [50414, 865, 11, 370, 264, 1867, 281, 300, 307, 321, 362, 281, 25164, 2201, 13, 634, 767, 2732, 365, 1293, 309, 293, 321, 2893, 257, 3566, 3827, 1553, 264, 8106, 293, 8106, 2108, 613, 6828, 293, 586, 586, 321, 434, 611, 2577, 257, 4994, 257, 2445, 597, 534, 5091, 1024, 365, 309, 293, 51364], "temperature": 0.0, "avg_logprob": -0.3206421988351004, "compression_ratio": 1.5674157303370786, "no_speech_prob": 0.11642180383205414}, {"id": 470, "seek": 306500, "start": 3066.0, "end": 3076.0, "text": " and we provide daily solutions to one in the enterprise. So we go all the way we address security, plant connectivity, as well as operational data and insights.", "tokens": [50414, 293, 321, 2893, 5212, 6547, 281, 472, 294, 264, 14132, 13, 407, 321, 352, 439, 264, 636, 321, 2985, 3825, 11, 3709, 21095, 11, 382, 731, 382, 16607, 1412, 293, 14310, 13, 50914], "temperature": 0.0, "avg_logprob": -0.17503205467672908, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.06681089103221893}, {"id": 471, "seek": 306500, "start": 3077.0, "end": 3087.0, "text": " So the real answer is you cannot afford to prioritize one over the other because then you're not going to have a successful plant level transformation that scales.", "tokens": [50964, 407, 264, 957, 1867, 307, 291, 2644, 6157, 281, 25164, 472, 670, 264, 661, 570, 550, 291, 434, 406, 516, 281, 362, 257, 4406, 3709, 1496, 9887, 300, 17408, 13, 51464], "temperature": 0.0, "avg_logprob": -0.17503205467672908, "compression_ratio": 1.588235294117647, "no_speech_prob": 0.06681089103221893}, {"id": 472, "seek": 308700, "start": 3087.0, "end": 3096.0, "text": " Okay, very, very super helpful and just a quick note we had to wave goodbye to <PERSON> a minute ago he had none of the meaning you had to go do so thank you <PERSON> for always helping out great to have you here.", "tokens": [50364, 1033, 11, 588, 11, 588, 1687, 4961, 293, 445, 257, 1702, 3637, 321, 632, 281, 5772, 12084, 281, 29276, 257, 3456, 2057, 415, 632, 6022, 295, 264, 3620, 291, 632, 281, 352, 360, 370, 1309, 291, 29276, 337, 1009, 4315, 484, 869, 281, 362, 291, 510, 13, 50814], "temperature": 0.0, "avg_logprob": -0.19805416618425822, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.22999437153339386}, {"id": 473, "seek": 308700, "start": 3097.0, "end": 3104.0, "text": " Okay, we also I'm looking so we have a couple of different chat channels where we're keeping track of some of these questions I'm looking at our internal chat here where I see a few things that were discussed.", "tokens": [50864, 1033, 11, 321, 611, 286, 478, 1237, 370, 321, 362, 257, 1916, 295, 819, 5081, 9235, 689, 321, 434, 5145, 2837, 295, 512, 295, 613, 1651, 286, 478, 1237, 412, 527, 6920, 5081, 510, 689, 286, 536, 257, 1326, 721, 300, 645, 7152, 13, 51214], "temperature": 0.0, "avg_logprob": -0.19805416618425822, "compression_ratio": 1.6951219512195121, "no_speech_prob": 0.22999437153339386}, {"id": 474, "seek": 310400, "start": 3104.0, "end": 3115.0, "text": " Sure you just mentioned you saw a good question from <PERSON>. Amazing collab curious with this foundation which type of simulation becomes more feasible high fidelity asset level modeling or broader plant level.", "tokens": [50364, 4894, 291, 445, 2835, 291, 1866, 257, 665, 1168, 490, 15777, 13, 14165, 44228, 6369, 365, 341, 7030, 597, 2010, 295, 16575, 3643, 544, 26648, 1090, 46404, 11999, 1496, 15983, 420, 13227, 3709, 1496, 13, 50914], "temperature": 0.0, "avg_logprob": -0.17335180000022607, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.11397276818752289}, {"id": 475, "seek": 310400, "start": 3116.0, "end": 3119.0, "text": " What if situational modeling did you want to tackle this one.", "tokens": [50964, 708, 498, 2054, 1478, 15983, 630, 291, 528, 281, 14896, 341, 472, 13, 51114], "temperature": 0.0, "avg_logprob": -0.17335180000022607, "compression_ratio": 1.511111111111111, "no_speech_prob": 0.11397276818752289}, {"id": 476, "seek": 311900, "start": 3119.0, "end": 3133.0, "text": " Sure, I'd also like to invite maybe so they're in others if they have any specific ideas just to talk about this but we are thinking a lot about what a situational modeling and scenario modeling.", "tokens": [50364, 4894, 11, 286, 1116, 611, 411, 281, 7980, 1310, 370, 436, 434, 294, 2357, 498, 436, 362, 604, 2685, 3487, 445, 281, 751, 466, 341, 457, 321, 366, 1953, 257, 688, 466, 437, 257, 2054, 1478, 15983, 293, 9005, 15983, 13, 51064], "temperature": 0.0, "avg_logprob": -0.15053274439669204, "compression_ratio": 1.6336206896551724, "no_speech_prob": 0.061293549835681915}, {"id": 477, "seek": 311900, "start": 3134.0, "end": 3144.0, "text": " Which means that if I have a change in raw material or if I add another machine to my line. How does the capacity change what will my line look like and what should I be planning for.", "tokens": [51114, 3013, 1355, 300, 498, 286, 362, 257, 1319, 294, 8936, 2527, 420, 498, 286, 909, 1071, 3479, 281, 452, 1622, 13, 1012, 775, 264, 6042, 1319, 437, 486, 452, 1622, 574, 411, 293, 437, 820, 286, 312, 5038, 337, 13, 51614], "temperature": 0.0, "avg_logprob": -0.15053274439669204, "compression_ratio": 1.6336206896551724, "no_speech_prob": 0.061293549835681915}, {"id": 478, "seek": 314400, "start": 3144.0, "end": 3152.0, "text": " Ideally we help them make real decisions by seeing things like if you add another machine you can reduce all of your weekend shifts or eliminate them all together.", "tokens": [50364, 40817, 321, 854, 552, 652, 957, 5327, 538, 2577, 721, 411, 498, 291, 909, 1071, 3479, 291, 393, 5407, 439, 295, 428, 6711, 19201, 420, 13819, 552, 439, 1214, 13, 50764], "temperature": 0.0, "avg_logprob": -0.16356891183292163, "compression_ratio": 1.6150627615062763, "no_speech_prob": 0.07681602984666824}, {"id": 479, "seek": 314400, "start": 3153.0, "end": 3164.0, "text": " And again the focus is on providing broad scenario level guidance with with our simulations but if anyone has additional thoughts and everything I'd love to invite them as well.", "tokens": [50814, 400, 797, 264, 1879, 307, 322, 6530, 4152, 9005, 1496, 10056, 365, 365, 527, 35138, 457, 498, 2878, 575, 4497, 4598, 293, 1203, 286, 1116, 959, 281, 7980, 552, 382, 731, 13, 51364], "temperature": 0.0, "avg_logprob": -0.16356891183292163, "compression_ratio": 1.6150627615062763, "no_speech_prob": 0.07681602984666824}, {"id": 480, "seek": 314400, "start": 3165.0, "end": 3166.0, "text": " Yeah, yeah.", "tokens": [51414, 865, 11, 1338, 13, 51464], "temperature": 0.0, "avg_logprob": -0.16356891183292163, "compression_ratio": 1.6150627615062763, "no_speech_prob": 0.07681602984666824}, {"id": 481, "seek": 314400, "start": 3167.0, "end": 3168.0, "text": " I have one point to add as well.", "tokens": [51514, 286, 362, 472, 935, 281, 909, 382, 731, 13, 51564], "temperature": 0.0, "avg_logprob": -0.16356891183292163, "compression_ratio": 1.6150627615062763, "no_speech_prob": 0.07681602984666824}, {"id": 482, "seek": 316800, "start": 3168.0, "end": 3174.0, "text": " Yeah, so it's sort of I would say both right like at the first step.", "tokens": [50364, 865, 11, 370, 309, 311, 1333, 295, 286, 576, 584, 1293, 558, 411, 412, 264, 700, 1823, 13, 50664], "temperature": 0.0, "avg_logprob": -0.1951473684871898, "compression_ratio": 1.6495726495726495, "no_speech_prob": 0.047347623854875565}, {"id": 483, "seek": 316800, "start": 3175.0, "end": 3197.0, "text": " We're trying to get as accurate a representation of the models as possible right and then try to solve real problems like these agentic recommendations or what's happening on the shop floor and so on so forth and for that perhaps you don't need like the super super high fidelity still high fidelity as you could see.", "tokens": [50714, 492, 434, 1382, 281, 483, 382, 8559, 257, 10290, 295, 264, 5245, 382, 1944, 558, 293, 550, 853, 281, 5039, 957, 2740, 411, 613, 9461, 299, 10434, 420, 437, 311, 2737, 322, 264, 3945, 4123, 293, 370, 322, 370, 5220, 293, 337, 300, 4317, 291, 500, 380, 643, 411, 264, 1687, 1687, 1090, 46404, 920, 1090, 46404, 382, 291, 727, 536, 13, 51814], "temperature": 0.0, "avg_logprob": -0.1951473684871898, "compression_ratio": 1.6495726495726495, "no_speech_prob": 0.047347623854875565}, {"id": 484, "seek": 319800, "start": 3198.0, "end": 3218.0, "text": " But as as you were talking about but there are definitely use cases when you're looking at animations and like real time playbacks of you know how things happen and how it caused a fault or what have you where a more realistic representation is absolutely essential.", "tokens": [50364, 583, 382, 382, 291, 645, 1417, 466, 457, 456, 366, 2138, 764, 3331, 562, 291, 434, 1237, 412, 22868, 293, 411, 957, 565, 862, 17758, 295, 291, 458, 577, 721, 1051, 293, 577, 309, 7008, 257, 7441, 420, 437, 362, 291, 689, 257, 544, 12465, 10290, 307, 3122, 7115, 13, 51364], "temperature": 0.0, "avg_logprob": -0.17160551171553762, "compression_ratio": 1.7089201877934272, "no_speech_prob": 0.006424202583730221}, {"id": 485, "seek": 319800, "start": 3219.0, "end": 3224.0, "text": " So both are different use cases and you know both are something we are looking at that to finish.", "tokens": [51414, 407, 1293, 366, 819, 764, 3331, 293, 291, 458, 1293, 366, 746, 321, 366, 1237, 412, 300, 281, 2413, 13, 51664], "temperature": 0.0, "avg_logprob": -0.17160551171553762, "compression_ratio": 1.7089201877934272, "no_speech_prob": 0.006424202583730221}, {"id": 486, "seek": 322400, "start": 3224.0, "end": 3242.0, "text": " Yeah, so but what what you guys said is spot on but specifically on the asset level or simulation specific question that <PERSON> is asking that we were talking about the bottling line use case the single most expensive asset that's there in the line is a filler and then of course the next one is a packer right.", "tokens": [50364, 865, 11, 370, 457, 437, 437, 291, 1074, 848, 307, 4008, 322, 457, 4682, 322, 264, 11999, 1496, 420, 16575, 2685, 1168, 300, 15777, 307, 3365, 300, 321, 645, 1417, 466, 264, 2274, 1688, 1622, 764, 1389, 264, 2167, 881, 5124, 11999, 300, 311, 456, 294, 264, 1622, 307, 257, 34676, 293, 550, 295, 1164, 264, 958, 472, 307, 257, 2844, 260, 558, 13, 51264], "temperature": 0.0, "avg_logprob": -0.1475536823272705, "compression_ratio": 1.6455026455026456, "no_speech_prob": 0.005851053632795811}, {"id": 487, "seek": 324200, "start": 3242.0, "end": 3256.0, "text": " So there are filler level simulations that can also be done to understand spillage inefficiencies things that are supposed to be followed by certain string guidelines of course you cannot stop a filler to run those scenarios.", "tokens": [50364, 407, 456, 366, 34676, 1496, 35138, 300, 393, 611, 312, 1096, 281, 1223, 22044, 609, 7167, 3341, 31294, 721, 300, 366, 3442, 281, 312, 6263, 538, 1629, 6798, 12470, 295, 1164, 291, 2644, 1590, 257, 34676, 281, 1190, 729, 15077, 13, 51064], "temperature": 0.0, "avg_logprob": -0.12418829543249947, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0689990445971489}, {"id": 488, "seek": 324200, "start": 3257.0, "end": 3259.0, "text": " So that's where simulation comes really handy.", "tokens": [51114, 407, 300, 311, 689, 16575, 1487, 534, 13239, 13, 51214], "temperature": 0.0, "avg_logprob": -0.12418829543249947, "compression_ratio": 1.5454545454545454, "no_speech_prob": 0.0689990445971489}, {"id": 489, "seek": 325900, "start": 3260.0, "end": 3272.0, "text": " So to think about the whole digital twin journey that we were discussing in the last few minutes is to break down into two separate journeys one is the whole operational digital twin and the other one is a simulation digital twin and they both kind of go hand in hand.", "tokens": [50414, 407, 281, 519, 466, 264, 1379, 4562, 18397, 4671, 300, 321, 645, 10850, 294, 264, 1036, 1326, 2077, 307, 281, 1821, 760, 666, 732, 4994, 36736, 472, 307, 264, 1379, 16607, 4562, 18397, 293, 264, 661, 472, 307, 257, 16575, 4562, 18397, 293, 436, 1293, 733, 295, 352, 1011, 294, 1011, 13, 51014], "temperature": 0.0, "avg_logprob": -0.0731062086943154, "compression_ratio": 1.9963235294117647, "no_speech_prob": 0.10302304476499557}, {"id": 490, "seek": 325900, "start": 3273.0, "end": 3288.0, "text": " So the filler simulation is one of the examples where you could be thinking about fluid level simulations and the CFD style simulations as well where we were actually in track with a third party simulation provider that can run that kind of filler level simulations as well.", "tokens": [51064, 407, 264, 34676, 16575, 307, 472, 295, 264, 5110, 689, 291, 727, 312, 1953, 466, 9113, 1496, 35138, 293, 264, 21792, 35, 3758, 35138, 382, 731, 689, 321, 645, 767, 294, 2837, 365, 257, 2636, 3595, 16575, 12398, 300, 393, 1190, 300, 733, 295, 34676, 1496, 35138, 382, 731, 13, 51814], "temperature": 0.0, "avg_logprob": -0.0731062086943154, "compression_ratio": 1.9963235294117647, "no_speech_prob": 0.10302304476499557}, {"id": 491, "seek": 328900, "start": 3289.0, "end": 3300.0, "text": " It's a it's a team play it's not like in a one size fits all and one partner provides all the different experience in the services you bring the right ingredients for the right kind of job to get the results.", "tokens": [50364, 467, 311, 257, 309, 311, 257, 1469, 862, 309, 311, 406, 411, 294, 257, 472, 2744, 9001, 439, 293, 472, 4975, 6417, 439, 264, 819, 1752, 294, 264, 3328, 291, 1565, 264, 558, 6952, 337, 264, 558, 733, 295, 1691, 281, 483, 264, 3542, 13, 50914], "temperature": 0.0, "avg_logprob": -0.18841616942150757, "compression_ratio": 1.6345381526104417, "no_speech_prob": 0.003697306616231799}, {"id": 492, "seek": 328900, "start": 3301.0, "end": 3302.0, "text": " The business results that you expect.", "tokens": [50964, 440, 1606, 3542, 300, 291, 2066, 13, 51014], "temperature": 0.0, "avg_logprob": -0.18841616942150757, "compression_ratio": 1.6345381526104417, "no_speech_prob": 0.003697306616231799}, {"id": 493, "seek": 328900, "start": 3303.0, "end": 3304.0, "text": " Great.", "tokens": [51064, 3769, 13, 51114], "temperature": 0.0, "avg_logprob": -0.18841616942150757, "compression_ratio": 1.6345381526104417, "no_speech_prob": 0.003697306616231799}, {"id": 494, "seek": 328900, "start": 3304.0, "end": 3305.0, "text": " Okay that's super helpful.", "tokens": [51114, 1033, 300, 311, 1687, 4961, 13, 51164], "temperature": 0.0, "avg_logprob": -0.18841616942150757, "compression_ratio": 1.6345381526104417, "no_speech_prob": 0.003697306616231799}, {"id": 495, "seek": 328900, "start": 3305.0, "end": 3312.0, "text": " And so this this gentleman or a gentle lady has asked us a couple of times in chat so I know they're eager to for this answer.", "tokens": [51164, 400, 370, 341, 341, 15761, 420, 257, 6424, 7262, 575, 2351, 505, 257, 1916, 295, 1413, 294, 5081, 370, 286, 458, 436, 434, 18259, 281, 337, 341, 1867, 13, 51514], "temperature": 0.0, "avg_logprob": -0.18841616942150757, "compression_ratio": 1.6345381526104417, "no_speech_prob": 0.003697306616231799}, {"id": 496, "seek": 331200, "start": 3312.0, "end": 3323.0, "text": " This is an interesting question obviously because we're talking about very developer kind of focused pipeline but we have a lot of creators out there in the world who really want to ramp up and and contribute to these challenges.", "tokens": [50364, 639, 307, 364, 1880, 1168, 2745, 570, 321, 434, 1417, 466, 588, 10754, 733, 295, 5178, 15517, 457, 321, 362, 257, 688, 295, 16039, 484, 456, 294, 264, 1002, 567, 534, 528, 281, 12428, 493, 293, 293, 10586, 281, 613, 4759, 13, 50914], "temperature": 0.0, "avg_logprob": -0.21858546970126866, "compression_ratio": 1.6699346405228759, "no_speech_prob": 0.15802618861198425}, {"id": 497, "seek": 331200, "start": 3324.0, "end": 3337.0, "text": " At C graph recently there was a session if <PERSON> has has a second maybe she can try to find that session I think the moment live the other day for everyone to watch now we had a session about as it's really as if you're a 3D content creator how do you upscale for the physical AI.", "tokens": [50964, 1711, 383, 4295, 3938, 456, 390, 257, 5481, 498, 42814, 575, 575, 257, 1150, 1310, 750, 393, 853, 281, 915, 300, 5481, 286, 519, 264, 1623, 1621, 264, 661, 786, 337, 1518, 281, 1159, 586, 321, 632, 257, 5481, 466, 382, 309, 311, 534, 382, 498, 291, 434, 257, 805, 35, 2701, 14181, 577, 360, 291, 493, 20033, 337, 264, 4001, 7318, 13, 51614], "temperature": 0.0, "avg_logprob": -0.21858546970126866, "compression_ratio": 1.6699346405228759, "no_speech_prob": 0.15802618861198425}, {"id": 498, "seek": 333700, "start": 3338.0, "end": 3341.0, "text": " Does anyone have any any suggestions for <PERSON><PERSON> here.", "tokens": [50414, 4402, 2878, 362, 604, 604, 13396, 337, 398, 1299, 510, 13, 50564], "temperature": 0.0, "avg_logprob": -0.15977787360166892, "compression_ratio": 1.6767676767676767, "no_speech_prob": 0.07883115112781525}, {"id": 499, "seek": 333700, "start": 3342.0, "end": 3345.0, "text": " I think I think this is a great question for kinetic vision and <PERSON> do you want to take that.", "tokens": [50614, 286, 519, 286, 519, 341, 307, 257, 869, 1168, 337, 27135, 5201, 293, 17809, 360, 291, 528, 281, 747, 300, 13, 50764], "temperature": 0.0, "avg_logprob": -0.15977787360166892, "compression_ratio": 1.6767676767676767, "no_speech_prob": 0.07883115112781525}, {"id": 500, "seek": 333700, "start": 3347.0, "end": 3358.0, "text": " Yeah so I actually saw this question I thought wow we're actually going to be answering some of the one of these questions so you know I the things that I laid out in my presentation.", "tokens": [50864, 865, 370, 286, 767, 1866, 341, 1168, 286, 1194, 6076, 321, 434, 767, 516, 281, 312, 13430, 512, 295, 264, 472, 295, 613, 1651, 370, 291, 458, 286, 264, 721, 300, 286, 9897, 484, 294, 452, 5860, 13, 51414], "temperature": 0.0, "avg_logprob": -0.15977787360166892, "compression_ratio": 1.6767676767676767, "no_speech_prob": 0.07883115112781525}, {"id": 501, "seek": 335800, "start": 3358.0, "end": 3363.0, "text": " It is a lot about tools curiosity and skill sets right so.", "tokens": [50364, 467, 307, 257, 688, 466, 3873, 18769, 293, 5389, 6352, 558, 370, 13, 50614], "temperature": 0.0, "avg_logprob": -0.11826621712028206, "compression_ratio": 1.7179487179487178, "no_speech_prob": 0.6018408536911011}, {"id": 502, "seek": 335800, "start": 3364.0, "end": 3380.0, "text": " Really just you know for this type of simulated world we need these types of tools we're using <PERSON>vis scanning we're using reality capture and then we're using what you know if you're if you're a creator you're already using this great set of 3D content content creation tools.", "tokens": [50664, 4083, 445, 291, 458, 337, 341, 2010, 295, 41713, 1002, 321, 643, 613, 3467, 295, 3873, 321, 434, 1228, 9219, 271, 27019, 321, 434, 1228, 4103, 7983, 293, 550, 321, 434, 1228, 437, 291, 458, 498, 291, 434, 498, 291, 434, 257, 14181, 291, 434, 1217, 1228, 341, 869, 992, 295, 805, 35, 2701, 2701, 8016, 3873, 13, 51464], "temperature": 0.0, "avg_logprob": -0.11826621712028206, "compression_ratio": 1.7179487179487178, "no_speech_prob": 0.6018408536911011}, {"id": 503, "seek": 338000, "start": 3381.0, "end": 3388.0, "text": " You know in Maya 3D Studio Max substance you know those are those are all the set of tools that you're using so.", "tokens": [50414, 509, 458, 294, 21695, 805, 35, 13500, 7402, 12961, 291, 458, 729, 366, 729, 366, 439, 264, 992, 295, 3873, 300, 291, 434, 1228, 370, 13, 50764], "temperature": 0.0, "avg_logprob": -0.10771601374556379, "compression_ratio": 1.7251184834123223, "no_speech_prob": 0.3432253301143646}, {"id": 504, "seek": 338000, "start": 3389.0, "end": 3407.0, "text": " I think really using the those tools and the guidance on how to get to that level simulated world I think is you know from from the from those software vendors is you know going to be really important but the other thing is the collaboration what I'm.", "tokens": [50814, 286, 519, 534, 1228, 264, 729, 3873, 293, 264, 10056, 322, 577, 281, 483, 281, 300, 1496, 41713, 1002, 286, 519, 307, 291, 458, 490, 490, 264, 490, 729, 4722, 22056, 307, 291, 458, 516, 281, 312, 534, 1021, 457, 264, 661, 551, 307, 264, 9363, 437, 286, 478, 13, 51714], "temperature": 0.0, "avg_logprob": -0.10771601374556379, "compression_ratio": 1.7251184834123223, "no_speech_prob": 0.3432253301143646}, {"id": 505, "seek": 340700, "start": 3407.0, "end": 3415.0, "text": " You know a lot of these questions what I'm hearing is they all relate to collaboration and communication.", "tokens": [50364, 509, 458, 257, 688, 295, 613, 1651, 437, 286, 478, 4763, 307, 436, 439, 10961, 281, 9363, 293, 6101, 13, 50764], "temperature": 0.0, "avg_logprob": -0.08877344131469726, "compression_ratio": 1.5939086294416243, "no_speech_prob": 0.0035735308192670345}, {"id": 506, "seek": 340700, "start": 3416.0, "end": 3428.0, "text": " These are big problems we're creating an entire simulated world so that means that we need all of the skill sets and expertise from each of the individuals that places something in that in that virtual world.", "tokens": [50814, 1981, 366, 955, 2740, 321, 434, 4084, 364, 2302, 41713, 1002, 370, 300, 1355, 300, 321, 643, 439, 295, 264, 5389, 6352, 293, 11769, 490, 1184, 295, 264, 5346, 300, 3190, 746, 294, 300, 294, 300, 6374, 1002, 13, 51414], "temperature": 0.0, "avg_logprob": -0.08877344131469726, "compression_ratio": 1.5939086294416243, "no_speech_prob": 0.0035735308192670345}, {"id": 507, "seek": 342800, "start": 3428.0, "end": 3443.0, "text": " I'm sorry <PERSON><PERSON><PERSON> I'm not sure when you learned about fillers but you know to make a good decision about how to make a filler run operate properly and it's optimal speed is probably not your your expertise you know enough to be dangerous.", "tokens": [50364, 286, 478, 2597, 460, 12779, 71, 286, 478, 406, 988, 562, 291, 3264, 466, 2836, 433, 457, 291, 458, 281, 652, 257, 665, 3537, 466, 577, 281, 652, 257, 34676, 1190, 9651, 6108, 293, 309, 311, 16252, 3073, 307, 1391, 406, 428, 428, 11769, 291, 458, 1547, 281, 312, 5795, 13, 51114], "temperature": 0.0, "avg_logprob": -0.13004911681752146, "compression_ratio": 1.6576576576576576, "no_speech_prob": 0.37880030274391174}, {"id": 508, "seek": 342800, "start": 3444.0, "end": 3452.0, "text": " But working with that person who either sold the filler to this customer or who operates the filler every day is really important.", "tokens": [51164, 583, 1364, 365, 300, 954, 567, 2139, 3718, 264, 34676, 281, 341, 5474, 420, 567, 22577, 264, 34676, 633, 786, 307, 534, 1021, 13, 51564], "temperature": 0.0, "avg_logprob": -0.13004911681752146, "compression_ratio": 1.6576576576576576, "no_speech_prob": 0.37880030274391174}, {"id": 509, "seek": 345200, "start": 3452.0, "end": 3460.0, "text": " And so you know you have these hard skills but the soft skills are just as important making sure that you are building that network of experts and you're collaborating them.", "tokens": [50364, 400, 370, 291, 458, 291, 362, 613, 1152, 3942, 457, 264, 2787, 3942, 366, 445, 382, 1021, 1455, 988, 300, 291, 366, 2390, 300, 3209, 295, 8572, 293, 291, 434, 30188, 552, 13, 50764], "temperature": 0.0, "avg_logprob": -0.07794712014394263, "compression_ratio": 1.6930232558139535, "no_speech_prob": 0.36949676275253296}, {"id": 510, "seek": 345200, "start": 3461.0, "end": 3471.0, "text": " Collaborating with them as you build that simulated world I know this is a developer webinar and we want to hear about you know programming languages and programs but I cannot underestimate.", "tokens": [50814, 44483, 990, 365, 552, 382, 291, 1322, 300, 41713, 1002, 286, 458, 341, 307, 257, 10754, 10942, 293, 321, 528, 281, 1568, 466, 291, 458, 9410, 8650, 293, 4268, 457, 286, 2644, 35826, 13, 51314], "temperature": 0.0, "avg_logprob": -0.07794712014394263, "compression_ratio": 1.6930232558139535, "no_speech_prob": 0.36949676275253296}, {"id": 511, "seek": 347100, "start": 3472.0, "end": 3484.0, "text": " The this is where the I saw some you know I'll pivot here to I see things you know comments in here about well you know once we you know once we get rid of the humans where how do we you know manage the narrative.", "tokens": [50414, 440, 341, 307, 689, 264, 286, 1866, 512, 291, 458, 286, 603, 14538, 510, 281, 286, 536, 721, 291, 458, 3053, 294, 510, 466, 731, 291, 458, 1564, 321, 291, 458, 1564, 321, 483, 3973, 295, 264, 6255, 689, 577, 360, 321, 291, 458, 3067, 264, 9977, 13, 51014], "temperature": 0.0, "avg_logprob": -0.08750866368873832, "compression_ratio": 1.8744588744588744, "no_speech_prob": 0.5297110080718994}, {"id": 512, "seek": 347100, "start": 3485.0, "end": 3498.0, "text": " Don't get rid of the humans that the humans like we're stuck doing so much BS now elevate the humans and continue to build those relationships use the relationships to build that simulated world OK I'm going crazy here.", "tokens": [51064, 1468, 380, 483, 3973, 295, 264, 6255, 300, 264, 6255, 411, 321, 434, 5541, 884, 370, 709, 27253, 586, 33054, 264, 6255, 293, 2354, 281, 1322, 729, 6159, 764, 264, 6159, 281, 1322, 300, 41713, 1002, 2264, 286, 478, 516, 3219, 510, 13, 51714], "temperature": 0.0, "avg_logprob": -0.08750866368873832, "compression_ratio": 1.8744588744588744, "no_speech_prob": 0.5297110080718994}, {"id": 513, "seek": 349800, "start": 3499.0, "end": 3503.0, "text": " I'm going to save that quote when I have an extra view with my boss so thank you.", "tokens": [50414, 286, 478, 516, 281, 3155, 300, 6513, 562, 286, 362, 364, 2857, 1910, 365, 452, 5741, 370, 1309, 291, 13, 50614], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 514, "seek": 349800, "start": 3503.0, "end": 3504.0, "text": " Cool.", "tokens": [50614, 8561, 13, 50664], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 515, "seek": 349800, "start": 3504.0, "end": 3505.0, "text": " That's great.", "tokens": [50664, 663, 311, 869, 13, 50714], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 516, "seek": 349800, "start": 3505.0, "end": 3506.0, "text": " You're out of here at March.", "tokens": [50714, 509, 434, 484, 295, 510, 412, 6129, 13, 50764], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 517, "seek": 349800, "start": 3506.0, "end": 3507.0, "text": " All right.", "tokens": [50764, 1057, 558, 13, 50814], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 518, "seek": 349800, "start": 3507.0, "end": 3513.0, "text": " So speaking about programming languages about this question came in from LinkedIn about CUDA programming and the a models.", "tokens": [50814, 407, 4124, 466, 9410, 8650, 466, 341, 1168, 1361, 294, 490, 20657, 466, 29777, 7509, 9410, 293, 264, 257, 5245, 13, 51114], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 519, "seek": 349800, "start": 3513.0, "end": 3514.0, "text": " Don't want to tackle this one.", "tokens": [51114, 1468, 380, 528, 281, 14896, 341, 472, 13, 51164], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 520, "seek": 349800, "start": 3516.0, "end": 3521.0, "text": " I don't know if we have a CUDA expert here but no I don't think I don't think it feels like a.", "tokens": [51264, 286, 500, 380, 458, 498, 321, 362, 257, 29777, 7509, 5844, 510, 457, 572, 286, 500, 380, 519, 286, 500, 380, 519, 309, 3417, 411, 257, 13, 51514], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 521, "seek": 349800, "start": 3521.0, "end": 3522.0, "text": " Yeah.", "tokens": [51514, 865, 13, 51564], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 522, "seek": 349800, "start": 3523.0, "end": 3524.0, "text": " Yeah.", "tokens": [51614, 865, 13, 51664], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 523, "seek": 349800, "start": 3524.0, "end": 3525.0, "text": " So we'll.", "tokens": [51664, 407, 321, 603, 13, 51714], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 524, "seek": 349800, "start": 3526.0, "end": 3527.0, "text": " Sorry.", "tokens": [51764, 4919, 13, 51814], "temperature": 0.0, "avg_logprob": -0.3375672628713209, "compression_ratio": 1.6177606177606179, "no_speech_prob": 0.09133017808198929}, {"id": 525, "seek": 352700, "start": 3527.0, "end": 3542.0, "text": " CUDA is foundational like every single thing that that you see that's built on top of machine learning and artificial intelligence that allows the GPU to accelerate it is built on CUDA every single every single operation is built on CUDA.", "tokens": [50364, 29777, 7509, 307, 32195, 411, 633, 2167, 551, 300, 300, 291, 536, 300, 311, 3094, 322, 1192, 295, 3479, 2539, 293, 11677, 7599, 300, 4045, 264, 18407, 281, 21341, 309, 307, 3094, 322, 29777, 7509, 633, 2167, 633, 2167, 6916, 307, 3094, 322, 29777, 7509, 13, 51114], "temperature": 0.0, "avg_logprob": -0.11105272769927979, "compression_ratio": 1.7330316742081449, "no_speech_prob": 0.004100021440535784}, {"id": 526, "seek": 352700, "start": 3543.0, "end": 3550.0, "text": " Nobody talks about CUDA anymore because it's so foundational you have to have it to make these operations move quickly on a video graphics card.", "tokens": [51164, 9297, 6686, 466, 29777, 7509, 3602, 570, 309, 311, 370, 32195, 291, 362, 281, 362, 309, 281, 652, 613, 7705, 1286, 2661, 322, 257, 960, 11837, 2920, 13, 51514], "temperature": 0.0, "avg_logprob": -0.11105272769927979, "compression_ratio": 1.7330316742081449, "no_speech_prob": 0.004100021440535784}, {"id": 527, "seek": 355000, "start": 3550.0, "end": 3559.0, "text": " So you know it's really any any anything that you're using to accelerate the training of a neural network the inferencing of a neural network a scene graph.", "tokens": [50364, 407, 291, 458, 309, 311, 534, 604, 604, 1340, 300, 291, 434, 1228, 281, 21341, 264, 3097, 295, 257, 18161, 3209, 264, 13596, 13644, 295, 257, 18161, 3209, 257, 4145, 4295, 13, 50814], "temperature": 0.0, "avg_logprob": -0.1685649738755337, "compression_ratio": 1.6205357142857142, "no_speech_prob": 0.03504687547683716}, {"id": 528, "seek": 355000, "start": 3559.0, "end": 3563.0, "text": " Every single one of those operations is built on the CUDA libraries.", "tokens": [50814, 2048, 2167, 472, 295, 729, 7705, 307, 3094, 322, 264, 29777, 7509, 15148, 13, 51014], "temperature": 0.0, "avg_logprob": -0.1685649738755337, "compression_ratio": 1.6205357142857142, "no_speech_prob": 0.03504687547683716}, {"id": 529, "seek": 355000, "start": 3564.0, "end": 3568.0, "text": " Great great information and may ask the panel here because I know we're at time.", "tokens": [51064, 3769, 869, 1589, 293, 815, 1029, 264, 4831, 510, 570, 286, 458, 321, 434, 412, 565, 13, 51264], "temperature": 0.0, "avg_logprob": -0.1685649738755337, "compression_ratio": 1.6205357142857142, "no_speech_prob": 0.03504687547683716}, {"id": 530, "seek": 355000, "start": 3568.0, "end": 3571.0, "text": " Did you want to hard stop we take a couple of questions.", "tokens": [51264, 2589, 291, 528, 281, 1152, 1590, 321, 747, 257, 1916, 295, 1651, 13, 51414], "temperature": 0.0, "avg_logprob": -0.1685649738755337, "compression_ratio": 1.6205357142857142, "no_speech_prob": 0.03504687547683716}, {"id": 531, "seek": 357100, "start": 3572.0, "end": 3573.0, "text": " Okay great.", "tokens": [50414, 1033, 869, 13, 50464], "temperature": 0.0, "avg_logprob": -0.3078603382352032, "compression_ratio": 1.5728155339805825, "no_speech_prob": 0.010248650796711445}, {"id": 532, "seek": 357100, "start": 3573.0, "end": 3581.0, "text": " Okay here's one coming in from <PERSON> who is asking if we can describe their app streaming over Azure has experienced latency etc.", "tokens": [50464, 1033, 510, 311, 472, 1348, 294, 490, 2705, 567, 307, 3365, 498, 321, 393, 6786, 641, 724, 11791, 670, 11969, 575, 6751, 27043, 5183, 13, 50864], "temperature": 0.0, "avg_logprob": -0.3078603382352032, "compression_ratio": 1.5728155339805825, "no_speech_prob": 0.010248650796711445}, {"id": 533, "seek": 357100, "start": 3582.0, "end": 3589.0, "text": " I believe the question means the Ocast the on US kid upstreaming that's available on Azure marketplace.", "tokens": [50914, 286, 1697, 264, 1168, 1355, 264, 422, 3734, 264, 322, 2546, 1636, 33915, 278, 300, 311, 2435, 322, 11969, 19455, 13, 51264], "temperature": 0.0, "avg_logprob": -0.3078603382352032, "compression_ratio": 1.5728155339805825, "no_speech_prob": 0.010248650796711445}, {"id": 534, "seek": 357100, "start": 3590.0, "end": 3591.0, "text": " It is available.", "tokens": [51314, 467, 307, 2435, 13, 51364], "temperature": 0.0, "avg_logprob": -0.3078603382352032, "compression_ratio": 1.5728155339805825, "no_speech_prob": 0.010248650796711445}, {"id": 535, "seek": 357100, "start": 3591.0, "end": 3596.0, "text": " It is a co-sale ready fully transactable marketplace listing.", "tokens": [51364, 467, 307, 257, 598, 12, 82, 1220, 1919, 4498, 46688, 712, 19455, 22161, 13, 51614], "temperature": 0.0, "avg_logprob": -0.3078603382352032, "compression_ratio": 1.5728155339805825, "no_speech_prob": 0.010248650796711445}, {"id": 536, "seek": 359600, "start": 3596.0, "end": 3603.0, "text": " So some of the work that we saw in this in this session is actually using that same Ocast.", "tokens": [50364, 407, 512, 295, 264, 589, 300, 321, 1866, 294, 341, 294, 341, 5481, 307, 767, 1228, 300, 912, 422, 3734, 13, 50714], "temperature": 0.0, "avg_logprob": -0.13551628228389856, "compression_ratio": 1.744, "no_speech_prob": 0.017828604206442833}, {"id": 537, "seek": 359600, "start": 3603.0, "end": 3612.0, "text": " So as a developer you can actually go to Azure marketplace today and get started follow the GitHub repo the same things at what site machine did.", "tokens": [50714, 407, 382, 257, 10754, 291, 393, 767, 352, 281, 11969, 19455, 965, 293, 483, 1409, 1524, 264, 23331, 49040, 264, 912, 721, 412, 437, 3621, 3479, 630, 13, 51164], "temperature": 0.0, "avg_logprob": -0.13551628228389856, "compression_ratio": 1.744, "no_speech_prob": 0.017828604206442833}, {"id": 538, "seek": 359600, "start": 3612.0, "end": 3617.0, "text": " So the work that site machine did was following that ignite repo that drew was talking about.", "tokens": [51164, 407, 264, 589, 300, 3621, 3479, 630, 390, 3480, 300, 49609, 49040, 300, 12804, 390, 1417, 466, 13, 51414], "temperature": 0.0, "avg_logprob": -0.13551628228389856, "compression_ratio": 1.744, "no_speech_prob": 0.017828604206442833}, {"id": 539, "seek": 359600, "start": 3617.0, "end": 3624.0, "text": " So that's the close partnership that we have between NVIDIA and Microsoft to showcase how to get started.", "tokens": [51414, 407, 300, 311, 264, 1998, 9982, 300, 321, 362, 1296, 426, 3958, 6914, 293, 8116, 281, 20388, 577, 281, 483, 1409, 13, 51764], "temperature": 0.0, "avg_logprob": -0.13551628228389856, "compression_ratio": 1.744, "no_speech_prob": 0.017828604206442833}, {"id": 540, "seek": 362400, "start": 3624.0, "end": 3630.0, "text": " How you can actually take those containers the kid upstreaming containers deployed on your own.", "tokens": [50364, 1012, 291, 393, 767, 747, 729, 17089, 264, 1636, 33915, 278, 17089, 17826, 322, 428, 1065, 13, 50664], "temperature": 0.0, "avg_logprob": -0.20679987212758005, "compression_ratio": 1.673728813559322, "no_speech_prob": 0.007835891097784042}, {"id": 541, "seek": 362400, "start": 3630.0, "end": 3637.0, "text": " Kubernetes clusters on Azure leverage the a 10s that are running on Azure commercials and get started.", "tokens": [50664, 23145, 23313, 322, 11969, 13982, 264, 257, 1266, 82, 300, 366, 2614, 322, 11969, 33666, 293, 483, 1409, 13, 51014], "temperature": 0.0, "avg_logprob": -0.20679987212758005, "compression_ratio": 1.673728813559322, "no_speech_prob": 0.007835891097784042}, {"id": 542, "seek": 362400, "start": 3637.0, "end": 3648.0, "text": " So it all basically starts from that kid upstreaming and then build the entire solutions like what's it was talking about with that front end web applications and integrating it into that web app.", "tokens": [51014, 407, 309, 439, 1936, 3719, 490, 300, 1636, 33915, 278, 293, 550, 1322, 264, 2302, 6547, 411, 437, 311, 309, 390, 1417, 466, 365, 300, 1868, 917, 3670, 5821, 293, 26889, 309, 666, 300, 3670, 724, 13, 51564], "temperature": 0.0, "avg_logprob": -0.20679987212758005, "compression_ratio": 1.673728813559322, "no_speech_prob": 0.007835891097784042}, {"id": 543, "seek": 364800, "start": 3648.0, "end": 3650.0, "text": " So it's it's all there.", "tokens": [50364, 407, 309, 311, 309, 311, 439, 456, 13, 50464], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 544, "seek": 364800, "start": 3650.0, "end": 3654.0, "text": " It's all available on that marketplace and get a repo for that we release at ignite.", "tokens": [50464, 467, 311, 439, 2435, 322, 300, 19455, 293, 483, 257, 49040, 337, 300, 321, 4374, 412, 49609, 13, 50664], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 545, "seek": 364800, "start": 3654.0, "end": 3661.0, "text": " I think we should also admire you said that discord will have it so we should also share that GitHub repo as well for any developer to get started today.", "tokens": [50664, 286, 519, 321, 820, 611, 21951, 291, 848, 300, 32989, 486, 362, 309, 370, 321, 820, 611, 2073, 300, 23331, 49040, 382, 731, 337, 604, 10754, 281, 483, 1409, 965, 13, 51014], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 546, "seek": 364800, "start": 3662.0, "end": 3663.0, "text": " Okay, we'll do that.", "tokens": [51064, 1033, 11, 321, 603, 360, 300, 13, 51114], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 547, "seek": 364800, "start": 3663.0, "end": 3665.0, "text": " I think <PERSON> posted that thread in the chat a little earlier.", "tokens": [51114, 286, 519, 42814, 9437, 300, 7207, 294, 264, 5081, 257, 707, 3071, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 548, "seek": 364800, "start": 3665.0, "end": 3667.0, "text": " We'll also add to the description afterwards.", "tokens": [51214, 492, 603, 611, 909, 281, 264, 3855, 10543, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 549, "seek": 364800, "start": 3667.0, "end": 3669.0, "text": " We got another question here from LinkedIn.", "tokens": [51314, 492, 658, 1071, 1168, 510, 490, 20657, 13, 51414], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 550, "seek": 364800, "start": 3669.0, "end": 3673.0, "text": " <PERSON>, this is probably great for everybody, but definitely <PERSON><PERSON><PERSON>.", "tokens": [51414, 16442, 11, 341, 307, 1391, 869, 337, 2201, 11, 457, 2138, 460, 12779, 71, 13, 51614], "temperature": 0.0, "avg_logprob": -0.1961251506655235, "compression_ratio": 1.6578947368421053, "no_speech_prob": 0.031219149008393288}, {"id": 551, "seek": 367300, "start": 3673.0, "end": 3684.0, "text": " This is, you know, obviously when when when we have business folks from different companies watching these kinds of topics and they realize, you know what, I should take a look at omniverse or take a look at open USD.", "tokens": [50364, 639, 307, 11, 291, 458, 11, 2745, 562, 562, 562, 321, 362, 1606, 4024, 490, 819, 3431, 1976, 613, 3685, 295, 8378, 293, 436, 4325, 11, 291, 458, 437, 11, 286, 820, 747, 257, 574, 412, 36874, 5376, 420, 747, 257, 574, 412, 1269, 24375, 13, 50914], "temperature": 0.0, "avg_logprob": -0.18861189755526456, "compression_ratio": 1.705223880597015, "no_speech_prob": 0.06623081117868423}, {"id": 552, "seek": 367300, "start": 3685.0, "end": 3686.0, "text": " What would you say, <PERSON><PERSON><PERSON>?", "tokens": [50964, 708, 576, 291, 584, 11, 460, 12779, 71, 30, 51014], "temperature": 0.0, "avg_logprob": -0.18861189755526456, "compression_ratio": 1.705223880597015, "no_speech_prob": 0.06623081117868423}, {"id": 553, "seek": 367300, "start": 3686.0, "end": 3693.0, "text": " What what's what what is your pitch to have these have these leaders actually take a serious look at adoption.", "tokens": [51014, 708, 437, 311, 437, 437, 307, 428, 7293, 281, 362, 613, 362, 613, 3523, 767, 747, 257, 3156, 574, 412, 19215, 13, 51364], "temperature": 0.0, "avg_logprob": -0.18861189755526456, "compression_ratio": 1.705223880597015, "no_speech_prob": 0.06623081117868423}, {"id": 554, "seek": 367300, "start": 3693.0, "end": 3699.0, "text": " So I think I think it's all I believe channel kind of laid this out at the beginning of the session.", "tokens": [51364, 407, 286, 519, 286, 519, 309, 311, 439, 286, 1697, 2269, 733, 295, 9897, 341, 484, 412, 264, 2863, 295, 264, 5481, 13, 51664], "temperature": 0.0, "avg_logprob": -0.18861189755526456, "compression_ratio": 1.705223880597015, "no_speech_prob": 0.06623081117868423}, {"id": 555, "seek": 369900, "start": 3699.0, "end": 3707.0, "text": " It all starts with the the KPI, the business KPI and the real like immediate material impact.", "tokens": [50364, 467, 439, 3719, 365, 264, 264, 591, 31701, 11, 264, 1606, 591, 31701, 293, 264, 957, 411, 11629, 2527, 2712, 13, 50764], "temperature": 0.0, "avg_logprob": -0.1468051321366254, "compression_ratio": 1.555, "no_speech_prob": 0.018957044929265976}, {"id": 556, "seek": 369900, "start": 3707.0, "end": 3717.0, "text": " So the modeling line use case that <PERSON> was talking about, there was an operational efficiency gain or throughput improvement or yield improvement.", "tokens": [50764, 407, 264, 15983, 1622, 764, 1389, 300, 13754, 390, 1417, 466, 11, 456, 390, 364, 16607, 10493, 6052, 420, 44629, 10444, 420, 11257, 10444, 13, 51264], "temperature": 0.0, "avg_logprob": -0.1468051321366254, "compression_ratio": 1.555, "no_speech_prob": 0.018957044929265976}, {"id": 557, "seek": 369900, "start": 3717.0, "end": 3720.0, "text": " That's where that's where this entire kind of journey started off.", "tokens": [51264, 663, 311, 689, 300, 311, 689, 341, 2302, 733, 295, 4671, 1409, 766, 13, 51414], "temperature": 0.0, "avg_logprob": -0.1468051321366254, "compression_ratio": 1.555, "no_speech_prob": 0.018957044929265976}, {"id": 558, "seek": 372000, "start": 3720.0, "end": 3729.0, "text": " So try to understand how what that K<PERSON> would look like and it can be it can be related to going back to what <PERSON> was talking about.", "tokens": [50364, 407, 853, 281, 1223, 577, 437, 300, 591, 31701, 576, 574, 411, 293, 309, 393, 312, 309, 393, 312, 4077, 281, 516, 646, 281, 437, 17809, 390, 1417, 466, 13, 50814], "temperature": 0.0, "avg_logprob": -0.09812325444714777, "compression_ratio": 1.6118143459915613, "no_speech_prob": 0.04819405823945999}, {"id": 559, "seek": 372000, "start": 3729.0, "end": 3734.0, "text": " If you take the filler simulation example, there are subject matter experts who have been doing that for decades.", "tokens": [50814, 759, 291, 747, 264, 34676, 16575, 1365, 11, 456, 366, 3983, 1871, 8572, 567, 362, 668, 884, 300, 337, 7878, 13, 51064], "temperature": 0.0, "avg_logprob": -0.09812325444714777, "compression_ratio": 1.6118143459915613, "no_speech_prob": 0.04819405823945999}, {"id": 560, "seek": 372000, "start": 3734.0, "end": 3742.0, "text": " So but if they are looking at a specific business KPI that they want to hit through a simulation scenario, then then start with that.", "tokens": [51064, 407, 457, 498, 436, 366, 1237, 412, 257, 2685, 1606, 591, 31701, 300, 436, 528, 281, 2045, 807, 257, 16575, 9005, 11, 550, 550, 722, 365, 300, 13, 51464], "temperature": 0.0, "avg_logprob": -0.09812325444714777, "compression_ratio": 1.6118143459915613, "no_speech_prob": 0.04819405823945999}, {"id": 561, "seek": 374200, "start": 3742.0, "end": 3751.0, "text": " See where all of these new new technologies and the digital transformation, the digital twin, the simulation workflows are going to actually cause an impact.", "tokens": [50364, 3008, 689, 439, 295, 613, 777, 777, 7943, 293, 264, 4562, 9887, 11, 264, 4562, 18397, 11, 264, 16575, 43461, 366, 516, 281, 767, 3082, 364, 2712, 13, 50814], "temperature": 0.0, "avg_logprob": -0.1712976430917715, "compression_ratio": 1.6596638655462186, "no_speech_prob": 0.029442068189382553}, {"id": 562, "seek": 374200, "start": 3751.0, "end": 3766.0, "text": " In case of psych machine, they zeroed in on an OE improvement, took their platform to Microsoft and then media technology took the help from kinetic vision and showcase that example with a double digit operational efficiency improvement.", "tokens": [50814, 682, 1389, 295, 4681, 3479, 11, 436, 4018, 292, 294, 322, 364, 422, 36, 10444, 11, 1890, 641, 3663, 281, 8116, 293, 550, 3021, 2899, 1890, 264, 854, 490, 27135, 5201, 293, 20388, 300, 1365, 365, 257, 3834, 14293, 16607, 10493, 10444, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1712976430917715, "compression_ratio": 1.6596638655462186, "no_speech_prob": 0.029442068189382553}, {"id": 563, "seek": 376600, "start": 3766.0, "end": 3770.0, "text": " If I remember right, <PERSON>, I think it was like a 10 to 15 percent.", "tokens": [50364, 759, 286, 1604, 558, 11, 13754, 11, 286, 519, 309, 390, 411, 257, 1266, 281, 2119, 3043, 13, 50564], "temperature": 0.0, "avg_logprob": -0.15920196879993787, "compression_ratio": 1.8786610878661087, "no_speech_prob": 0.1550496369600296}, {"id": 564, "seek": 376600, "start": 3770.0, "end": 3781.0, "text": " You guys were targeting like one to two percent and that itself was like a big impact, both top line and bottom line, but they were able to showcase a double digit impact of 10 to 15 percent on top line and bottom line.", "tokens": [50564, 509, 1074, 645, 17918, 411, 472, 281, 732, 3043, 293, 300, 2564, 390, 411, 257, 955, 2712, 11, 1293, 1192, 1622, 293, 2767, 1622, 11, 457, 436, 645, 1075, 281, 20388, 257, 3834, 14293, 2712, 295, 1266, 281, 2119, 3043, 322, 1192, 1622, 293, 2767, 1622, 13, 51114], "temperature": 0.0, "avg_logprob": -0.15920196879993787, "compression_ratio": 1.8786610878661087, "no_speech_prob": 0.1550496369600296}, {"id": 565, "seek": 376600, "start": 3781.0, "end": 3786.0, "text": " Or I wouldn't say top line of bottom line, but the operational efficiency gain that has impact both top line and bottom line.", "tokens": [51114, 1610, 286, 2759, 380, 584, 1192, 1622, 295, 2767, 1622, 11, 457, 264, 16607, 10493, 6052, 300, 575, 2712, 1293, 1192, 1622, 293, 2767, 1622, 13, 51364], "temperature": 0.0, "avg_logprob": -0.15920196879993787, "compression_ratio": 1.8786610878661087, "no_speech_prob": 0.1550496369600296}, {"id": 566, "seek": 376600, "start": 3786.0, "end": 3788.0, "text": " So that's that's where it starts.", "tokens": [51364, 407, 300, 311, 300, 311, 689, 309, 3719, 13, 51464], "temperature": 0.0, "avg_logprob": -0.15920196879993787, "compression_ratio": 1.8786610878661087, "no_speech_prob": 0.1550496369600296}, {"id": 567, "seek": 378800, "start": 3788.0, "end": 3795.0, "text": " It always starts with the business value and the KPI and then start with that one use case.", "tokens": [50364, 467, 1009, 3719, 365, 264, 1606, 2158, 293, 264, 591, 31701, 293, 550, 722, 365, 300, 472, 764, 1389, 13, 50714], "temperature": 0.0, "avg_logprob": -0.10937888405539772, "compression_ratio": 1.8453237410071943, "no_speech_prob": 0.21030676364898682}, {"id": 568, "seek": 378800, "start": 3795.0, "end": 3798.0, "text": " Look at your existing stack of technologies that you have.", "tokens": [50714, 2053, 412, 428, 6741, 8630, 295, 7943, 300, 291, 362, 13, 50864], "temperature": 0.0, "avg_logprob": -0.10937888405539772, "compression_ratio": 1.8453237410071943, "no_speech_prob": 0.21030676364898682}, {"id": 569, "seek": 378800, "start": 3798.0, "end": 3807.0, "text": " See what the gap exists, adopt the technologies to the right level rather than like throwing everything out of the window and start from scratch.", "tokens": [50864, 3008, 437, 264, 7417, 8198, 11, 6878, 264, 7943, 281, 264, 558, 1496, 2831, 813, 411, 10238, 1203, 484, 295, 264, 4910, 293, 722, 490, 8459, 13, 51314], "temperature": 0.0, "avg_logprob": -0.10937888405539772, "compression_ratio": 1.8453237410071943, "no_speech_prob": 0.21030676364898682}, {"id": 570, "seek": 378800, "start": 3807.0, "end": 3808.0, "text": " That never works.", "tokens": [51314, 663, 1128, 1985, 13, 51364], "temperature": 0.0, "avg_logprob": -0.10937888405539772, "compression_ratio": 1.8453237410071943, "no_speech_prob": 0.21030676364898682}, {"id": 571, "seek": 378800, "start": 3808.0, "end": 3817.0, "text": " It's way too expensive and then see the incremental gain and then keep expanding from there from one line to multiple lines from one factory to multiple factories and see whether that really sticks.", "tokens": [51364, 467, 311, 636, 886, 5124, 293, 550, 536, 264, 35759, 6052, 293, 550, 1066, 14702, 490, 456, 490, 472, 1622, 281, 3866, 3876, 490, 472, 9265, 281, 3866, 24813, 293, 536, 1968, 300, 534, 12518, 13, 51814], "temperature": 0.0, "avg_logprob": -0.10937888405539772, "compression_ratio": 1.8453237410071943, "no_speech_prob": 0.21030676364898682}, {"id": 572, "seek": 381700, "start": 3817.0, "end": 3820.0, "text": " So that's that's how I think I would think about.", "tokens": [50364, 407, 300, 311, 300, 311, 577, 286, 519, 286, 576, 519, 466, 13, 50514], "temperature": 0.0, "avg_logprob": -0.1346701600334861, "compression_ratio": 1.634020618556701, "no_speech_prob": 0.004759101662784815}, {"id": 573, "seek": 381700, "start": 3820.0, "end": 3825.0, "text": " I'm open to other inputs and ideas from folks here.", "tokens": [50514, 286, 478, 1269, 281, 661, 15743, 293, 3487, 490, 4024, 510, 13, 50764], "temperature": 0.0, "avg_logprob": -0.1346701600334861, "compression_ratio": 1.634020618556701, "no_speech_prob": 0.004759101662784815}, {"id": 574, "seek": 381700, "start": 3825.0, "end": 3829.0, "text": " Oh, I've always got a hot take.", "tokens": [50764, 876, 11, 286, 600, 1009, 658, 257, 2368, 747, 13, 50964], "temperature": 0.0, "avg_logprob": -0.1346701600334861, "compression_ratio": 1.634020618556701, "no_speech_prob": 0.004759101662784815}, {"id": 575, "seek": 381700, "start": 3829.0, "end": 3839.0, "text": " So, you know, for organizations when you're thinking about your omniverse investment, you know, omniverse is an, you know, an entire ecosystem.", "tokens": [50964, 407, 11, 291, 458, 11, 337, 6150, 562, 291, 434, 1953, 466, 428, 36874, 5376, 6078, 11, 291, 458, 11, 36874, 5376, 307, 364, 11, 291, 458, 11, 364, 2302, 11311, 13, 51464], "temperature": 0.0, "avg_logprob": -0.1346701600334861, "compression_ratio": 1.634020618556701, "no_speech_prob": 0.004759101662784815}, {"id": 576, "seek": 381700, "start": 3839.0, "end": 3842.0, "text": " So you can engage it in different ways.", "tokens": [51464, 407, 291, 393, 4683, 309, 294, 819, 2098, 13, 51614], "temperature": 0.0, "avg_logprob": -0.1346701600334861, "compression_ratio": 1.634020618556701, "no_speech_prob": 0.004759101662784815}, {"id": 577, "seek": 384200, "start": 3842.0, "end": 3853.0, "text": " This engagement through an ISV like site machine is actually a very low friction engagement because your organization probably already has Azure resources.", "tokens": [50364, 639, 8742, 807, 364, 6205, 53, 411, 3621, 3479, 307, 767, 257, 588, 2295, 17710, 8742, 570, 428, 4475, 1391, 1217, 575, 11969, 3593, 13, 50914], "temperature": 0.0, "avg_logprob": -0.11181446484157018, "compression_ratio": 1.5925925925925926, "no_speech_prob": 0.05883362516760826}, {"id": 578, "seek": 384200, "start": 3853.0, "end": 3857.0, "text": " And now that Microsoft has made this capability available through Azure.", "tokens": [50914, 400, 586, 300, 8116, 575, 1027, 341, 13759, 2435, 807, 11969, 13, 51114], "temperature": 0.0, "avg_logprob": -0.11181446484157018, "compression_ratio": 1.5925925925925926, "no_speech_prob": 0.05883362516760826}, {"id": 579, "seek": 384200, "start": 3857.0, "end": 3860.0, "text": " Now this is just a SaaS purchase.", "tokens": [51114, 823, 341, 307, 445, 257, 49733, 8110, 13, 51264], "temperature": 0.0, "avg_logprob": -0.11181446484157018, "compression_ratio": 1.5925925925925926, "no_speech_prob": 0.05883362516760826}, {"id": 580, "seek": 384200, "start": 3860.0, "end": 3866.0, "text": " You're just calling up a company like site machine and you're saying, Hey, I would like to use your software to see my game.", "tokens": [51264, 509, 434, 445, 5141, 493, 257, 2237, 411, 3621, 3479, 293, 291, 434, 1566, 11, 1911, 11, 286, 576, 411, 281, 764, 428, 4722, 281, 536, 452, 1216, 13, 51564], "temperature": 0.0, "avg_logprob": -0.11181446484157018, "compression_ratio": 1.5925925925925926, "no_speech_prob": 0.05883362516760826}, {"id": 581, "seek": 386600, "start": 3867.0, "end": 3873.0, "text": " And then you're, and then it's utilizing either the SaaS platform or your cloud Azure private tenant.", "tokens": [50414, 400, 550, 291, 434, 11, 293, 550, 309, 311, 26775, 2139, 264, 49733, 3663, 420, 428, 4588, 11969, 4551, 31000, 13, 50714], "temperature": 0.0, "avg_logprob": -0.11106610071091425, "compression_ratio": 1.6245353159851301, "no_speech_prob": 0.6974167227745056}, {"id": 582, "seek": 386600, "start": 3873.0, "end": 3878.0, "text": " And that's a very low, that's a very low friction way to invest in omniverse.", "tokens": [50714, 400, 300, 311, 257, 588, 2295, 11, 300, 311, 257, 588, 2295, 17710, 636, 281, 1963, 294, 36874, 5376, 13, 50964], "temperature": 0.0, "avg_logprob": -0.11106610071091425, "compression_ratio": 1.6245353159851301, "no_speech_prob": 0.6974167227745056}, {"id": 583, "seek": 386600, "start": 3878.0, "end": 3884.0, "text": " You may be a different type of organization and say, Hey, we have a very unique manufacturing process.", "tokens": [50964, 509, 815, 312, 257, 819, 2010, 295, 4475, 293, 584, 11, 1911, 11, 321, 362, 257, 588, 3845, 11096, 1399, 13, 51264], "temperature": 0.0, "avg_logprob": -0.11106610071091425, "compression_ratio": 1.6245353159851301, "no_speech_prob": 0.6974167227745056}, {"id": 584, "seek": 386600, "start": 3884.0, "end": 3888.0, "text": " A site machine doesn't meet all of our requirements.", "tokens": [51264, 316, 3621, 3479, 1177, 380, 1677, 439, 295, 527, 7728, 13, 51464], "temperature": 0.0, "avg_logprob": -0.11106610071091425, "compression_ratio": 1.6245353159851301, "no_speech_prob": 0.6974167227745056}, {"id": 585, "seek": 386600, "start": 3888.0, "end": 3894.0, "text": " We want to build our own applications to be a technology leader in our space and lead everybody else.", "tokens": [51464, 492, 528, 281, 1322, 527, 1065, 5821, 281, 312, 257, 2899, 5263, 294, 527, 1901, 293, 1477, 2201, 1646, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11106610071091425, "compression_ratio": 1.6245353159851301, "no_speech_prob": 0.6974167227745056}, {"id": 586, "seek": 389400, "start": 3894.0, "end": 3904.0, "text": " That's where then you would make the type of investment with omniverse via omniverse cloud or OBE to bring your developers and actually have them building their own applications.", "tokens": [50364, 663, 311, 689, 550, 291, 576, 652, 264, 2010, 295, 6078, 365, 36874, 5376, 5766, 36874, 5376, 4588, 420, 422, 10207, 281, 1565, 428, 8849, 293, 767, 362, 552, 2390, 641, 1065, 5821, 13, 50864], "temperature": 0.0, "avg_logprob": -0.06905132640491832, "compression_ratio": 1.7906137184115523, "no_speech_prob": 0.005677246022969484}, {"id": 587, "seek": 389400, "start": 3904.0, "end": 3913.0, "text": " If you're an organization who can build your own applications and you want to be a leader in your technology, then that's how you would invest in omniverse.", "tokens": [50864, 759, 291, 434, 364, 4475, 567, 393, 1322, 428, 1065, 5821, 293, 291, 528, 281, 312, 257, 5263, 294, 428, 2899, 11, 550, 300, 311, 577, 291, 576, 1963, 294, 36874, 5376, 13, 51314], "temperature": 0.0, "avg_logprob": -0.06905132640491832, "compression_ratio": 1.7906137184115523, "no_speech_prob": 0.005677246022969484}, {"id": 588, "seek": 389400, "start": 3913.0, "end": 3915.0, "text": " And that's like a strategic decision.", "tokens": [51314, 400, 300, 311, 411, 257, 10924, 3537, 13, 51414], "temperature": 0.0, "avg_logprob": -0.06905132640491832, "compression_ratio": 1.7906137184115523, "no_speech_prob": 0.005677246022969484}, {"id": 589, "seek": 389400, "start": 3915.0, "end": 3923.0, "text": " Are we a developer of applications here within our company or are we a procurer of ISV applications to solve our problems?", "tokens": [51414, 2014, 321, 257, 10754, 295, 5821, 510, 1951, 527, 2237, 420, 366, 321, 257, 9510, 9858, 295, 6205, 53, 5821, 281, 5039, 527, 2740, 30, 51814], "temperature": 0.0, "avg_logprob": -0.06905132640491832, "compression_ratio": 1.7906137184115523, "no_speech_prob": 0.005677246022969484}, {"id": 590, "seek": 392300, "start": 3923.0, "end": 3928.0, "text": " And that's a key decision point between leaders. Are you developing or are you procuring software?", "tokens": [50364, 400, 300, 311, 257, 2141, 3537, 935, 1296, 3523, 13, 2014, 291, 6416, 420, 366, 291, 9510, 1345, 4722, 30, 50614], "temperature": 0.0, "avg_logprob": -0.1846935749053955, "compression_ratio": 1.5769230769230769, "no_speech_prob": 0.015249484218657017}, {"id": 591, "seek": 392300, "start": 3928.0, "end": 3931.0, "text": " That's great, great, great context. Amazing.", "tokens": [50614, 663, 311, 869, 11, 869, 11, 869, 4319, 13, 14165, 13, 50764], "temperature": 0.0, "avg_logprob": -0.1846935749053955, "compression_ratio": 1.5769230769230769, "no_speech_prob": 0.015249484218657017}, {"id": 592, "seek": 392300, "start": 3931.0, "end": 3933.0, "text": " Okay, we got another question here.", "tokens": [50764, 1033, 11, 321, 658, 1071, 1168, 510, 13, 50864], "temperature": 0.0, "avg_logprob": -0.1846935749053955, "compression_ratio": 1.5769230769230769, "no_speech_prob": 0.015249484218657017}, {"id": 593, "seek": 392300, "start": 3933.0, "end": 3939.0, "text": " And actually, <PERSON>, enter it quickly if you can, but I think you covered this earlier.", "tokens": [50864, 400, 767, 11, 17809, 11, 3242, 309, 2661, 498, 291, 393, 11, 457, 286, 519, 291, 5343, 341, 3071, 13, 51164], "temperature": 0.0, "avg_logprob": -0.1846935749053955, "compression_ratio": 1.5769230769230769, "no_speech_prob": 0.015249484218657017}, {"id": 594, "seek": 392300, "start": 3939.0, "end": 3946.0, "text": " How helpful is photorealistic effects when used by GUI engineers or technicians in practice?", "tokens": [51164, 1012, 4961, 307, 2409, 418, 304, 3142, 5065, 562, 1143, 538, 17917, 40, 11955, 420, 40885, 294, 3124, 30, 51514], "temperature": 0.0, "avg_logprob": -0.1846935749053955, "compression_ratio": 1.5769230769230769, "no_speech_prob": 0.015249484218657017}, {"id": 595, "seek": 392300, "start": 3946.0, "end": 3951.0, "text": " Yeah, I mean, I think about this as communication, right? Who are you communicating with?", "tokens": [51514, 865, 11, 286, 914, 11, 286, 519, 466, 341, 382, 6101, 11, 558, 30, 2102, 366, 291, 17559, 365, 30, 51764], "temperature": 0.0, "avg_logprob": -0.1846935749053955, "compression_ratio": 1.5769230769230769, "no_speech_prob": 0.015249484218657017}, {"id": 596, "seek": 395100, "start": 3951.0, "end": 3961.0, "text": " The more photorealistic your output, then the broader your collaboration is with all in your organization.", "tokens": [50364, 440, 544, 2409, 418, 304, 3142, 428, 5598, 11, 550, 264, 13227, 428, 9363, 307, 365, 439, 294, 428, 4475, 13, 50864], "temperature": 0.0, "avg_logprob": -0.055849256186649716, "compression_ratio": 1.5443037974683544, "no_speech_prob": 0.0094743762165308}, {"id": 597, "seek": 395100, "start": 3961.0, "end": 3969.0, "text": " Yes, if you're engineer to engineer, you may not need photorealistic effects to be able to solve a certain problem and change a variable.", "tokens": [50864, 1079, 11, 498, 291, 434, 11403, 281, 11403, 11, 291, 815, 406, 643, 2409, 418, 304, 3142, 5065, 281, 312, 1075, 281, 5039, 257, 1629, 1154, 293, 1319, 257, 7006, 13, 51264], "temperature": 0.0, "avg_logprob": -0.055849256186649716, "compression_ratio": 1.5443037974683544, "no_speech_prob": 0.0094743762165308}, {"id": 598, "seek": 396900, "start": 3969.0, "end": 3983.0, "text": " But if you are making a big investment and you need to communicate to leaders on opening up the budget to go ahead and make some huge cost savings for the company,", "tokens": [50364, 583, 498, 291, 366, 1455, 257, 955, 6078, 293, 291, 643, 281, 7890, 281, 3523, 322, 5193, 493, 264, 4706, 281, 352, 2286, 293, 652, 512, 2603, 2063, 13454, 337, 264, 2237, 11, 51064], "temperature": 0.0, "avg_logprob": -0.07847933011634328, "compression_ratio": 1.565217391304348, "no_speech_prob": 0.508398711681366}, {"id": 599, "seek": 396900, "start": 3983.0, "end": 3987.0, "text": " you probably do need some kind of photorealistic output.", "tokens": [51064, 291, 1391, 360, 643, 512, 733, 295, 2409, 418, 304, 3142, 5598, 13, 51264], "temperature": 0.0, "avg_logprob": -0.07847933011634328, "compression_ratio": 1.565217391304348, "no_speech_prob": 0.508398711681366}, {"id": 600, "seek": 396900, "start": 3987.0, "end": 3991.0, "text": " And people, NVIDIA is going to give it to us for free, right?", "tokens": [51264, 400, 561, 11, 426, 3958, 6914, 307, 516, 281, 976, 309, 281, 505, 337, 1737, 11, 558, 30, 51464], "temperature": 0.0, "avg_logprob": -0.07847933011634328, "compression_ratio": 1.565217391304348, "no_speech_prob": 0.508398711681366}, {"id": 601, "seek": 396900, "start": 3991.0, "end": 3994.0, "text": " I'm not for free. You got to buy GPUs, okay?", "tokens": [51464, 286, 478, 406, 337, 1737, 13, 509, 658, 281, 2256, 18407, 82, 11, 1392, 30, 51614], "temperature": 0.0, "avg_logprob": -0.07847933011634328, "compression_ratio": 1.565217391304348, "no_speech_prob": 0.508398711681366}, {"id": 602, "seek": 396900, "start": 3994.0, "end": 3998.0, "text": " But the highly photorealistic effects should all be free in the end.", "tokens": [51614, 583, 264, 5405, 2409, 418, 304, 3142, 5065, 820, 439, 312, 1737, 294, 264, 917, 13, 51814], "temperature": 0.0, "avg_logprob": -0.07847933011634328, "compression_ratio": 1.565217391304348, "no_speech_prob": 0.508398711681366}, {"id": 603, "seek": 399800, "start": 3998.0, "end": 4002.0, "text": " Yes, there's some friction now, but that friction is going to decrease over time.", "tokens": [50364, 1079, 11, 456, 311, 512, 17710, 586, 11, 457, 300, 17710, 307, 516, 281, 11514, 670, 565, 13, 50564], "temperature": 0.0, "avg_logprob": -0.07568105784329501, "compression_ratio": 1.5689655172413792, "no_speech_prob": 0.0074461838230490685}, {"id": 604, "seek": 399800, "start": 4002.0, "end": 4006.0, "text": " Everything, you know, compute becomes cheaper over time.", "tokens": [50564, 5471, 11, 291, 458, 11, 14722, 3643, 12284, 670, 565, 13, 50764], "temperature": 0.0, "avg_logprob": -0.07568105784329501, "compression_ratio": 1.5689655172413792, "no_speech_prob": 0.0074461838230490685}, {"id": 605, "seek": 399800, "start": 4006.0, "end": 4014.0, "text": " And so I do think it's very important, especially in creating these connections through your organization to get real work done.", "tokens": [50764, 400, 370, 286, 360, 519, 309, 311, 588, 1021, 11, 2318, 294, 4084, 613, 9271, 807, 428, 4475, 281, 483, 957, 589, 1096, 13, 51164], "temperature": 0.0, "avg_logprob": -0.07568105784329501, "compression_ratio": 1.5689655172413792, "no_speech_prob": 0.0074461838230490685}, {"id": 606, "seek": 399800, "start": 4014.0, "end": 4023.0, "text": " And the photorealism to <PERSON>'s point, today it feels like it's a human-to-machine interaction,", "tokens": [51164, 400, 264, 2409, 418, 304, 1434, 281, 17809, 311, 935, 11, 965, 309, 3417, 411, 309, 311, 257, 1952, 12, 1353, 12, 46061, 9285, 11, 51614], "temperature": 0.0, "avg_logprob": -0.07568105784329501, "compression_ratio": 1.5689655172413792, "no_speech_prob": 0.0074461838230490685}, {"id": 607, "seek": 402300, "start": 4023.0, "end": 4028.0, "text": " but the future is actually towards massive amount of automations through physical AI.", "tokens": [50364, 457, 264, 2027, 307, 767, 3030, 5994, 2372, 295, 3553, 763, 807, 4001, 7318, 13, 50614], "temperature": 0.0, "avg_logprob": -0.09833273206438337, "compression_ratio": 1.5868725868725868, "no_speech_prob": 0.025331979617476463}, {"id": 608, "seek": 402300, "start": 4028.0, "end": 4031.0, "text": " It's a lot of machine-to-machine training.", "tokens": [50614, 467, 311, 257, 688, 295, 3479, 12, 1353, 12, 46061, 3097, 13, 50764], "temperature": 0.0, "avg_logprob": -0.09833273206438337, "compression_ratio": 1.5868725868725868, "no_speech_prob": 0.025331979617476463}, {"id": 609, "seek": 402300, "start": 4031.0, "end": 4037.0, "text": " So for a robot to really train and learn, it needs that physical accuracy.", "tokens": [50764, 407, 337, 257, 7881, 281, 534, 3847, 293, 1466, 11, 309, 2203, 300, 4001, 14170, 13, 51064], "temperature": 0.0, "avg_logprob": -0.09833273206438337, "compression_ratio": 1.5868725868725868, "no_speech_prob": 0.025331979617476463}, {"id": 610, "seek": 402300, "start": 4037.0, "end": 4040.0, "text": " And that's why photorealism is super important.", "tokens": [51064, 400, 300, 311, 983, 2409, 418, 304, 1434, 307, 1687, 1021, 13, 51214], "temperature": 0.0, "avg_logprob": -0.09833273206438337, "compression_ratio": 1.5868725868725868, "no_speech_prob": 0.025331979617476463}, {"id": 611, "seek": 402300, "start": 4040.0, "end": 4045.0, "text": " And when we're building assets, <PERSON> and I have had this conversation many times.", "tokens": [51214, 400, 562, 321, 434, 2390, 9769, 11, 17809, 293, 286, 362, 632, 341, 3761, 867, 1413, 13, 51464], "temperature": 0.0, "avg_logprob": -0.09833273206438337, "compression_ratio": 1.5868725868725868, "no_speech_prob": 0.025331979617476463}, {"id": 612, "seek": 402300, "start": 4045.0, "end": 4049.0, "text": " When we're building these 3D-USD assets, today it's about operational twin.", "tokens": [51464, 1133, 321, 434, 2390, 613, 805, 35, 12, 3447, 35, 9769, 11, 965, 309, 311, 466, 16607, 18397, 13, 51664], "temperature": 0.0, "avg_logprob": -0.09833273206438337, "compression_ratio": 1.5868725868725868, "no_speech_prob": 0.025331979617476463}, {"id": 613, "seek": 404900, "start": 4049.0, "end": 4053.0, "text": " But really, that's just the stepping stone.", "tokens": [50364, 583, 534, 11, 300, 311, 445, 264, 16821, 7581, 13, 50564], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 614, "seek": 404900, "start": 4053.0, "end": 4057.0, "text": " The real goal is to drive through that entire journey of operational simulation", "tokens": [50564, 440, 957, 3387, 307, 281, 3332, 807, 300, 2302, 4671, 295, 16607, 16575, 50764], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 615, "seek": 404900, "start": 4057.0, "end": 4061.0, "text": " and ultimately for physical AI and robotics, right?", "tokens": [50764, 293, 6284, 337, 4001, 7318, 293, 34145, 11, 558, 30, 50964], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 616, "seek": 404900, "start": 4061.0, "end": 4064.0, "text": " And when you're thinking about that journey, you really need that photorealism", "tokens": [50964, 400, 562, 291, 434, 1953, 466, 300, 4671, 11, 291, 534, 643, 300, 2409, 418, 304, 1434, 51114], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 617, "seek": 404900, "start": 4064.0, "end": 4067.0, "text": " and really high-fidelity, accurate USD assets.", "tokens": [51114, 293, 534, 1090, 12, 69, 33343, 11, 8559, 24375, 9769, 13, 51264], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 618, "seek": 404900, "start": 4067.0, "end": 4071.0, "text": " Yeah, we should do another hour on synthetic training data for vision systems.", "tokens": [51264, 865, 11, 321, 820, 360, 1071, 1773, 322, 23420, 3097, 1412, 337, 5201, 3652, 13, 51464], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 619, "seek": 404900, "start": 4071.0, "end": 4072.0, "text": " Exactly.", "tokens": [51464, 7587, 13, 51514], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 620, "seek": 404900, "start": 4072.0, "end": 4075.0, "text": " Yeah, that is really important. That's the technology side.", "tokens": [51514, 865, 11, 300, 307, 534, 1021, 13, 663, 311, 264, 2899, 1252, 13, 51664], "temperature": 0.0, "avg_logprob": -0.07441303708137723, "compression_ratio": 1.597864768683274, "no_speech_prob": 0.03146742656826973}, {"id": 621, "seek": 407500, "start": 4075.0, "end": 4078.0, "text": " I'm always trying to cover the technology side and the human side.", "tokens": [50364, 286, 478, 1009, 1382, 281, 2060, 264, 2899, 1252, 293, 264, 1952, 1252, 13, 50514], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 622, "seek": 407500, "start": 4078.0, "end": 4083.0, "text": " So sorry, I shouldn't have left that one off. That's like a huge, huge thing.", "tokens": [50514, 407, 2597, 11, 286, 4659, 380, 362, 1411, 300, 472, 766, 13, 663, 311, 411, 257, 2603, 11, 2603, 551, 13, 50764], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 623, "seek": 407500, "start": 4083.0, "end": 4085.0, "text": " Well, this is an unrelated note.", "tokens": [50764, 1042, 11, 341, 307, 364, 38967, 3637, 13, 50864], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 624, "seek": 407500, "start": 4085.0, "end": 4091.0, "text": " Curious how one decides optimal resolutions of point clouds that can be fused with realistic data.", "tokens": [50864, 7907, 851, 577, 472, 14898, 16252, 32179, 295, 935, 12193, 300, 393, 312, 283, 4717, 365, 12465, 1412, 13, 51164], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 625, "seek": 407500, "start": 4091.0, "end": 4095.0, "text": " Do you have any thoughts on this one?", "tokens": [51164, 1144, 291, 362, 604, 4598, 322, 341, 472, 30, 51364], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 626, "seek": 407500, "start": 4095.0, "end": 4098.0, "text": " Yeah, these are all...", "tokens": [51364, 865, 11, 613, 366, 439, 485, 51514], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 627, "seek": 407500, "start": 4098.0, "end": 4103.0, "text": " It's like what's available and fast, right, and can solve the problem.", "tokens": [51514, 467, 311, 411, 437, 311, 2435, 293, 2370, 11, 558, 11, 293, 393, 5039, 264, 1154, 13, 51764], "temperature": 0.0, "avg_logprob": -0.15038599534468217, "compression_ratio": 1.5632183908045978, "no_speech_prob": 0.00489569827914238}, {"id": 628, "seek": 410300, "start": 4103.0, "end": 4108.0, "text": " It's a multi-variable solution and it's always changing over time.", "tokens": [50364, 467, 311, 257, 4825, 12, 34033, 712, 3827, 293, 309, 311, 1009, 4473, 670, 565, 13, 50614], "temperature": 0.0, "avg_logprob": -0.12164192933302659, "compression_ratio": 1.429245283018868, "no_speech_prob": 0.006343517452478409}, {"id": 629, "seek": 410300, "start": 4108.0, "end": 4115.0, "text": " So we've chosen a certain type of resolution because we're talking about, I'll say,", "tokens": [50614, 407, 321, 600, 8614, 257, 1629, 2010, 295, 8669, 570, 321, 434, 1417, 466, 11, 286, 603, 584, 11, 50964], "temperature": 0.0, "avg_logprob": -0.12164192933302659, "compression_ratio": 1.429245283018868, "no_speech_prob": 0.006343517452478409}, {"id": 630, "seek": 410300, "start": 4115.0, "end": 4120.0, "text": " human-scale, facility-scale decisions, right?", "tokens": [50964, 1952, 12, 20033, 11, 8973, 12, 20033, 5327, 11, 558, 30, 51214], "temperature": 0.0, "avg_logprob": -0.12164192933302659, "compression_ratio": 1.429245283018868, "no_speech_prob": 0.006343517452478409}, {"id": 631, "seek": 410300, "start": 4120.0, "end": 4125.0, "text": " We also operate three North Star Industrial CT scanners here.", "tokens": [51214, 492, 611, 9651, 1045, 4067, 5705, 32059, 19529, 795, 25792, 510, 13, 51464], "temperature": 0.0, "avg_logprob": -0.12164192933302659, "compression_ratio": 1.429245283018868, "no_speech_prob": 0.006343517452478409}, {"id": 632, "seek": 410300, "start": 4125.0, "end": 4127.0, "text": " So that's a completely different technology.", "tokens": [51464, 407, 300, 311, 257, 2584, 819, 2899, 13, 51564], "temperature": 0.0, "avg_logprob": -0.12164192933302659, "compression_ratio": 1.429245283018868, "no_speech_prob": 0.006343517452478409}, {"id": 633, "seek": 412700, "start": 4127.0, "end": 4129.0, "text": " It scans down to the micron level.", "tokens": [50364, 467, 35116, 760, 281, 264, 45094, 1496, 13, 50464], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 634, "seek": 412700, "start": 4129.0, "end": 4139.0, "text": " We can detect the porosity and the femur of a mouse or a void in a solder pad on a BGA.", "tokens": [50464, 492, 393, 5531, 264, 1515, 20373, 293, 264, 4010, 374, 295, 257, 9719, 420, 257, 22009, 294, 257, 38128, 6887, 322, 257, 363, 12570, 13, 50964], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 635, "seek": 412700, "start": 4139.0, "end": 4144.0, "text": " So you really need to use the right technology for the job,", "tokens": [50964, 407, 291, 534, 643, 281, 764, 264, 558, 2899, 337, 264, 1691, 11, 51214], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 636, "seek": 412700, "start": 4144.0, "end": 4146.0, "text": " but it's going to be some mixture of those things, right?", "tokens": [51214, 457, 309, 311, 516, 281, 312, 512, 9925, 295, 729, 721, 11, 558, 30, 51314], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 637, "seek": 412700, "start": 4146.0, "end": 4150.0, "text": " The expense of the technology, the value of the problem you're solving.", "tokens": [51314, 440, 18406, 295, 264, 2899, 11, 264, 2158, 295, 264, 1154, 291, 434, 12606, 13, 51514], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 638, "seek": 412700, "start": 4150.0, "end": 4153.0, "text": " But if you have questions, hey, you know who you can call.", "tokens": [51514, 583, 498, 291, 362, 1651, 11, 4177, 11, 291, 458, 567, 291, 393, 818, 13, 51664], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 639, "seek": 412700, "start": 4153.0, "end": 4155.0, "text": " Yeah, that's great. Well, thank you.", "tokens": [51664, 865, 11, 300, 311, 869, 13, 1042, 11, 1309, 291, 13, 51764], "temperature": 0.0, "avg_logprob": -0.07918451586340228, "compression_ratio": 1.6, "no_speech_prob": 0.3029925227165222}, {"id": 640, "seek": 415500, "start": 4155.0, "end": 4159.0, "text": " It's fascinating. I never thought of a mouse femur before.", "tokens": [50364, 467, 311, 10343, 13, 286, 1128, 1194, 295, 257, 9719, 4010, 374, 949, 13, 50564], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 641, "seek": 415500, "start": 4159.0, "end": 4165.0, "text": " So <PERSON> is asking on LinkedIn about, is this workflow available with academia?", "tokens": [50564, 407, 6781, 307, 3365, 322, 20657, 466, 11, 307, 341, 20993, 2435, 365, 28937, 30, 50864], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 642, "seek": 415500, "start": 4165.0, "end": 4167.0, "text": " What do you mean, of course? Why not?", "tokens": [50864, 708, 360, 291, 914, 11, 295, 1164, 30, 1545, 406, 30, 50964], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 643, "seek": 415500, "start": 4167.0, "end": 4172.0, "text": " We do have an HDR team here actually at NVIDIA that I can help put you in touch with", "tokens": [50964, 492, 360, 362, 364, 29650, 1469, 510, 767, 412, 426, 3958, 6914, 300, 286, 393, 854, 829, 291, 294, 2557, 365, 51214], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 644, "seek": 415500, "start": 4172.0, "end": 4175.0, "text": " if you have specific questions about your school working.", "tokens": [51214, 498, 291, 362, 2685, 1651, 466, 428, 1395, 1364, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 645, "seek": 415500, "start": 4175.0, "end": 4177.0, "text": " But <PERSON><PERSON><PERSON>, I don't know if you have any other thoughts on this.", "tokens": [51364, 583, 460, 12779, 71, 11, 286, 500, 380, 458, 498, 291, 362, 604, 661, 4598, 322, 341, 13, 51464], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 646, "seek": 415500, "start": 4177.0, "end": 4179.0, "text": " No, this is great.", "tokens": [51464, 883, 11, 341, 307, 869, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 647, "seek": 415500, "start": 4179.0, "end": 4182.0, "text": " Thank you for hosting it. Thank you for inviting everybody.", "tokens": [51564, 1044, 291, 337, 16058, 309, 13, 1044, 291, 337, 18202, 2201, 13, 51714], "temperature": 0.0, "avg_logprob": -0.1639049072265625, "compression_ratio": 1.5876288659793814, "no_speech_prob": 0.006541859358549118}, {"id": 648, "seek": 418200, "start": 4182.0, "end": 4185.0, "text": " And thank you Psych Machine for an awesome work.", "tokens": [50364, 400, 1309, 291, 17303, 22155, 337, 364, 3476, 589, 13, 50514], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 649, "seek": 418200, "start": 4185.0, "end": 4191.0, "text": " I know we had a really short ramp of getting all of this integrated up and running in six months.", "tokens": [50514, 286, 458, 321, 632, 257, 534, 2099, 12428, 295, 1242, 439, 295, 341, 10919, 493, 293, 2614, 294, 2309, 2493, 13, 50814], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 650, "seek": 418200, "start": 4191.0, "end": 4194.0, "text": " Thank you Kinetic Vision for an incredible partnership and support.", "tokens": [50814, 1044, 291, 27950, 3532, 25170, 337, 364, 4651, 9982, 293, 1406, 13, 50964], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 651, "seek": 418200, "start": 4194.0, "end": 4198.0, "text": " You guys have been awesome with NVIDIA for over the years and of course for Microsoft.", "tokens": [50964, 509, 1074, 362, 668, 3476, 365, 426, 3958, 6914, 337, 670, 264, 924, 293, 295, 1164, 337, 8116, 13, 51164], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 652, "seek": 418200, "start": 4198.0, "end": 4201.0, "text": " So thank you <PERSON> and the entire team.", "tokens": [51164, 407, 1309, 291, 25550, 293, 264, 2302, 1469, 13, 51314], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 653, "seek": 418200, "start": 4201.0, "end": 4206.0, "text": " What we started off like a year ago, it's really like blossoming and this is just the beginning.", "tokens": [51314, 708, 321, 1409, 766, 411, 257, 1064, 2057, 11, 309, 311, 534, 411, 22956, 10539, 293, 341, 307, 445, 264, 2863, 13, 51564], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 654, "seek": 418200, "start": 4206.0, "end": 4208.0, "text": " We're going to grow even more.", "tokens": [51564, 492, 434, 516, 281, 1852, 754, 544, 13, 51664], "temperature": 0.0, "avg_logprob": -0.10923106853778546, "compression_ratio": 1.6027397260273972, "no_speech_prob": 0.009336556307971478}, {"id": 655, "seek": 420800, "start": 4208.0, "end": 4212.0, "text": " And <PERSON>, I hope you will invite us again with more exciting things.", "tokens": [50364, 400, 3977, 2039, 11, 286, 1454, 291, 486, 7980, 505, 797, 365, 544, 4670, 721, 13, 50564], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 656, "seek": 420800, "start": 4212.0, "end": 4216.0, "text": " Of course. I always like to ask this as a last round.", "tokens": [50564, 2720, 1164, 13, 286, 1009, 411, 281, 1029, 341, 382, 257, 1036, 3098, 13, 50764], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 657, "seek": 420800, "start": 4216.0, "end": 4218.0, "text": " <PERSON><PERSON><PERSON>, you just went. So you passed this.", "tokens": [50764, 460, 12779, 71, 11, 291, 445, 1437, 13, 407, 291, 4678, 341, 13, 50864], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 658, "seek": 420800, "start": 4218.0, "end": 4222.0, "text": " But does anyone have any last kind of words of wisdom that you want people to really remember?", "tokens": [50864, 583, 775, 2878, 362, 604, 1036, 733, 295, 2283, 295, 10712, 300, 291, 528, 561, 281, 534, 1604, 30, 51064], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 659, "seek": 420800, "start": 4222.0, "end": 4225.0, "text": " What's your key takeaway you want people to come away with?", "tokens": [51064, 708, 311, 428, 2141, 30681, 291, 528, 561, 281, 808, 1314, 365, 30, 51214], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 660, "seek": 420800, "start": 4225.0, "end": 4227.0, "text": " We'll start with, we'll go backwards actually.", "tokens": [51214, 492, 603, 722, 365, 11, 321, 603, 352, 12204, 767, 13, 51314], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 661, "seek": 420800, "start": 4227.0, "end": 4232.0, "text": " So <PERSON>, what's your key takeaway here if we want people to remember?", "tokens": [51314, 407, 25550, 11, 437, 311, 428, 2141, 30681, 510, 498, 321, 528, 561, 281, 1604, 30, 51564], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 662, "seek": 420800, "start": 4232.0, "end": 4237.0, "text": " Key takeaway. NVIDIA and Microsoft have a beautiful relationship.", "tokens": [51564, 12759, 30681, 13, 426, 3958, 6914, 293, 8116, 362, 257, 2238, 2480, 13, 51814], "temperature": 0.0, "avg_logprob": -0.1365617450914885, "compression_ratio": 1.7003367003367003, "no_speech_prob": 0.054745402187108994}, {"id": 663, "seek": 423700, "start": 4237.0, "end": 4244.0, "text": " And we're also trying to design industrial standards together as well.", "tokens": [50364, 400, 321, 434, 611, 1382, 281, 1715, 9987, 7787, 1214, 382, 731, 13, 50714], "temperature": 0.0, "avg_logprob": -0.1304652462266896, "compression_ratio": 1.4293193717277486, "no_speech_prob": 0.0030015374068170786}, {"id": 664, "seek": 423700, "start": 4244.0, "end": 4252.0, "text": " So just stay in contact with us and hopefully it gives you confidence to build whatever you all dream up.", "tokens": [50714, 407, 445, 1754, 294, 3385, 365, 505, 293, 4696, 309, 2709, 291, 6687, 281, 1322, 2035, 291, 439, 3055, 493, 13, 51114], "temperature": 0.0, "avg_logprob": -0.1304652462266896, "compression_ratio": 1.4293193717277486, "no_speech_prob": 0.0030015374068170786}, {"id": 665, "seek": 423700, "start": 4252.0, "end": 4254.0, "text": " So that's what my takeaway is.", "tokens": [51114, 407, 300, 311, 437, 452, 30681, 307, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1304652462266896, "compression_ratio": 1.4293193717277486, "no_speech_prob": 0.0030015374068170786}, {"id": 666, "seek": 423700, "start": 4254.0, "end": 4257.0, "text": " Amazing. Great words Drew. I'm glad you went.", "tokens": [51214, 14165, 13, 3769, 2283, 25550, 13, 286, 478, 5404, 291, 1437, 13, 51364], "temperature": 0.0, "avg_logprob": -0.1304652462266896, "compression_ratio": 1.4293193717277486, "no_speech_prob": 0.0030015374068170786}, {"id": 667, "seek": 423700, "start": 4257.0, "end": 4261.0, "text": " <PERSON><PERSON>, you're next.", "tokens": [51364, 11789, 84, 11, 291, 434, 958, 13, 51564], "temperature": 0.0, "avg_logprob": -0.1304652462266896, "compression_ratio": 1.4293193717277486, "no_speech_prob": 0.0030015374068170786}, {"id": 668, "seek": 426100, "start": 4262.0, "end": 4267.0, "text": " Oh, okay. Yeah. So I think the key takeaway I'd love for everyone to remember is, you know,", "tokens": [50414, 876, 11, 1392, 13, 865, 13, 407, 286, 519, 264, 2141, 30681, 286, 1116, 959, 337, 1518, 281, 1604, 307, 11, 291, 458, 11, 50664], "temperature": 0.0, "avg_logprob": -0.1538112380287864, "compression_ratio": 1.5305343511450382, "no_speech_prob": 0.009930304251611233}, {"id": 669, "seek": 426100, "start": 4267.0, "end": 4276.0, "text": " the domain specific intelligence and architecture that Sight Machine provides is critical for manufacturers to actually deliver real value.", "tokens": [50664, 264, 9274, 2685, 7599, 293, 9482, 300, 318, 397, 22155, 6417, 307, 4924, 337, 18455, 281, 767, 4239, 957, 2158, 13, 51114], "temperature": 0.0, "avg_logprob": -0.1538112380287864, "compression_ratio": 1.5305343511450382, "no_speech_prob": 0.009930304251611233}, {"id": 670, "seek": 426100, "start": 4276.0, "end": 4279.0, "text": " And that can be a great framework for other verticals as well, right?", "tokens": [51114, 400, 300, 393, 312, 257, 869, 8388, 337, 661, 9429, 82, 382, 731, 11, 558, 30, 51264], "temperature": 0.0, "avg_logprob": -0.1538112380287864, "compression_ratio": 1.5305343511450382, "no_speech_prob": 0.009930304251611233}, {"id": 671, "seek": 426100, "start": 4279.0, "end": 4285.0, "text": " Understanding the entire technology stack that's necessary for AI and data transformation, I think,", "tokens": [51264, 36858, 264, 2302, 2899, 8630, 300, 311, 4818, 337, 7318, 293, 1412, 9887, 11, 286, 519, 11, 51564], "temperature": 0.0, "avg_logprob": -0.1538112380287864, "compression_ratio": 1.5305343511450382, "no_speech_prob": 0.009930304251611233}, {"id": 672, "seek": 428500, "start": 4285.0, "end": 4287.0, "text": " is becoming much more fascinating.", "tokens": [50364, 307, 5617, 709, 544, 10343, 13, 50464], "temperature": 0.0, "avg_logprob": -0.1465116931546119, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.027857400476932526}, {"id": 673, "seek": 428500, "start": 4287.0, "end": 4296.0, "text": " But you know, understanding each layer and what we provide in addition to Microsoft and NVIDIA platforms would be a great thing to keep in mind.", "tokens": [50464, 583, 291, 458, 11, 3701, 1184, 4583, 293, 437, 321, 2893, 294, 4500, 281, 8116, 293, 426, 3958, 6914, 9473, 576, 312, 257, 869, 551, 281, 1066, 294, 1575, 13, 50914], "temperature": 0.0, "avg_logprob": -0.1465116931546119, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.027857400476932526}, {"id": 674, "seek": 428500, "start": 4296.0, "end": 4299.0, "text": " Great words, <PERSON><PERSON>. Thank you.", "tokens": [50914, 3769, 2283, 11, 11789, 84, 13, 1044, 291, 13, 51064], "temperature": 0.0, "avg_logprob": -0.1465116931546119, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.027857400476932526}, {"id": 675, "seek": 428500, "start": 4299.0, "end": 4301.0, "text": " <PERSON><PERSON><PERSON>, what's on your mind?", "tokens": [51064, 12323, 24118, 11, 437, 311, 322, 428, 1575, 30, 51164], "temperature": 0.0, "avg_logprob": -0.1465116931546119, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.027857400476932526}, {"id": 676, "seek": 428500, "start": 4301.0, "end": 4308.0, "text": " Yeah, I'll channel <PERSON> a little bit and say this, you know, sort of the two modes come to mind, right?", "tokens": [51164, 865, 11, 286, 603, 2269, 17809, 257, 707, 857, 293, 584, 341, 11, 291, 458, 11, 1333, 295, 264, 732, 14068, 808, 281, 1575, 11, 558, 30, 51514], "temperature": 0.0, "avg_logprob": -0.1465116931546119, "compression_ratio": 1.4556962025316456, "no_speech_prob": 0.027857400476932526}, {"id": 677, "seek": 430800, "start": 4308.0, "end": 4319.0, "text": " Like this partnership and what we've achieved showcases what can be done with the power of omniverse and Microsoft Azure and Fabric, right?", "tokens": [50364, 1743, 341, 9982, 293, 437, 321, 600, 11042, 29794, 1957, 437, 393, 312, 1096, 365, 264, 1347, 295, 36874, 5376, 293, 8116, 11969, 293, 17440, 1341, 11, 558, 30, 50914], "temperature": 0.0, "avg_logprob": -0.10948245496634977, "compression_ratio": 1.5707964601769913, "no_speech_prob": 0.18586935102939606}, {"id": 678, "seek": 430800, "start": 4319.0, "end": 4332.0, "text": " And if somebody who's like really wants to put on a developer hat and play with it and build these technologies, like this is the, you know, sort of blueprint on how it can be done and how it has actually been done.", "tokens": [50914, 400, 498, 2618, 567, 311, 411, 534, 2738, 281, 829, 322, 257, 10754, 2385, 293, 862, 365, 309, 293, 1322, 613, 7943, 11, 411, 341, 307, 264, 11, 291, 458, 11, 1333, 295, 35868, 322, 577, 309, 393, 312, 1096, 293, 577, 309, 575, 767, 668, 1096, 13, 51564], "temperature": 0.0, "avg_logprob": -0.10948245496634977, "compression_ratio": 1.5707964601769913, "no_speech_prob": 0.18586935102939606}, {"id": 679, "seek": 433200, "start": 4332.0, "end": 4344.0, "text": " That said, as <PERSON><PERSON> mentioned, a lot is based on data and the domain expertise and so on, and that's where Sight Machine comes in.", "tokens": [50364, 663, 848, 11, 382, 11789, 84, 2835, 11, 257, 688, 307, 2361, 322, 1412, 293, 264, 9274, 11769, 293, 370, 322, 11, 293, 300, 311, 689, 318, 397, 22155, 1487, 294, 13, 50964], "temperature": 0.0, "avg_logprob": -0.11576222849416208, "compression_ratio": 1.6334841628959276, "no_speech_prob": 0.07075092941522598}, {"id": 680, "seek": 433200, "start": 4344.0, "end": 4360.0, "text": " And so if you have looking for a solution that offers end-to-end with the data, with all the integrations in place and has done the heavy lifting for you, please feel free to contact us and we have the solution for you. Thank you.", "tokens": [50964, 400, 370, 498, 291, 362, 1237, 337, 257, 3827, 300, 7736, 917, 12, 1353, 12, 521, 365, 264, 1412, 11, 365, 439, 264, 3572, 763, 294, 1081, 293, 575, 1096, 264, 4676, 15798, 337, 291, 11, 1767, 841, 1737, 281, 3385, 505, 293, 321, 362, 264, 3827, 337, 291, 13, 1044, 291, 13, 51764], "temperature": 0.0, "avg_logprob": -0.11576222849416208, "compression_ratio": 1.6334841628959276, "no_speech_prob": 0.07075092941522598}, {"id": 681, "seek": 436000, "start": 4360.0, "end": 4365.0, "text": " Amazing. Okay, <PERSON>, you have the last word here.", "tokens": [50364, 14165, 13, 1033, 11, 17809, 11, 291, 362, 264, 1036, 1349, 510, 13, 50614], "temperature": 0.0, "avg_logprob": -0.1475331992433782, "compression_ratio": 1.2654320987654322, "no_speech_prob": 0.05816606432199478}, {"id": 682, "seek": 436000, "start": 4365.0, "end": 4367.0, "text": " I'm afraid.", "tokens": [50614, 286, 478, 4638, 13, 50714], "temperature": 0.0, "avg_logprob": -0.1475331992433782, "compression_ratio": 1.2654320987654322, "no_speech_prob": 0.05816606432199478}, {"id": 683, "seek": 436000, "start": 4367.0, "end": 4382.0, "text": " Yeah, well, so in manufacturing and supply chain, most people's operations run around 50% of the nameplate of what it was designed for, okay?", "tokens": [50714, 865, 11, 731, 11, 370, 294, 11096, 293, 5847, 5021, 11, 881, 561, 311, 7705, 1190, 926, 2625, 4, 295, 264, 1315, 37008, 295, 437, 309, 390, 4761, 337, 11, 1392, 30, 51464], "temperature": 0.0, "avg_logprob": -0.1475331992433782, "compression_ratio": 1.2654320987654322, "no_speech_prob": 0.05816606432199478}, {"id": 684, "seek": 438200, "start": 4382.0, "end": 4389.0, "text": " At every big company, these are billion-dollar problems and they're only going to be solved digitally.", "tokens": [50364, 1711, 633, 955, 2237, 11, 613, 366, 5218, 12, 40485, 2740, 293, 436, 434, 787, 516, 281, 312, 13041, 36938, 13, 50714], "temperature": 0.0, "avg_logprob": -0.12610026581646644, "compression_ratio": 1.4720812182741116, "no_speech_prob": 0.45631828904151917}, {"id": 685, "seek": 438200, "start": 4389.0, "end": 4396.0, "text": " So if you're looking at like, how much does this cost? Should I do it?", "tokens": [50714, 407, 498, 291, 434, 1237, 412, 411, 11, 577, 709, 775, 341, 2063, 30, 6454, 286, 360, 309, 30, 51064], "temperature": 0.0, "avg_logprob": -0.12610026581646644, "compression_ratio": 1.4720812182741116, "no_speech_prob": 0.45631828904151917}, {"id": 686, "seek": 438200, "start": 4396.0, "end": 4403.0, "text": " Yes, the answer is yes. It's 10 times faster and cheaper doing it digitally than going in and doing things manually.", "tokens": [51064, 1079, 11, 264, 1867, 307, 2086, 13, 467, 311, 1266, 1413, 4663, 293, 12284, 884, 309, 36938, 813, 516, 294, 293, 884, 721, 16945, 13, 51414], "temperature": 0.0, "avg_logprob": -0.12610026581646644, "compression_ratio": 1.4720812182741116, "no_speech_prob": 0.45631828904151917}, {"id": 687, "seek": 440300, "start": 4404.0, "end": 4412.0, "text": " So the biggest issue is not the cost. The biggest issue is not the technology. The biggest issue is a people problem, okay?", "tokens": [50414, 407, 264, 3880, 2734, 307, 406, 264, 2063, 13, 440, 3880, 2734, 307, 406, 264, 2899, 13, 440, 3880, 2734, 307, 257, 561, 1154, 11, 1392, 30, 50814], "temperature": 0.0, "avg_logprob": -0.10760035722152046, "compression_ratio": 1.7882882882882882, "no_speech_prob": 0.587227463722229}, {"id": 688, "seek": 440300, "start": 4412.0, "end": 4421.0, "text": " This is about multidisciplinary experts understanding all aspects of the problem. It's about having a vision.", "tokens": [50814, 639, 307, 466, 2120, 40920, 24560, 8572, 3701, 439, 7270, 295, 264, 1154, 13, 467, 311, 466, 1419, 257, 5201, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10760035722152046, "compression_ratio": 1.7882882882882882, "no_speech_prob": 0.587227463722229}, {"id": 689, "seek": 440300, "start": 4421.0, "end": 4430.0, "text": " I love that question. What do I do about IT and manufacturing and them being misaligned? Get them aligned, people. They have to be aligned to solve these problems.", "tokens": [51264, 286, 959, 300, 1168, 13, 708, 360, 286, 360, 466, 6783, 293, 11096, 293, 552, 885, 3346, 304, 16690, 30, 3240, 552, 17962, 11, 561, 13, 814, 362, 281, 312, 17962, 281, 5039, 613, 2740, 13, 51714], "temperature": 0.0, "avg_logprob": -0.10760035722152046, "compression_ratio": 1.7882882882882882, "no_speech_prob": 0.587227463722229}, {"id": 690, "seek": 443000, "start": 4430.0, "end": 4437.0, "text": " So much of what we're talking about here today, this is amazing. NVIDIA has done an amazing job of making sure that technology is available.", "tokens": [50364, 407, 709, 295, 437, 321, 434, 1417, 466, 510, 965, 11, 341, 307, 2243, 13, 426, 3958, 6914, 575, 1096, 364, 2243, 1691, 295, 1455, 988, 300, 2899, 307, 2435, 13, 50714], "temperature": 0.0, "avg_logprob": -0.1038547158241272, "compression_ratio": 1.6237623762376239, "no_speech_prob": 0.015649575740098953}, {"id": 691, "seek": 443000, "start": 4437.0, "end": 4443.0, "text": " Fantastic. Developers and ISVs like Sight Machine have made sure that they use that technology and put it into products.", "tokens": [50714, 21320, 13, 11442, 433, 293, 6205, 53, 82, 411, 318, 397, 22155, 362, 1027, 988, 300, 436, 764, 300, 2899, 293, 829, 309, 666, 3383, 13, 51014], "temperature": 0.0, "avg_logprob": -0.1038547158241272, "compression_ratio": 1.6237623762376239, "no_speech_prob": 0.015649575740098953}, {"id": 692, "seek": 443000, "start": 4443.0, "end": 4447.0, "text": " And then, you know, Kinetic Vision is helping customers solve problems every day.", "tokens": [51014, 400, 550, 11, 291, 458, 11, 27950, 3532, 25170, 307, 4315, 4581, 5039, 2740, 633, 786, 13, 51214], "temperature": 0.0, "avg_logprob": -0.1038547158241272, "compression_ratio": 1.6237623762376239, "no_speech_prob": 0.015649575740098953}, {"id": 693, "seek": 443000, "start": 4447.0, "end": 4456.0, "text": " But what I'm seeing on the front lines is people issues and companies really coming to grips with how to solve these problems in their organization.", "tokens": [51214, 583, 437, 286, 478, 2577, 322, 264, 1868, 3876, 307, 561, 2663, 293, 3431, 534, 1348, 281, 38037, 365, 577, 281, 5039, 613, 2740, 294, 641, 4475, 13, 51664], "temperature": 0.0, "avg_logprob": -0.1038547158241272, "compression_ratio": 1.6237623762376239, "no_speech_prob": 0.015649575740098953}, {"id": 694, "seek": 445600, "start": 4456.0, "end": 4461.0, "text": " So try to build those relationships. People get them in front of the right stakeholders.", "tokens": [50364, 407, 853, 281, 1322, 729, 6159, 13, 3432, 483, 552, 294, 1868, 295, 264, 558, 17779, 13, 50614], "temperature": 0.0, "avg_logprob": -0.10098517792565483, "compression_ratio": 1.6890459363957597, "no_speech_prob": 0.012845943681895733}, {"id": 695, "seek": 445600, "start": 4461.0, "end": 4467.0, "text": " Make sure that you're supporting, you know, your visionary leaders, making sure your visionary leaders are working with their visionary developers.", "tokens": [50614, 4387, 988, 300, 291, 434, 7231, 11, 291, 458, 11, 428, 49442, 3523, 11, 1455, 988, 428, 49442, 3523, 366, 1364, 365, 641, 49442, 8849, 13, 50914], "temperature": 0.0, "avg_logprob": -0.10098517792565483, "compression_ratio": 1.6890459363957597, "no_speech_prob": 0.012845943681895733}, {"id": 696, "seek": 445600, "start": 4467.0, "end": 4474.0, "text": " It's a big people challenge, but I know that we'll be able to get there. So thanks again.", "tokens": [50914, 467, 311, 257, 955, 561, 3430, 11, 457, 286, 458, 300, 321, 603, 312, 1075, 281, 483, 456, 13, 407, 3231, 797, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10098517792565483, "compression_ratio": 1.6890459363957597, "no_speech_prob": 0.012845943681895733}, {"id": 697, "seek": 445600, "start": 4474.0, "end": 4480.0, "text": " That's awesome. I'm so glad. That was a great closing remarks. I want to thank all of our amazing guests from Sight Machine, Microsoft, Kinetic Vision,", "tokens": [51264, 663, 311, 3476, 13, 286, 478, 370, 5404, 13, 663, 390, 257, 869, 10377, 19151, 13, 286, 528, 281, 1309, 439, 295, 527, 2243, 9804, 490, 318, 397, 22155, 11, 8116, 11, 27950, 3532, 25170, 11, 51564], "temperature": 0.0, "avg_logprob": -0.10098517792565483, "compression_ratio": 1.6890459363957597, "no_speech_prob": 0.012845943681895733}, {"id": 698, "seek": 448000, "start": 4480.0, "end": 4486.0, "text": " and of course, my colleagues here at NVIDIA for sharing your amazing insight. Each one of you has been extremely helpful.", "tokens": [50364, 293, 295, 1164, 11, 452, 7734, 510, 412, 426, 3958, 6914, 337, 5414, 428, 2243, 11269, 13, 6947, 472, 295, 291, 575, 668, 4664, 4961, 13, 50664], "temperature": 0.0, "avg_logprob": -0.10330896307952213, "compression_ratio": 1.5583333333333333, "no_speech_prob": 0.7190446257591248}, {"id": 699, "seek": 448000, "start": 4486.0, "end": 4493.0, "text": " We've seen how Agentec AI, OpenUSD, and Jill Twins can help factory teams spot issues faster and optimize production lines.", "tokens": [50664, 492, 600, 1612, 577, 2725, 1576, 66, 7318, 11, 7238, 3447, 35, 11, 293, 24690, 2574, 1292, 393, 854, 9265, 5491, 4008, 2663, 4663, 293, 19719, 4265, 3876, 13, 51014], "temperature": 0.0, "avg_logprob": -0.10330896307952213, "compression_ratio": 1.5583333333333333, "no_speech_prob": 0.7190446257591248}, {"id": 700, "seek": 448000, "start": 4493.0, "end": 4498.0, "text": " And it seems like this is just the beginning of what's possible. So a big thanks to all of you who joined us live out there.", "tokens": [51014, 400, 309, 2544, 411, 341, 307, 445, 264, 2863, 295, 437, 311, 1944, 13, 407, 257, 955, 3231, 281, 439, 295, 291, 567, 6869, 505, 1621, 484, 456, 13, 51264], "temperature": 0.0, "avg_logprob": -0.10330896307952213, "compression_ratio": 1.5583333333333333, "no_speech_prob": 0.7190446257591248}, {"id": 701, "seek": 448000, "start": 4498.0, "end": 4501.0, "text": " Great comments and questions. We really love the projects that we shared.", "tokens": [51264, 3769, 3053, 293, 1651, 13, 492, 534, 959, 264, 4455, 300, 321, 5507, 13, 51414], "temperature": 0.0, "avg_logprob": -0.10330896307952213, "compression_ratio": 1.5583333333333333, "no_speech_prob": 0.7190446257591248}, {"id": 702, "seek": 448000, "start": 4501.0, "end": 4506.0, "text": " If you missed part of today's session, don't worry. The replay is available using the same link you're at right now.", "tokens": [51414, 759, 291, 6721, 644, 295, 965, 311, 5481, 11, 500, 380, 3292, 13, 440, 23836, 307, 2435, 1228, 264, 912, 2113, 291, 434, 412, 558, 586, 13, 51664], "temperature": 0.0, "avg_logprob": -0.10330896307952213, "compression_ratio": 1.5583333333333333, "no_speech_prob": 0.7190446257591248}, {"id": 703, "seek": 450600, "start": 4506.0, "end": 4510.0, "text": " Or just go to our YouTube channel anytime, NVIDIA Omniverse. Go to live and you'll find it there.", "tokens": [50364, 1610, 445, 352, 281, 527, 3088, 2269, 13038, 11, 426, 3958, 6914, 9757, 77, 5376, 13, 1037, 281, 1621, 293, 291, 603, 915, 309, 456, 13, 50564], "temperature": 0.0, "avg_logprob": -0.12161656935437978, "compression_ratio": 1.6139817629179332, "no_speech_prob": 0.5948688387870789}, {"id": 704, "seek": 450600, "start": 4510.0, "end": 4515.0, "text": " And as I'm showing on the screen now, this kind of experts presentation is valuable to you.", "tokens": [50564, 400, 382, 286, 478, 4099, 322, 264, 2568, 586, 11, 341, 733, 295, 8572, 5860, 307, 8263, 281, 291, 13, 50814], "temperature": 0.0, "avg_logprob": -0.12161656935437978, "compression_ratio": 1.6139817629179332, "no_speech_prob": 0.5948688387870789}, {"id": 705, "seek": 450600, "start": 4515.0, "end": 4522.0, "text": " I highly encourage you if you can make it to GTC DC. So our first one, Washington DC, you're going to see <PERSON> there providing a great keynote.", "tokens": [50814, 286, 5405, 5373, 291, 498, 291, 393, 652, 309, 281, 17530, 34, 9114, 13, 407, 527, 700, 472, 11, 6149, 9114, 11, 291, 434, 516, 281, 536, 508, 32934, 456, 6530, 257, 869, 33896, 13, 51164], "temperature": 0.0, "avg_logprob": -0.12161656935437978, "compression_ratio": 1.6139817629179332, "no_speech_prob": 0.5948688387870789}, {"id": 706, "seek": 450600, "start": 4522.0, "end": 4528.0, "text": " So I hope everyone there can make it. I will be there. So let me know if you're going and we can grab a coffee or something together.", "tokens": [51164, 407, 286, 1454, 1518, 456, 393, 652, 309, 13, 286, 486, 312, 456, 13, 407, 718, 385, 458, 498, 291, 434, 516, 293, 321, 393, 4444, 257, 4982, 420, 746, 1214, 13, 51464], "temperature": 0.0, "avg_logprob": -0.12161656935437978, "compression_ratio": 1.6139817629179332, "no_speech_prob": 0.5948688387870789}, {"id": 707, "seek": 450600, "start": 4528.0, "end": 4531.0, "text": " Until next time, thank you so much for joining us, everybody.", "tokens": [51464, 9088, 958, 565, 11, 1309, 291, 370, 709, 337, 5549, 505, 11, 2201, 13, 51614], "temperature": 0.0, "avg_logprob": -0.12161656935437978, "compression_ratio": 1.6139817629179332, "no_speech_prob": 0.5948688387870789}, {"id": 708, "seek": 453100, "start": 4531.0, "end": 4536.0, "text": " It's been an absolute honor to join the panel with you all today and with you all there in the audience.", "tokens": [50364, 467, 311, 668, 364, 8236, 5968, 281, 3917, 264, 4831, 365, 291, 439, 965, 293, 365, 291, 439, 456, 294, 264, 4034, 13, 50614], "temperature": 0.0, "avg_logprob": -0.09999050543858455, "compression_ratio": 1.4453781512605042, "no_speech_prob": 0.2871408462524414}, {"id": 709, "seek": 453100, "start": 4536.0, "end": 4538.0, "text": " Have a great rest of the day.", "tokens": [50614, 3560, 257, 869, 1472, 295, 264, 786, 13, 50714], "temperature": 0.0, "avg_logprob": -0.09999050543858455, "compression_ratio": 1.4453781512605042, "no_speech_prob": 0.2871408462524414}, {"id": 710, "seek": 453100, "start": 4538.0, "end": 4539.0, "text": " Thank you so much.", "tokens": [50714, 1044, 291, 370, 709, 13, 50764], "temperature": 0.0, "avg_logprob": -0.09999050543858455, "compression_ratio": 1.4453781512605042, "no_speech_prob": 0.2871408462524414}, {"id": 711, "seek": 453100, "start": 4539.0, "end": 4540.0, "text": " Thanks.", "tokens": [50764, 2561, 13, 50814], "temperature": 0.0, "avg_logprob": -0.09999050543858455, "compression_ratio": 1.4453781512605042, "no_speech_prob": 0.2871408462524414}, {"id": 712, "seek": 453100, "start": 4540.0, "end": 4542.0, "text": " Thank you.", "tokens": [50814, 1044, 291, 13, 50914], "temperature": 0.0, "avg_logprob": -0.09999050543858455, "compression_ratio": 1.4453781512605042, "no_speech_prob": 0.2871408462524414}], "language": "en"}